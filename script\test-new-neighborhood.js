// Test script to create property with new neighborhood JSON format
const axios = require('axios');

const API_BASE = 'http://localhost:1337/api';

// Existing user 'badr' login credentials
const testUser = {
  email: '<EMAIL>',
  password: 'Mb123321'
};

async function testNewNeighborhoodFormat() {
  try {
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/local`, {
      identifier: testUser.email,
      password: testUser.password
    });
    
    const userId = loginResponse.data.user.id;
    const userToken = loginResponse.data.jwt;
    console.log(`✅ Logged in as user ID: ${userId}`);
    
    // Test with new neighborhood JSON format
    console.log('\n🏠 Creating property with new neighborhood JSON format...');
    const propertyData = {
      title: 'Test Google Maps Neighborhoods',
      description: 'Testing new neighborhood JSON format with Google Maps integration',
      price: 250000,
      propertyType: 'apartment',
      area: 1200,
      address: '123 Main Street',
      city: 'New York',
      country: 'USA',
      neighborhood: [
        {
          name: 'Manhattan',
          type: 'neighborhood',
          formatted_address: 'Manhattan, New York, NY, USA',
          place_id: 'ChIJYeZuBI9YwokRjMDs_IEyCwo'
        },
        {
          name: 'Upper East Side',
          type: 'sublocality',
          formatted_address: 'Upper East Side, New York, NY, USA',
          place_id: 'ChIJYeZuBI9YwokRjMDs_IEyCwo'
        }
      ]
    };
    
    try {
      const response = await axios.post(`${API_BASE}/properties`, {
        data: propertyData
      }, {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Property with new neighborhood format created successfully');
      console.log('Property ID:', response.data.data.id);
      console.log('Neighborhood data:', JSON.stringify(response.data.data.neighborhood, null, 2));
      
      // Test retrieving the property
      console.log('\n📖 Retrieving property to verify neighborhood data...');
      const getResponse = await axios.get(`${API_BASE}/properties/${response.data.data.id}`, {
        headers: {
          Authorization: `Bearer ${userToken}`
        }
      });
      
      console.log('✅ Property retrieved successfully');
      console.log('Retrieved neighborhood data:', JSON.stringify(getResponse.data.data.neighborhood, null, 2));
      
    } catch (error) {
      console.error('❌ Property creation failed:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
  }
}

// Run the test
testNewNeighborhoodFormat();
