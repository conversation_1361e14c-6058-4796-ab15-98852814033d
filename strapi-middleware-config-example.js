// Example Strapi middleware configuration for Chartbrew integration
// Add this to your config/middlewares.js file

module.exports = [
  'strapi::logger',
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': ["'self'", 'http:', 'https:', 'http://localhost:4018', 'http://localhost:4019'],
          'frame-src': ["'self'", 'http://localhost:4018', 'http://localhost:4019'],
          'img-src': ["'self'", 'data:', 'blob:', 'http:', 'https:'],
          upgradeInsecureRequests: null,
        },
      },
    },
  },
  {
    name: 'strapi::cors',
    config: {
      enabled: true,
      headers: '*',
      origin: [
        'http://localhost:1337',
        'http://localhost:3000', 
        'http://localhost:4018', // Chartbrew frontend
        'http://localhost:4019', // Chartbrew API
        // Add your production domains here
      ]
    }
  },
  'strapi::poweredBy',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
