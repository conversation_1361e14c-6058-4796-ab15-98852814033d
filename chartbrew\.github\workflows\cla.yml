name: "CLA Assistant"
on:
  issue_comment:
    types: [created]
  pull_request_target:
    types: [opened,closed,synchronize]

permissions:
  actions: write
  contents: write
  pull-requests: write
  statuses: write

jobs:
  CLAAssistant:
    runs-on: ubuntu-latest
    steps:
      - name: "CLA Assistant"
        if: (github.event.comment.body == 'recheck' || github.event.comment.body == 'I have read the CLA Document and I hereby sign the CLA') || github.event_name == 'pull_request_target'
        uses: contributor-assistant/github-action@v2.6.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
        with:
          path-to-signatures: 'contributors/cla.json'
          path-to-document: 'https://github.com/chartbrew/chartbrew/blob/master/CLA.md'
          branch: 'master'
          allowlist: r<PERSON><PERSON><PERSON>

          create-file-commit-message: ':memo: Create file for storing CLA Signatures'
          signed-commit-message: ':busts_in_silhouette: $<PERSON><PERSON><PERSON> has signed the CLA'
          custom-allsigned-prcomment: 'All Contributors have signed the CLA.'
