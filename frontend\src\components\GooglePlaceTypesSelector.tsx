'use client';

import React, { useState, useEffect } from 'react';
import { Check, X, Search, Info } from 'lucide-react';

interface GooglePlaceType {
  [key: string]: string;
}

interface PlaceTypeCategory {
  name: string;
  types: string[];
}

interface PlaceTypeCategories {
  [key: string]: PlaceTypeCategory;
}

interface GooglePlaceTypesSelectorProps {
  selectedTypes: string[];
  onChange: (types: string[]) => void;
  maxSelections?: number;
  className?: string;
}

export const GooglePlaceTypesSelector: React.FC<GooglePlaceTypesSelectorProps> = ({
  selectedTypes,
  onChange,
  maxSelections = 10,
  className = ''
}) => {
  const [availableTypes, setAvailableTypes] = useState<GooglePlaceType>({});
  const [categories, setCategories] = useState<PlaceTypeCategories>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    fetchGooglePlaceTypes();
  }, []);

  const fetchGooglePlaceTypes = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/api/nearby-place-categories/google-place-types`);
      
      if (response.ok) {
        const data = await response.json();
        setAvailableTypes(data.data.types);
        setCategories(data.data.categories);
      } else {
        setError('Failed to load Google Place Types');
      }
    } catch (err) {
      setError('Failed to connect to server');
    } finally {
      setLoading(false);
    }
  };

  const handleTypeToggle = (type: string) => {
    const isSelected = selectedTypes.includes(type);
    
    if (isSelected) {
      // Remove type
      onChange(selectedTypes.filter(t => t !== type));
    } else {
      // Add type (check max limit)
      if (selectedTypes.length < maxSelections) {
        onChange([...selectedTypes, type]);
      }
    }
  };

  const handleCategorySelect = (categoryKey: string) => {
    if (categoryKey === 'all') {
      setSelectedCategory('all');
      return;
    }

    const category = categories[categoryKey];
    if (!category) return;

    // Add all types from this category (respecting max limit)
    const newTypes = [...selectedTypes];
    category.types.forEach(type => {
      if (!newTypes.includes(type) && newTypes.length < maxSelections) {
        newTypes.push(type);
      }
    });
    
    onChange(newTypes);
    setSelectedCategory(categoryKey);
  };

  const getFilteredTypes = () => {
    const allTypes = Object.entries(availableTypes);
    
    let filtered = allTypes;
    
    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(([key, value]) => 
        key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        value.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Filter by category
    if (selectedCategory !== 'all') {
      const categoryTypes = categories[selectedCategory]?.types || [];
      filtered = filtered.filter(([key, value]) => categoryTypes.includes(value));
    }
    
    return filtered;
  };

  const formatTypeName = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  if (loading) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-8 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 bg-red-50 border border-red-200 rounded-md ${className}`}>
        <div className="flex items-center space-x-2 text-red-600">
          <X className="h-4 w-4" />
          <span className="text-sm">{error}</span>
        </div>
        <button
          onClick={fetchGooglePlaceTypes}
          className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  const filteredTypes = getFilteredTypes();

  return (
    <div className={className}>
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Google Place Types
          <span className="text-gray-500 ml-1">({selectedTypes.length}/{maxSelections})</span>
        </label>
        
        {/* Info */}
        <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-start space-x-2">
            <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Select accurate Google Place Types</p>
              <p>These are the exact values used by Google Places API. Choose types that match your category.</p>
            </div>
          </div>
        </div>

        {/* Search and Category Filter */}
        <div className="flex space-x-2 mb-3">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search place types..."
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          >
            <option value="all">All Categories</option>
            {Object.entries(categories).map(([key, category]) => (
              <option key={key} value={key}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        {/* Quick Category Selection */}
        <div className="mb-3">
          <p className="text-xs text-gray-600 mb-2">Quick select by category:</p>
          <div className="flex flex-wrap gap-1">
            {Object.entries(categories).map(([key, category]) => (
              <button
                key={key}
                onClick={() => handleCategorySelect(key)}
                className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded border text-gray-700"
              >
                Add {category.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Selected Types */}
      {selectedTypes.length > 0 && (
        <div className="mb-4">
          <p className="text-sm font-medium text-gray-700 mb-2">Selected Types:</p>
          <div className="flex flex-wrap gap-1">
            {selectedTypes.map(type => (
              <span
                key={type}
                className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
              >
                {formatTypeName(type)}
                <button
                  onClick={() => handleTypeToggle(type)}
                  className="ml-1 hover:text-blue-600"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Available Types */}
      <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-md">
        {filteredTypes.length === 0 ? (
          <div className="p-4 text-center text-gray-500 text-sm">
            No place types found matching your criteria
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredTypes.map(([key, value]) => {
              const isSelected = selectedTypes.includes(value);
              const isDisabled = !isSelected && selectedTypes.length >= maxSelections;
              
              return (
                <button
                  key={key}
                  onClick={() => handleTypeToggle(value)}
                  disabled={isDisabled}
                  className={`w-full px-3 py-2 text-left hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed ${
                    isSelected ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatTypeName(value)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {value}
                      </div>
                    </div>
                    {isSelected && (
                      <Check className="h-4 w-4 text-blue-600" />
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};
