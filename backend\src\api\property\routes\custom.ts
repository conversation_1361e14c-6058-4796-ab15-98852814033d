/**
 * Custom property routes - consolidated
 */

export default {
  routes: [
    // User properties management
    {
      method: 'GET',
      path: '/properties/my-properties',
      handler: 'property.getMyProperties',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/properties/featured',
      handler: 'property.getFeatured',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/properties/:id/edit',
      handler: 'property.getForEdit',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    // Publishing controls
    {
      method: 'PUT',
      path: '/properties/:id/publish',
      handler: 'property.publish',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'PUT',
      path: '/properties/:id/unpublish',
      handler: 'property.unpublish',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    // Image management
    {
      method: 'PUT',
      path: '/properties/:id/image-order',
      handler: 'property.updateImageOrder',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    // Nearby places functionality
    {
      method: 'POST',
      path: '/properties/nearby-places',
      handler: 'property.findNearbyPlaces',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/properties/:id/generate-nearby-places',
      handler: 'property.generateNearbyPlaces',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/properties/:id/nearby-places',
      handler: 'property.getNearbyPlaces',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    // Slug-based property lookup
    {
      method: 'GET',
      path: '/properties/by-slug/:slug',
      handler: 'property.findBySlug',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    // Chartbrew data endpoint for analytics integration
    {
      method: 'GET',
      path: '/properties/chartbrew-data',
      handler: 'property.getChartbrewData',
      config: {
        policies: [],
        middlewares: [],
      },
    },

  ],
};
