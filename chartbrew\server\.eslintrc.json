{"extends": "airbnb-base", "rules": {"quotes": [1, "double"], "no-plusplus": "off", "no-underscore-dangle": "off", "no-shadow": "off", "comma-dangle": ["error", {"arrays": "only-multiline", "objects": "only-multiline", "imports": "only-multiline", "exports": "only-multiline", "functions": "ignore"}], "arrow-body-style": "off", "no-else-return": "off", "quote-props": "off", "no-restricted-syntax": "off", "linebreak-style": "off", "class-methods-use-this": "off", "prefer-promise-reject-errors": "off", "camelcase": "off", "import/extensions": "off", "no-template-curly-in-string": "off", "no-promise-executor-return": "off", "function-paren-newline": "off", "default-param-last": "off"}, "parser": "@babel/eslint-parser", "parserOptions": {"requireConfigFile": false, "babelOptions": {"babelrc": false, "configFile": false}}, "globals": {"Headers": false}}