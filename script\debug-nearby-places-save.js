/**
 * Debug script to test why nearby places data is not being saved
 */

const axios = require('axios');

async function debugNearbyPlacesSave() {
  console.log('🔍 Debugging Nearby Places Save Issue...\n');

  const API_BASE = 'http://localhost:1337/api';

  try {
    // Step 1: Check if we can find a property with coordinates
    console.log('1. Finding a property with coordinates...');
    const propertiesResponse = await axios.get(`${API_BASE}/properties?pagination[limit]=10`);
    const properties = propertiesResponse.data.data;
    
    const propertyWithCoords = properties.find(p => p.coordinates);
    
    if (!propertyWithCoords) {
      console.log('❌ No properties with coordinates found');
      return;
    }

    console.log(`✅ Found property: ${propertyWithCoords.title}`);
    console.log(`   ID: ${propertyWithCoords.id}`);
    console.log(`   DocumentID: ${propertyWithCoords.documentId}`);
    console.log(`   Coordinates: ${propertyWithCoords.coordinates.lat}, ${propertyWithCoords.coordinates.lng}`);
    console.log(`   Current nearbyPlaces: ${propertyWithCoords.nearbyPlaces ? 'EXISTS' : 'NULL'}`);

    // Step 2: Check if categories are available
    console.log('\n2. Checking nearby place categories...');
    const categoriesResponse = await axios.get(`${API_BASE}/nearby-place-categories/enabled`);
    const categories = categoriesResponse.data.data;
    
    console.log(`✅ Found ${categories.length} enabled categories:`);
    categories.forEach(cat => {
      console.log(`   - ${cat.displayName}: ${cat.googlePlaceTypes.length} place types`);
    });

    // Step 3: Test the Google Places API directly
    console.log('\n3. Testing Google Places API directly...');
    const testCategory = categories[0];
    const testCoords = propertyWithCoords.coordinates;
    
    const placesResponse = await axios.post(`${API_BASE}/properties/nearby-places`, {
      lat: testCoords.lat,
      lng: testCoords.lng,
      types: testCategory.googlePlaceTypes,
      radius: testCategory.searchRadius || 1500,
      maxResults: testCategory.maxResults || 5
    });

    console.log(`✅ Google Places API working: ${placesResponse.data.data.length} places found`);

    // Step 4: Test the generate-nearby-places endpoint (this requires authentication)
    console.log('\n4. Testing generate-nearby-places endpoint...');
    
    // First, let's try without authentication to see the error
    try {
      const generateResponse = await axios.post(
        `${API_BASE}/properties/${propertyWithCoords.id}/generate-nearby-places`,
        {}
      );
      console.log('✅ Generate endpoint response:', generateResponse.data);
    } catch (generateError) {
      console.log('❌ Generate endpoint error:', generateError.response?.status, generateError.response?.data);
      
      if (generateError.response?.status === 401) {
        console.log('🔑 Authentication required for generate-nearby-places endpoint');
        console.log('   This is likely why the data is not being saved!');
      }
    }

    // Step 5: Check the property schema to ensure nearbyPlaces field exists
    console.log('\n5. Checking property schema...');
    try {
      // Try to manually update a property to test if nearbyPlaces field works
      const testData = {
        nearbyPlaces: {
          test: {
            category: { name: 'test', displayName: 'Test' },
            places: [{ name: 'Test Place', vicinity: 'Test Location' }]
          }
        }
      };

      const updateResponse = await axios.put(
        `${API_BASE}/properties/${propertyWithCoords.id}`,
        { data: testData }
      );
      
      console.log('✅ Manual property update successful');
      
      // Check if the data was actually saved
      const checkResponse = await axios.get(`${API_BASE}/properties/${propertyWithCoords.id}`);
      const savedNearbyPlaces = checkResponse.data.data.nearbyPlaces;
      
      if (savedNearbyPlaces && savedNearbyPlaces.test) {
        console.log('✅ nearbyPlaces field is working - data was saved and retrieved');
      } else {
        console.log('❌ nearbyPlaces field issue - data was not saved properly');
      }
      
    } catch (updateError) {
      console.log('❌ Manual update failed:', updateError.response?.status, updateError.response?.data);
    }

    // Step 6: Check the backend logs
    console.log('\n6. Recommendations:');
    console.log('   1. Check if authentication is working for the generate endpoint');
    console.log('   2. Check backend console logs for any errors during save');
    console.log('   3. Verify the property ID being used in the frontend matches the backend');
    console.log('   4. Check if there are any validation errors in the nearbyPlaces field');

    // Step 7: Test with a different property ID format
    console.log('\n7. Testing with different ID formats...');
    
    // Test with documentId
    console.log(`   Testing with documentId: ${propertyWithCoords.documentId}`);
    try {
      const docIdResponse = await axios.get(`${API_BASE}/properties/${propertyWithCoords.documentId}`);
      console.log('   ✅ DocumentId access works');
    } catch (docIdError) {
      console.log('   ❌ DocumentId access failed:', docIdError.response?.status);
    }

    // Test with numeric ID
    console.log(`   Testing with numeric ID: ${propertyWithCoords.id}`);
    try {
      const numIdResponse = await axios.get(`${API_BASE}/properties/${propertyWithCoords.id}`);
      console.log('   ✅ Numeric ID access works');
    } catch (numIdError) {
      console.log('   ❌ Numeric ID access failed:', numIdError.response?.status);
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.response?.data || error.message);
  }
}

debugNearbyPlacesSave();
