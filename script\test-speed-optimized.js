// Quick test to demonstrate speed improvements
const axios = require('axios');

const API_URL = 'http://localhost:1337/api';

async function testSpeedOptimizations() {
  console.log('🚀 Testing Speed Optimizations\n');
  
  try {
    // Login
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    const token = loginResponse.data.jwt;
    console.log('✅ Login successful\n');

    // Test 1: Sequential (old way)
    console.log('📊 Test 1: Sequential Processing (Old Way)');
    const startSequential = Date.now();
    
    for (let i = 0; i < 3; i++) {
      const response = await axios.post(`${API_URL}/properties`, {
        data: {
          title: `Sequential Test Property ${i + 1}`,
          description: 'Test property for speed comparison',
          price: 100000,
          currency: 'USD',
          propertyType: 'apartment',
          offer: 'for-sale',
          bedrooms: 2,
          bathrooms: 1,
          area: 80,
          areaUnit: 'sqm',
          address: 'Test Address',
          city: 'Sharm El Sheikh',
          country: 'Egypt',
          neighborhood: 'Test Area',
          coordinates: { lat: 27.9158, lng: 34.3300 },
          propertyCode: `SEQ-${Date.now()}-${i}`,
          features: ['wifi'],
          furnished: false,
          petFriendly: false,
          views: 0
        }
      }, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log(`   ✅ Created property ${i + 1}: ID ${response.data.data.id}`);
      
      // Old delay
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    const sequentialTime = Date.now() - startSequential;
    console.log(`   ⏱️  Sequential time: ${sequentialTime}ms\n`);

    // Test 2: Concurrent (new way)
    console.log('📊 Test 2: Concurrent Processing (New Way)');
    const startConcurrent = Date.now();
    
    const concurrentPromises = [];
    for (let i = 0; i < 3; i++) {
      concurrentPromises.push(
        axios.post(`${API_URL}/properties`, {
          data: {
            title: `Concurrent Test Property ${i + 1}`,
            description: 'Test property for speed comparison',
            price: 100000,
            currency: 'USD',
            propertyType: 'apartment',
            offer: 'for-sale',
            bedrooms: 2,
            bathrooms: 1,
            area: 80,
            areaUnit: 'sqm',
            address: 'Test Address',
            city: 'Sharm El Sheikh',
            country: 'Egypt',
            neighborhood: 'Test Area',
            coordinates: { lat: 27.9158, lng: 34.3300 },
            propertyCode: `CON-${Date.now()}-${i}`,
            features: ['wifi'],
            furnished: false,
            petFriendly: false,
            views: 0
          }
        }, {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      );
    }
    
    const concurrentResults = await Promise.all(concurrentPromises);
    concurrentResults.forEach((response, i) => {
      console.log(`   ✅ Created property ${i + 1}: ID ${response.data.data.id}`);
    });
    
    // New shorter delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const concurrentTime = Date.now() - startConcurrent;
    console.log(`   ⏱️  Concurrent time: ${concurrentTime}ms\n`);

    // Results
    const speedup = (sequentialTime / concurrentTime).toFixed(1);
    console.log('🎯 SPEED COMPARISON RESULTS:');
    console.log('═'.repeat(40));
    console.log(`📈 Sequential (old): ${sequentialTime}ms`);
    console.log(`⚡ Concurrent (new): ${concurrentTime}ms`);
    console.log(`🚀 Speed improvement: ${speedup}x faster`);
    console.log(`💡 For 1000 properties: ~${(sequentialTime * 1000 / 3 / 1000 / 60).toFixed(1)} min → ~${(concurrentTime * 1000 / 3 / 1000 / 60).toFixed(1)} min`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSpeedOptimizations();
