# 🏠 Real Estate Platform

A comprehensive real estate web application built with modern technologies, featuring property management, user dashboards, advanced search capabilities, and Google Maps integration.

## 📋 Table of Contents

- [Features](#-features)
- [Tech Stack](#-tech-stack)
- [Architecture](#-architecture)
- [Getting Started](#-getting-started)
- [Project Structure](#-project-structure)
- [API Documentation](#-api-documentation)
- [User Roles & Permissions](#-user-roles--permissions)
- [Key Functionalities](#-key-functionalities)
- [Environment Setup](#-environment-setup)
- [Development Workflow](#-development-workflow)
- [Testing](#-testing)
- [Deployment](#-deployment)
- [Contributing](#-contributing)

## ✨ Features

### 🏡 Property Management
- **Property Listings**: Comprehensive property database with detailed information
- **Advanced Search & Filtering**: Search by location, price, type, features, and more
- **Property Submission**: User-friendly form for property submissions
- **Image & Floor Plan Upload**: Multi-file upload with preview functionality
- **Property Status Management**: Draft, published, sold, rented status tracking
- **Property Actions**: View, edit, publish, delete with three-dots menu interface

### 👥 User Management
- **Multi-Role System**: Admin, User, and Agent roles with different permissions
- **User Authentication**: Secure login/register with JWT tokens
- **User Dashboard**: Personalized dashboard for property management
- **Membership System**: Property submission limits based on membership tiers
- **Profile Management**: User profile editing and management

### 🗺️ Location & Maps
- **Google Maps Integration**: Interactive maps for property locations
- **Neighborhood System**: Multiple neighborhood selection with auto-detection
- **Nearby Places**: Yelp-style nearby places with categories (Education, Restaurants, etc.)
- **Coordinate Selection**: Map-based location picking with preview
- **Location-Based Search**: Find properties by area and proximity

### 📱 User Interface
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Modern UI/UX**: Clean, intuitive interface design
- **Dual View Modes**: List and grid views for property browsing
- **Interactive Components**: Sliders, filters, modals, and dynamic forms
- **Real-time Updates**: Live search and filtering capabilities

### 🔧 Admin Features
- **Admin Dashboard**: Comprehensive admin panel for system management
- **Content Management**: Manage all properties and users
- **Analytics Dashboard**: Property statistics and user activity tracking
- **Bulk Operations**: Mass property management capabilities
- **System Configuration**: Nearby place categories and system settings

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 15.3.4 with App Router
- **Language**: TypeScript 5.x
- **Styling**: Tailwind CSS 3.x
- **UI Components**: Custom components with Headless UI
- **State Management**: React Context API
- **HTTP Client**: Axios for API communication
- **Maps**: Google Maps JavaScript API
- **Icons**: Lucide React icons

### Backend
- **CMS**: Strapi v5.16.1 (Headless CMS)
- **Language**: Node.js with TypeScript
- **Database**: SQLite (development), PostgreSQL (production ready)
- **Authentication**: Strapi built-in JWT authentication
- **File Upload**: Strapi upload plugin with local storage
- **API**: RESTful API with custom endpoints

### External Services
- **Maps**: Google Maps API (Places, Geocoding)
- **Storage**: Local file storage (configurable for cloud)
- **Email**: Configurable email service integration

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   External      │
│   (Next.js)     │◄──►│   (Strapi)      │◄──►│   Services      │
│                 │    │                 │    │                 │
│ • Pages         │    │ • Content Types │    │ • Google Maps   │
│ • Components    │    │ • Controllers   │    │ • Email Service │
│ • Contexts      │    │ • Services      │    │ • File Storage  │
│ • API Layer     │    │ • Middlewares   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Content Types (Strapi)
- **Property**: Main property entity with all details
- **User**: Extended user profiles with membership info
- **Membership**: User membership tiers and limits
- **Nearby Place Category**: Configurable place categories
- **Project**: Real estate projects (separate from properties)

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ (LTS recommended)
- npm 9+ or yarn 1.22+
- Git
- Google Maps API key (for location features)

### Quick Start

1. **Clone the Repository**
```bash
git clone <repository-url>
cd real-estate
```

2. **Backend Setup**
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your configuration
npm run develop
```

3. **Frontend Setup**
```bash
cd ../frontend
npm install
cp .env.local.example .env.local
# Edit .env.local with your configuration
npm run dev
```

4. **Access the Application**
- Frontend: http://localhost:3000
- Backend Admin: http://localhost:1337/admin

### First Time Setup

1. **Create Admin User**
   - Go to http://localhost:1337/admin
   - Create your first admin account

2. **Configure Content Types**
   - The system will auto-generate required content types
   - Configure API permissions in Settings > Users & Permissions

3. **Test User Account**
   - Email: <EMAIL>
   - Password: Mb123321

## 📁 Project Structure

```
real-estate/
├── backend/                 # Strapi CMS Backend
│   ├── src/
│   │   ├── api/            # API endpoints and logic
│   │   │   ├── property/   # Property management
│   │   │   ├── membership/ # User membership system
│   │   │   └── nearby-place-category/ # Location categories
│   │   ├── components/     # Reusable Strapi components
│   │   ├── extensions/     # Strapi extensions
│   │   └── middlewares/    # Custom middlewares
│   ├── config/             # Strapi configuration
│   ├── database/           # Database files
│   └── public/             # Static files
│
├── frontend/               # Next.js Frontend
│   ├── src/
│   │   ├── app/           # App Router pages
│   │   │   ├── auth/      # Authentication pages
│   │   │   ├── dashboard/ # User dashboard
│   │   │   ├── properties/ # Property pages
│   │   │   └── submit-property/ # Property submission
│   │   ├── components/    # React components
│   │   │   ├── Layout/    # Layout components
│   │   │   ├── Property/  # Property-related components
│   │   │   └── UI/        # UI components
│   │   ├── contexts/      # React contexts
│   │   ├── lib/           # Utility libraries
│   │   └── styles/        # Global styles
│   └── public/            # Static assets
│
├── docs/                  # Documentation
├── scripts/               # Utility scripts
└── README.md             # This file
```

## 🔌 API Documentation

### Authentication Endpoints
```
POST /api/auth/local          # Login
POST /api/auth/local/register # Register
GET  /api/users/me           # Get current user
```

### Property Endpoints
```
GET    /api/properties                    # List properties
GET    /api/properties/:id               # Get property details
POST   /api/properties                   # Create property
PUT    /api/properties/:id               # Update property
DELETE /api/properties/:id               # Delete property
POST   /api/properties/:id/generate-nearby-places # Generate nearby places
```

### User Management
```
GET    /api/users/me                     # Current user profile
PUT    /api/users/:id                    # Update user profile
GET    /api/memberships                  # User membership info
```

### Location Services
```
GET    /api/nearby-place-categories/enabled # Get enabled place categories
GET    /api/properties/cities            # Get available cities
GET    /api/properties/neighborhoods     # Get neighborhoods by city
```

## 👤 User Roles & Permissions

### Admin
- Full system access
- Manage all properties and users
- Configure system settings
- Access analytics and reports
- Manage nearby place categories

### User
- Create and manage own properties
- Limited property submissions based on membership
- Access personal dashboard
- Submit properties for review

### Public
- Browse published properties
- Use search and filtering
- View property details
- No account required for browsing

## 🎯 Key Functionalities

### Property Submission Flow
1. **Authentication Check**: User must be logged in
2. **Membership Validation**: Check property submission limits
3. **Form Completion**: Fill property details with validation
4. **File Upload**: Upload images and floor plans
5. **Location Selection**: Choose coordinates and neighborhoods
6. **Preview & Submit**: Review and submit as draft or published

### Search & Filtering System
- **Text Search**: Search in title, description, address
- **Location Filters**: City, neighborhood, proximity
- **Property Filters**: Type, price range, bedrooms, bathrooms
- **Feature Filters**: Luxury, furnished, pet-friendly
- **Advanced Filters**: Area, year built, property code

### Nearby Places System
- **Category Management**: Admin-configurable place categories
- **Google Places Integration**: Real-time place detection
- **Map Visualization**: Interactive map with place markers
- **Category Display**: Organized by type (Education, Food, etc.)
- **Distance Calculation**: Proximity-based results

## 🔧 Environment Setup

### Backend Environment (.env)
```env
# Database
DATABASE_CLIENT=sqlite
DATABASE_FILENAME=.tmp/data.db

# Security
JWT_SECRET=your-super-secret-jwt-key
ADMIN_JWT_SECRET=your-admin-jwt-secret
TRANSFER_TOKEN_SALT=your-transfer-token-salt
API_TOKEN_SALT=your-api-token-salt

# Server
HOST=0.0.0.0
PORT=1337

# Google Services
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
```

### Frontend Environment (.env.local)
```env
# API Configuration
NEXT_PUBLIC_STRAPI_URL=http://localhost:1337

# Google Maps
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# App Configuration
NEXT_PUBLIC_APP_NAME=Real Estate Platform
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 🔄 Development Workflow

### Development Commands
```bash
# Backend
cd backend
npm run develop      # Start development server
npm run build       # Build for production
npm run start       # Start production server

# Frontend
cd frontend
npm run dev         # Start development server
npm run build       # Build for production
npm run start       # Start production server
npm run lint        # Run ESLint
```

### Code Quality
- **TypeScript**: Strict type checking enabled
- **ESLint**: Code linting with Next.js rules
- **Prettier**: Code formatting (recommended)
- **Git Hooks**: Pre-commit hooks for quality checks

## 🧪 Testing

### Test User Credentials
```
Email: <EMAIL>
Password: Mb123321
```

### Testing Scripts
```bash
# Test property submission
node test-submit-property.js

# Test nearby places system
node test-nearby-places.js

# Populate test data
node populate-test-properties.js
```

## 🚀 Deployment

### Production Checklist
- [ ] Configure production database (PostgreSQL recommended)
- [ ] Set up environment variables
- [ ] Configure file storage (AWS S3, Cloudinary, etc.)
- [ ] Set up email service
- [ ] Configure domain and SSL
- [ ] Set up monitoring and logging

### Deployment Options
- **Vercel** (Frontend) + **Railway/Heroku** (Backend)
- **Docker** containers with orchestration
- **Traditional VPS** with PM2 process manager

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes with proper TypeScript types
4. Test your changes thoroughly
5. Commit with descriptive messages
6. Push to your fork and submit a pull request

### Code Standards
- Use TypeScript for all new code
- Follow existing code style and patterns
- Add proper error handling
- Include JSDoc comments for functions
- Test your changes before submitting

### Pull Request Process
1. Update documentation if needed
2. Add tests for new functionality
3. Ensure all tests pass
4. Update CHANGELOG.md
5. Request review from maintainers

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check existing documentation
- Review the troubleshooting guide

---

**Built with ❤️ using Next.js and Strapi**
