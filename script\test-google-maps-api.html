<!DOCTYPE html>
<html>
<head>
    <title>Test Google Maps API</title>
</head>
<body>
    <h1>Google Maps API Test</h1>
    <div id="result"></div>
    
    <script>
        // Test if Google Maps API key is working
        async function testGoogleMapsAPI() {
            const apiKey = 'YOUR_API_KEY_HERE'; // Replace with actual key
            const testAddress = '1600 Amphitheatre Parkway, Mountain View, CA';
            
            try {
                // Test Geocoding API
                const geocodeUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(testAddress)}&key=${apiKey}`;
                
                const response = await fetch(geocodeUrl);
                const data = await response.json();
                
                const resultDiv = document.getElementById('result');
                
                if (data.status === 'OK') {
                    resultDiv.innerHTML = `
                        <h2>✅ Google Maps API is working!</h2>
                        <p><strong>Address:</strong> ${testAddress}</p>
                        <p><strong>Formatted Address:</strong> ${data.results[0].formatted_address}</p>
                        <p><strong>Coordinates:</strong> ${data.results[0].geometry.location.lat}, ${data.results[0].geometry.location.lng}</p>
                        <h3>Address Components:</h3>
                        <ul>
                            ${data.results[0].address_components.map(component => 
                                `<li><strong>${component.types.join(', ')}:</strong> ${component.long_name}</li>`
                            ).join('')}
                        </ul>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h2>❌ Google Maps API Error</h2>
                        <p><strong>Status:</strong> ${data.status}</p>
                        <p><strong>Error:</strong> ${data.error_message || 'Unknown error'}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h2>❌ Network Error</h2>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
        
        // Run test when page loads
        testGoogleMapsAPI();
    </script>
</body>
</html>
