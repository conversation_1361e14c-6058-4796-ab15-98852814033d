'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Search, MapPin, Home, Building2, ChevronLeft, ChevronRight, Filter } from 'lucide-react';
import { propertiesAPI } from '@/lib/api';

const Hero: React.FC = () => {
  const router = useRouter();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [cities, setCities] = useState<string[]>([]);
  const [neighborhoods, setNeighborhoods] = useState<string[]>([]);
  const [searchFilters, setSearchFilters] = useState({
    location: '',
    propertyType: '',
    offer: '',
    priceMin: '',
    priceMax: '',
    bedrooms: '',
    bathrooms: '',
    area: '',
    city: '',
    neighborhood: '',
    propertyCode: '',
    isLuxury: false,
    features: [] as string[]
  });

  const heroSlides = [
    {
      image: '/api/placeholder/1920/800',
      title: 'Find Your Perfect Dream Home',
      subtitle: 'Discover amazing properties and projects with our comprehensive real estate platform',
      overlay: 'from-blue-600/80 to-blue-800/80'
    },
    {
      image: '/api/placeholder/1920/800',
      title: 'Luxury Properties Await',
      subtitle: 'Explore premium real estate opportunities in prime locations',
      overlay: 'from-purple-600/80 to-purple-800/80'
    },
    {
      image: '/api/placeholder/1920/800',
      title: 'Investment Opportunities',
      subtitle: 'Discover profitable real estate investments and development projects',
      overlay: 'from-green-600/80 to-green-800/80'
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [heroSlides.length]);

  useEffect(() => {
    // Load cities and neighborhoods
    const loadLocationData = async () => {
      try {
        const citiesData = await propertiesAPI.getCities();
        setCities(citiesData);
      } catch (error) {
        console.error('Error loading cities:', error);
      }
    };
    loadLocationData();
  }, []);

  useEffect(() => {
    // Load neighborhoods when city changes
    if (searchFilters.city) {
      const loadNeighborhoods = async () => {
        try {
          const neighborhoodsData = await propertiesAPI.getNeighborhoods(searchFilters.city);
          setNeighborhoods(neighborhoodsData);
        } catch (error) {
          console.error('Error loading neighborhoods:', error);
        }
      };
      loadNeighborhoods();
    } else {
      setNeighborhoods([]);
    }
  }, [searchFilters.city]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  const handleFilterChange = (key: string, value: any) => {
    setSearchFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleSearch = () => {
    // Build query parameters
    const queryParams = new URLSearchParams();

    Object.entries(searchFilters).forEach(([key, value]) => {
      if (value && value !== '' && value !== false) {
        if (Array.isArray(value) && value.length > 0) {
          queryParams.set(key, value.join(','));
        } else if (!Array.isArray(value)) {
          queryParams.set(key, value.toString());
        }
      }
    });

    // Navigate to properties page with search parameters
    router.push(`/properties?${queryParams.toString()}`);
  };

  return (
    <section className="relative h-screen overflow-hidden">
      {/* Background Slider */}
      <div className="absolute inset-0">
        {heroSlides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={slide.image}
              alt={slide.title}
              className="w-full h-full object-cover"
            />
            <div className={`absolute inset-0 bg-gradient-to-r ${slide.overlay}`}></div>
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 rounded-full p-2 transition-colors"
      >
        <ChevronLeft className="h-6 w-6 text-white" />
      </button>
      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 rounded-full p-2 transition-colors"
      >
        <ChevronRight className="h-6 w-6 text-white" />
      </button>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 flex space-x-2">
        {heroSlides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-colors ${
              index === currentSlide ? 'bg-white' : 'bg-white/50'
            }`}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 h-full flex items-center">
        <div className="text-center text-white">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            {heroSlides[currentSlide].title.split(' ').slice(0, -2).join(' ')}
            <span className="block text-yellow-400">{heroSlides[currentSlide].title.split(' ').slice(-2).join(' ')}</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            {heroSlides[currentSlide].subtitle}
          </p>

          {/* Enhanced Search Bar */}
          <div className="max-w-6xl mx-auto mb-8">
            <div className="bg-white rounded-lg shadow-2xl p-6">
              {/* Basic Search Row */}
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Location / City"
                    value={searchFilters.location}
                    onChange={(e) => handleFilterChange('location', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                  />
                </div>
                <div className="relative">
                  <Home className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <select
                    value={searchFilters.propertyType}
                    onChange={(e) => handleFilterChange('propertyType', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                  >
                    <option value="">Property Type</option>
                    <option value="apartment">Apartment</option>
                    <option value="villa">Villa</option>
                    <option value="townhouse">Townhouse</option>
                    <option value="penthouse">Penthouse</option>
                    <option value="studio">Studio</option>
                    <option value="duplex">Duplex</option>
                    <option value="land">Land</option>
                    <option value="commercial">Commercial</option>
                  </select>
                </div>
                <div className="relative">
                  <select
                    value={searchFilters.offer}
                    onChange={(e) => handleFilterChange('offer', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                  >
                    <option value="">Offer Type</option>
                    <option value="for-sale">For Sale</option>
                    <option value="for-rent">For Rent</option>
                  </select>
                </div>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Price Range"
                    value={searchFilters.priceMin}
                    onChange={(e) => handleFilterChange('priceMin', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                  />
                </div>
                <button
                  onClick={handleSearch}
                  className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center font-semibold"
                >
                  <Search className="h-5 w-5 mr-2" />
                  Search
                </button>
              </div>

              {/* Advanced Filters Toggle */}
              <div className="flex justify-center">
                <button
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  className="text-blue-600 hover:text-blue-700 flex items-center gap-2 font-medium"
                >
                  <Filter className="h-4 w-4" />
                  {showAdvancedFilters ? 'Hide' : 'Show'} Advanced Filters
                </button>
              </div>

              {/* Advanced Filters */}
              {showAdvancedFilters && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <select
                      value={searchFilters.city}
                      onChange={(e) => handleFilterChange('city', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                    >
                      <option value="">Select City</option>
                      {cities.map((city) => (
                        <option key={city} value={city}>{city}</option>
                      ))}
                    </select>
                    <select
                      value={searchFilters.neighborhood}
                      onChange={(e) => handleFilterChange('neighborhood', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                      disabled={!searchFilters.city}
                    >
                      <option value="">Select Neighborhood</option>
                      {neighborhoods.map((neighborhood) => (
                        <option key={neighborhood} value={neighborhood}>{neighborhood}</option>
                      ))}
                    </select>
                    <input
                      type="text"
                      placeholder="Property Code"
                      value={searchFilters.propertyCode}
                      onChange={(e) => handleFilterChange('propertyCode', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                    />
                    <div className="grid grid-cols-2 gap-2">
                      <input
                        type="number"
                        placeholder="Min Price"
                        value={searchFilters.priceMin}
                        onChange={(e) => handleFilterChange('priceMin', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                      />
                      <input
                        type="number"
                        placeholder="Max Price"
                        value={searchFilters.priceMax}
                        onChange={(e) => handleFilterChange('priceMax', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
                    <select
                      value={searchFilters.bedrooms}
                      onChange={(e) => handleFilterChange('bedrooms', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                    >
                      <option value="">Bedrooms</option>
                      <option value="1">1+</option>
                      <option value="2">2+</option>
                      <option value="3">3+</option>
                      <option value="4">4+</option>
                      <option value="5">5+</option>
                    </select>
                    <select
                      value={searchFilters.bathrooms}
                      onChange={(e) => handleFilterChange('bathrooms', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                    >
                      <option value="">Bathrooms</option>
                      <option value="1">1+</option>
                      <option value="2">2+</option>
                      <option value="3">3+</option>
                      <option value="4">4+</option>
                    </select>
                    <input
                      type="number"
                      placeholder="Min Area (sqft)"
                      value={searchFilters.area}
                      onChange={(e) => handleFilterChange('area', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                    />
                    <div className="flex items-center justify-center space-x-2 bg-gray-50 rounded-md px-4 py-2">
                      <input
                        type="checkbox"
                        id="luxury"
                        checked={searchFilters.isLuxury}
                        onChange={(e) => handleFilterChange('isLuxury', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="luxury" className="text-gray-700 font-medium">Luxury Property</label>
                    </div>
                    <Link
                      href="/projects"
                      className="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors text-center font-semibold flex items-center justify-center"
                    >
                      View Projects
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="text-3xl font-bold mb-2 text-yellow-400">1000+</div>
              <div className="text-white">Properties</div>
            </div>
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="text-3xl font-bold mb-2 text-yellow-400">50+</div>
              <div className="text-white">Projects</div>
            </div>
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="text-3xl font-bold mb-2 text-yellow-400">500+</div>
              <div className="text-white">Happy Clients</div>
            </div>
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="text-3xl font-bold mb-2 text-yellow-400">10+</div>
              <div className="text-white">Years Experience</div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mt-12">
            <Link
              href="/properties"
              className="bg-yellow-400 text-blue-900 px-8 py-4 rounded-lg font-bold hover:bg-yellow-300 transition-all transform hover:scale-105 shadow-lg"
            >
              Browse Properties
            </Link>
            <Link
              href="/projects"
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-bold hover:bg-white hover:text-blue-600 transition-all transform hover:scale-105 shadow-lg"
            >
              View Projects
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
