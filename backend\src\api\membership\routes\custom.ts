/**
 * membership custom routes
 */

export default {
  routes: [
    {
      method: 'POST',
      path: '/memberships/subscribe',
      handler: 'api::membership.membership.subscribe',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/memberships/my-membership',
      handler: 'api::membership.membership.myMembership',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
