const axios = require('axios');

const API_URL = 'http://localhost:1337/api';

async function debugPropertyEndpoints() {
  console.log('🔍 Debugging Property Endpoints...\n');

  try {
    // Login first
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    const token = loginResponse.data.jwt;
    console.log('✅ Logged in successfully');
    
    // Get properties
    const myPropertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const properties = myPropertiesResponse.data.data || myPropertiesResponse.data;
    console.log(`✅ Found ${properties.length} properties\n`);
    
    if (properties.length > 0) {
      const property = properties[0];
      console.log('Testing with property:');
      console.log(`   Title: ${property.title}`);
      console.log(`   Numeric ID: ${property.id}`);
      console.log(`   Document ID: ${property.documentId}`);
      console.log('');
      
      // Test different endpoint variations
      console.log('Testing property view endpoints:');
      
      // Test 1: Using numeric ID
      try {
        const response1 = await axios.get(`${API_URL}/properties/${property.id}`);
        console.log(`✅ Numeric ID (${property.id}): SUCCESS`);
      } catch (error1) {
        console.log(`❌ Numeric ID (${property.id}): ${error1.response?.status} - ${error1.response?.statusText}`);
      }
      
      // Test 2: Using document ID
      try {
        const response2 = await axios.get(`${API_URL}/properties/${property.documentId}`);
        console.log(`✅ Document ID (${property.documentId}): SUCCESS`);
      } catch (error2) {
        console.log(`❌ Document ID (${property.documentId}): ${error2.response?.status} - ${error2.response?.statusText}`);
      }
      
      console.log('\nTesting property edit endpoints:');
      
      // Test 3: Edit with numeric ID
      try {
        const response3 = await axios.get(`${API_URL}/properties/${property.id}/edit`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`✅ Edit Numeric ID (${property.id}): SUCCESS`);
      } catch (error3) {
        console.log(`❌ Edit Numeric ID (${property.id}): ${error3.response?.status} - ${error3.response?.statusText}`);
      }
      
      // Test 4: Edit with document ID
      try {
        const response4 = await axios.get(`${API_URL}/properties/${property.documentId}/edit`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`✅ Edit Document ID (${property.documentId}): SUCCESS`);
      } catch (error4) {
        console.log(`❌ Edit Document ID (${property.documentId}): ${error4.response?.status} - ${error4.response?.statusText}`);
      }
      
      console.log('\nTesting publish endpoints:');
      
      // Test 5: Publish with numeric ID
      try {
        const response5 = await axios.put(`${API_URL}/properties/${property.id}/publish`, {}, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`✅ Publish Numeric ID (${property.id}): SUCCESS`);
        
        // Unpublish it back
        await axios.put(`${API_URL}/properties/${property.id}/unpublish`, {}, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`✅ Unpublish Numeric ID (${property.id}): SUCCESS`);
      } catch (error5) {
        console.log(`❌ Publish Numeric ID (${property.id}): ${error5.response?.status} - ${error5.response?.statusText}`);
      }
      
      // Test 6: Publish with document ID
      try {
        const response6 = await axios.put(`${API_URL}/properties/${property.documentId}/publish`, {}, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`✅ Publish Document ID (${property.documentId}): SUCCESS`);
        
        // Unpublish it back
        await axios.put(`${API_URL}/properties/${property.documentId}/unpublish`, {}, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`✅ Unpublish Document ID (${property.documentId}): SUCCESS`);
      } catch (error6) {
        console.log(`❌ Publish Document ID (${property.documentId}): ${error6.response?.status} - ${error6.response?.statusText}`);
      }
      
      console.log('\n🔧 Recommendations:');
      console.log('   - Use the ID type that works for each endpoint');
      console.log('   - Update frontend to use correct ID types');
      console.log('   - Ensure consistent ID handling across all endpoints');
      
    } else {
      console.log('❌ No properties found to test with');
    }
    
  } catch (error) {
    console.log('❌ Debug failed:', error.message);
  }
}

debugPropertyEndpoints().catch(console.error);
