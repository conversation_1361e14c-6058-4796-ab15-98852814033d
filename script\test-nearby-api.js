const axios = require('axios');

async function testNearbyPlacesAPI() {
  console.log('🧪 Testing Nearby Places API...\n');

  const API_BASE = 'http://localhost:1337/api';

  try {
    // Test 1: Check enabled categories
    console.log('1. Testing enabled categories endpoint...');
    const categoriesResponse = await axios.get(`${API_BASE}/nearby-place-categories/enabled`);
    console.log('✅ Categories response:', JSON.stringify(categoriesResponse.data, null, 2));
    
    if (!categoriesResponse.data.data || categoriesResponse.data.data.length === 0) {
      console.log('⚠️  No enabled categories found');
      return;
    }

    // Test 2: Test Google Places API
    console.log('\n2. Testing Google Places API...');
    const testCoords = { lat: 40.7128, lng: -74.0060 }; // New York
    const testCategory = categoriesResponse.data.data[0];
    
    console.log('Using category:', testCategory.name);
    console.log('Google place types:', testCategory.googlePlaceTypes);

    const placesResponse = await axios.post(`${API_BASE}/properties/nearby-places`, {
      lat: testCoords.lat,
      lng: testCoords.lng,
      types: testCategory.googlePlaceTypes,
      radius: testCategory.searchRadius || 1500,
      maxResults: testCategory.maxResults || 5
    });

    console.log('✅ Places API response:', placesResponse.data);
    
    if (placesResponse.data.success && placesResponse.data.places) {
      console.log(`Found ${placesResponse.data.places.length} places`);
      placesResponse.data.places.forEach((place, i) => {
        console.log(`  ${i + 1}. ${place.name} - ${place.vicinity}`);
      });
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    console.error('Full error:', error);
    if (error.response?.status === 405) {
      console.log('🔍 405 Method Not Allowed - checking route configuration...');
    }
    if (error.response?.status === 404) {
      console.log('🔍 404 Not Found - endpoint may not exist');
    }
  }
}

testNearbyPlacesAPI();
