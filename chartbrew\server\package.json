{"name": "server", "version": "v4.1.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start-dev": "set NODE_ENV=development && nodemon index", "start": "NODE_ENV=production node index", "lint": "eslint .", "db:migrate": "cd models && npx sequelize-cli db:migrate", "precommit": "lint-staged", "postinstall": "npx playwright install && npx playwright install-deps"}, "author": {"email": "<EMAIL>", "name": "Chartbrew"}, "license": "MIT", "dependencies": {"@bull-board/api": "^6.7.9", "@bull-board/express": "^6.7.9", "@clickhouse/client": "^1.10.1", "@google-analytics/admin": "^8.2.0", "@google-analytics/data": "^5.1.0", "@playwright/test": "^1.48.2", "accesscontrol": "^2.2.1", "base-64": "^1.0.0", "bcrypt": "^5.1.1", "body-parser": "^2.2.0", "bullmq": "^5.8.5", "compare-versions": "^6.1.0", "connect-busboy": "^1.0.0", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "crypto-js": "^4.2.0", "date-fns": "^3.3.1", "dotenv": "^16.0.3", "ejs": "^3.1.8", "express": "^5.1.0", "express-rate-limit": "^7.2.0", "firebase-admin": "^12.0.0", "googleapis": "^134.0.0", "helmet": "^7.1.0", "hot-formula-parser": "^4.0.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.20", "luxon": "^3.4.4", "method-override": "^3.0.0", "moment": "^2.29.1", "moment-timezone": "^0.5.41", "mongoose": "^8.2.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.2", "nanoid": "^3.1.20", "node-cron": "^4.2.0", "node-xlsx": "^0.23.0", "nodemailer": "^6.4.17", "openai": "^4.78.1", "otpauth": "^9.2.2", "pg": "^8.5.1", "pg-hstore": "^2.3.3", "playwright": "^1.48.2", "qrcode": "^1.5.3", "redis": "^4.6.15", "request": "^2.88.2", "request-promise": "^4.2.6", "sequelize": "^6.3.5", "simplecrypt": "^0.1.0", "ssh2": "^1.16.0", "tunnel-ssh": "^5.2.0", "umzug": "^3.7.0", "uninstall": "*", "uuid": "^9.0.1"}, "devDependencies": {"@babel/eslint-parser": "^7.19.1", "babel-eslint": "^10.1.0", "eslint": "^8.28.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.22.1", "nodemon": "^3.1.0"}}