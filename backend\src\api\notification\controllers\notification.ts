/**
 * notification controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::notification.notification', ({ strapi }) => ({
  // Get user's notifications
  async find(ctx: any) {
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in to view notifications');
    }

    const { page = 1, pageSize = 20, unreadOnly = false } = ctx.query;

    const pageNum = parseInt(page) || 1;
    const pageSizeNum = parseInt(pageSize) || 20;

    const filters: any = {
      recipient: user.id
    };

    // Filter for unread notifications only if requested
    if (unreadOnly === 'true') {
      filters.isRead = false;
    }

    // Filter out expired notifications - use $and instead of $or for Strapi v5
    filters.$and = [
      {
        $or: [
          { expiresAt: { $null: true } },
          { expiresAt: { $gt: new Date() } }
        ]
      }
    ];

    const notifications = await strapi.entityService.findMany('api::notification.notification', {
      filters,
      populate: ['sender', 'relatedProperty', 'relatedProject', 'relatedMessage'],
      sort: { createdAt: 'desc' },
      start: (pageNum - 1) * pageSizeNum,
      limit: pageSizeNum
    });

    // Get total count for pagination
    const total = await strapi.entityService.count('api::notification.notification', {
      filters
    });

    return {
      data: notifications,
      meta: {
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          pageCount: Math.ceil(total / pageSizeNum),
          total
        }
      }
    };
  },

  // Mark notification as read
  async markAsRead(ctx: any) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    const notification: any = await strapi.entityService.findOne('api::notification.notification', id, {
      populate: ['recipient']
    });

    if (!notification) {
      return ctx.notFound('Notification not found');
    }

    // Check if user is the recipient
    const recipientId = notification.recipient?.id || notification.recipient;
    if (recipientId !== user.id) {
      return ctx.forbidden('You can only mark your own notifications as read');
    }

    const updatedNotification = await strapi.entityService.update('api::notification.notification', id, {
      data: {
        isRead: true,
        readAt: new Date()
      },
      populate: ['sender', 'relatedProperty', 'relatedProject', 'relatedMessage']
    });

    return { data: updatedNotification };
  },

  // Mark all notifications as read
  async markAllAsRead(ctx: any) {
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    // Update all unread notifications for this user
    await strapi.db.query('api::notification.notification').updateMany({
      where: {
        recipient: user.id,
        isRead: false
      },
      data: {
        isRead: true,
        readAt: new Date()
      }
    });

    return { data: { message: 'All notifications marked as read' } };
  },

  // Get unread count
  async getUnreadCount(ctx: any) {
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    const count = await strapi.entityService.count('api::notification.notification', {
      filters: {
        recipient: user.id,
        isRead: false,
        $and: [
          {
            $or: [
              { expiresAt: { $null: true } },
              { expiresAt: { $gt: new Date() } }
            ]
          }
        ]
      }
    });

    return { data: { count } };
  },

  // Delete notification
  async delete(ctx: any) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    const notification: any = await strapi.entityService.findOne('api::notification.notification', id, {
      populate: ['recipient']
    });

    if (!notification) {
      return ctx.notFound('Notification not found');
    }

    // Check if user is the recipient
    const recipientId = notification.recipient?.id || notification.recipient;
    if (recipientId !== user.id) {
      return ctx.forbidden('You can only delete your own notifications');
    }

    await strapi.entityService.delete('api::notification.notification', id);

    return { data: { message: 'Notification deleted successfully' } };
  },

  // Test endpoint to create a notification
  async createTest(ctx: any) {
    const user = ctx.state.user;
    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      const notification = await strapi.service('api::notification.notification').createNotification({
        recipientId: user.id,
        title: 'Test Notification',
        message: 'This is a test notification to verify the system is working.',
        type: 'info',
        priority: 'normal',
        actionUrl: '/dashboard',
        actionText: 'Go to Dashboard'
      });

      return { data: notification };
    } catch (error) {
      console.error('Error creating test notification:', error);
      return ctx.internalServerError('Failed to create test notification');
    }
  }
}));
