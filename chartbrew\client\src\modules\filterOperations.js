export const operators = [{
  key: "=",
  text: "= (is)",
  value: "is",
}, {
  key: "≠",
  text: "≠ (is not)",
  value: "isNot",
}, {
  key: "!∅",
  text: "!∅ (is not null)",
  value: "isNotNull",
}, {
  key: "∅",
  text: "∅ (is null)",
  value: "isNull",
}, {
  key: ">",
  text: "> (greater than)",
  value: "greaterThan",
}, {
  key: "≥",
  text: "≥ (greater or equal)",
  value: "greaterOrEqual",
}, {
  key: "<",
  text: "< (less than)",
  value: "lessThan",
}, {
  key: "≤",
  text: "≤ (less or equal)",
  value: "lessOrEqual",
}, {
  key: "∈",
  text: "∈ (contains)",
  value: "contains",
}, {
  key: "∉",
  text: "∉ (does not contain)",
  value: "notContains",
}];

export const operations = [{
  key: "none",
  text: "No operation",
  value: "none",
}, {
  key: "count",
  text: "Count",
  value: "count",
}, {
  key: "count_unique",
  text: "Count uniques",
  value: "count_unique",
}, {
  key: "sum",
  text: "Sum",
  value: "sum",
}, {
  key: "avg",
  text: "Average",
  value: "avg",
}, {
  key: "min",
  text: "Min",
  value: "min",
}, {
  key: "max",
  text: "Max",
  value: "max",
}];
