// Test script to verify Chartbrew connectivity
// Run this with: node test-chartbrew-connectivity.js

const http = require('http');

function testConnection(host, port, path = '/') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: host,
      port: port,
      path: path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: data.substring(0, 100) // First 100 chars
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function runTests() {
  console.log('Testing Chartbrew connectivity...\n');

  const tests = [
    { name: 'Chartbrew API (localhost)', host: 'localhost', port: 4019 },
    { name: 'Chartbrew API (127.0.0.1)', host: '127.0.0.1', port: 4019 },
    { name: 'Chartbrew Frontend (localhost)', host: 'localhost', port: 4018 },
    { name: 'Chartbrew Frontend (127.0.0.1)', host: '127.0.0.1', port: 4018 },
  ];

  for (const test of tests) {
    try {
      const result = await testConnection(test.host, test.port);
      console.log(`✅ ${test.name}: Status ${result.status}`);
      console.log(`   Response: ${result.data.trim()}\n`);
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}\n`);
    }
  }

  console.log('Test completed. If any tests failed, check if the services are running.');
}

runTests();
