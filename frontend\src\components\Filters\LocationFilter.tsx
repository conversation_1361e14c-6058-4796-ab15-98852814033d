'use client';

import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Search, X, Navigation, Target } from 'lucide-react';
import { useCities, useNeighborhoods } from '@/hooks/useLocationData';

interface LocationFilterProps {
  city: string;
  neighborhood: string;
  searchRadius?: number;
  onCityChange: (value: string) => void;
  onNeighborhoodChange: (value: string) => void;
  onRadiusChange?: (value: number) => void;
  className?: string;
}



const RADIUS_OPTIONS = [
  { value: 1, label: '1 mile' },
  { value: 5, label: '5 miles' },
  { value: 10, label: '10 miles' },
  { value: 25, label: '25 miles' },
  { value: 50, label: '50 miles' },
];

export const LocationFilter: React.FC<LocationFilterProps> = ({
  city,
  neighborhood,
  searchRadius = 10,
  onCityChange,
  onNeighborhoodChange,
  onRadiusChange,
  className = '',
}) => {
  const [citySearch, setCitySearch] = useState('');
  const [neighborhoodSearch, setNeighborhoodSearch] = useState('');
  const [showCityDropdown, setShowCityDropdown] = useState(false);
  const [showNeighborhoodDropdown, setShowNeighborhoodDropdown] = useState(false);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);

  const cityInputRef = useRef<HTMLInputElement>(null);
  const neighborhoodInputRef = useRef<HTMLInputElement>(null);
  const cityDropdownRef = useRef<HTMLDivElement>(null);
  const neighborhoodDropdownRef = useRef<HTMLDivElement>(null);

  // Fetch location data
  const { data: cities = [], isLoading: citiesLoading } = useCities();
  const { data: neighborhoods = [], isLoading: neighborhoodsLoading } = useNeighborhoods(city);

  // Filter cities based on search
  const filteredCities = cities.filter((c): c is string =>
    typeof c === 'string' && c.toLowerCase().includes(citySearch.toLowerCase())
  );

  // Filter neighborhoods based on search
  const filteredNeighborhoods = neighborhoods.filter((n): n is string =>
    typeof n === 'string' && n.toLowerCase().includes(neighborhoodSearch.toLowerCase())
  );

  // Get user's current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
          // Here you would reverse geocode to get city/neighborhood
          console.log('User location:', position.coords);
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  };

  // Handle city selection
  const handleCitySelect = (selectedCity: string) => {
    onCityChange(selectedCity);
    setCitySearch(selectedCity);
    setShowCityDropdown(false);
    // Clear neighborhood when city changes
    if (selectedCity !== city) {
      onNeighborhoodChange('');
      setNeighborhoodSearch('');
    }
  };

  // Handle neighborhood selection
  const handleNeighborhoodSelect = (selectedNeighborhood: string) => {
    onNeighborhoodChange(selectedNeighborhood);
    setNeighborhoodSearch(selectedNeighborhood);
    setShowNeighborhoodDropdown(false);
  };

  // Clear location filters
  const clearLocation = () => {
    onCityChange('');
    onNeighborhoodChange('');
    setCitySearch('');
    setNeighborhoodSearch('');
  };

  // Initialize search values
  useEffect(() => {
    setCitySearch(city);
    setNeighborhoodSearch(neighborhood);
  }, [city, neighborhood]);

  // Handle click outside and keyboard events to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close city dropdown if clicking outside
      if (cityDropdownRef.current && !cityDropdownRef.current.contains(event.target as Node)) {
        setShowCityDropdown(false);
      }

      // Close neighborhood dropdown if clicking outside
      if (neighborhoodDropdownRef.current && !neighborhoodDropdownRef.current.contains(event.target as Node)) {
        setShowNeighborhoodDropdown(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      // Close dropdowns on Escape key
      if (event.key === 'Escape') {
        setShowCityDropdown(false);
        setShowNeighborhoodDropdown(false);
      }
    };

    // Add event listeners
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    // Cleanup
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          <MapPin className="inline h-4 w-4 mr-1 text-gray-500" />
          Location
        </label>
        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={getCurrentLocation}
            className="text-xs text-blue-600 hover:text-blue-700 flex items-center space-x-1"
          >
            <Navigation className="w-3 h-3" />
            <span>Use My Location</span>
          </button>
          {(city || neighborhood) && (
            <button
              type="button"
              onClick={clearLocation}
              className="text-xs text-red-600 hover:text-red-700 flex items-center space-x-1"
            >
              <X className="w-3 h-3" />
              <span>Clear</span>
            </button>
          )}
        </div>
      </div>

      {/* City Filter */}
      <div className="relative" ref={cityDropdownRef}>
        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <input
            ref={cityInputRef}
            type="text"
            placeholder="Search city..."
            value={citySearch}
            onChange={(e) => {
              setCitySearch(e.target.value);
              setShowCityDropdown(true);
            }}
            onFocus={() => setShowCityDropdown(true)}
            className="w-full pl-9 pr-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {city && (
            <button
              type="button"
              onClick={() => handleCitySelect('')}
              className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>

        {/* City Dropdown */}
        {showCityDropdown && filteredCities.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
            {filteredCities.map((cityOption) => (
              <button
                key={cityOption}
                type="button"
                onClick={() => handleCitySelect(cityOption)}
                className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center space-x-2"
              >
                <MapPin className="w-4 h-4 text-gray-400" />
                <span>{cityOption}</span>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Neighborhood Filter */}
      {city && (
        <div className="relative" ref={neighborhoodDropdownRef}>
          <div className="relative">
            <Target className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <input
              ref={neighborhoodInputRef}
              type="text"
              placeholder="Search neighborhood..."
              value={neighborhoodSearch}
              onChange={(e) => {
                setNeighborhoodSearch(e.target.value);
                setShowNeighborhoodDropdown(true);
              }}
              onFocus={() => setShowNeighborhoodDropdown(true)}
              className="w-full pl-9 pr-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            {neighborhood && (
              <button
                type="button"
                onClick={() => handleNeighborhoodSelect('')}
                className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          {/* Neighborhood Dropdown */}
          {showNeighborhoodDropdown && filteredNeighborhoods.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
              {filteredNeighborhoods.map((neighborhoodOption) => (
                <button
                  key={neighborhoodOption}
                  type="button"
                  onClick={() => handleNeighborhoodSelect(neighborhoodOption)}
                  className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center space-x-2"
                >
                  <Target className="w-4 h-4 text-gray-400" />
                  <span>{neighborhoodOption}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Search Radius */}
      {onRadiusChange && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Search Radius
          </label>
          <select
            value={searchRadius}
            onChange={(e) => onRadiusChange(parseInt(e.target.value))}
            className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {RADIUS_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Current Selection Display */}
      {(city || neighborhood) && (
        <div className="p-3 bg-green-50 rounded-md">
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-800">
              {neighborhood ? `${neighborhood}, ${city}` : city}
              {onRadiusChange && ` (${searchRadius} mile radius)`}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
