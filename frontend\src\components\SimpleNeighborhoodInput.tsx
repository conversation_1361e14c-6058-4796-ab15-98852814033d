'use client';

import React, { useState } from 'react';
import { X, Plus } from 'lucide-react';

interface SimpleNeighborhoodInputProps {
  value: string[];
  onChange: (neighborhoods: string[]) => void;
  maxSelections?: number;
  placeholder?: string;
  className?: string;
}

export const SimpleNeighborhoodInput: React.FC<SimpleNeighborhoodInputProps> = ({
  value = [],
  onChange,
  maxSelections = 3,
  placeholder = "Enter neighborhood name...",
  className = ''
}) => {
  const [inputValue, setInputValue] = useState('');

  const handleAddNeighborhood = () => {
    const trimmedValue = inputValue.trim();
    if (trimmedValue && !value.includes(trimmedValue) && value.length < maxSelections) {
      onChange([...value, trimmedValue]);
      setInputValue('');
    }
  };

  const handleRemoveNeighborhood = (neighborhoodToRemove: string) => {
    onChange(value.filter(neighborhood => neighborhood !== neighborhoodToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddNeighborhood();
    }
  };

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Neighborhood
        <span className="text-gray-500 ml-1">({value.length}/{maxSelections})</span>
      </label>
      
      {/* Selected Neighborhoods */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-3">
          {value.map((neighborhood, index) => (
            <span
              key={index}
              className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
            >
              {neighborhood}
              <button
                type="button"
                onClick={() => handleRemoveNeighborhood(neighborhood)}
                className="ml-2 hover:text-blue-600"
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      {/* Input Field */}
      <div className="flex space-x-2">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          disabled={value.length >= maxSelections}
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
        />
        <button
          type="button"
          onClick={handleAddNeighborhood}
          disabled={!inputValue.trim() || value.includes(inputValue.trim()) || value.length >= maxSelections}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
        >
          <Plus className="h-4 w-4" />
          <span>Add</span>
        </button>
      </div>

      {/* Help Text */}
      <p className="text-sm text-gray-500 mt-2">
        Enter neighborhood names manually. This is separate from nearby places detection.
      </p>
      
      {value.length >= maxSelections && (
        <p className="text-sm text-orange-600 mt-1">
          Maximum {maxSelections} neighborhoods allowed
        </p>
      )}
    </div>
  );
};
