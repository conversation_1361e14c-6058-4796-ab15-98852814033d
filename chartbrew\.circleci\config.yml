version: 5
jobs:
  setup:
    docker:
      - image: cimg/node:22.15.0
      - image: circleci/php:7.1-apache-node-browsers # The primary container where steps are run
      - image: circleci/mysql:5.7.27
        environment:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: chartbrewdev
          MYSQL_USER: root

    working_directory: ~/chartbrew

    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
            - v7-dependencies-{{ checksum "package.json" }}
            # fallback to using the latest cache if no exact match is found
            - v7-dependencies-

      - run: npm run setup

      - save_cache:
          paths:
            - node_modules
            - client/node_modules
            - server/node_modules
          key: v7-dependencies-{{ checksum "package.json" }}

  # lint the server project
  lint_server:
    docker:
      - image: cimg/node:22.15.0
    working_directory: ~/chartbrew
    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
            - v7-dependencies-{{ checksum "package.json" }}
            # fallback to using the latest cache if no exact match is found
            - v7-dependencies-
      - run:
          name: Linting server code
          command: cd server && npm install && npm run lint
  
  # lint the client project
  lint_client:
    docker:
      - image: cimg/node:22.15.0
    working_directory: ~/chartbrew
    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
            - v7-dependencies-{{ checksum "package.json" }}
            # fallback to using the latest cache if no exact match is found
            - v7-dependencies-

      - run:
          name: Linting client code
          command: cd client && npm install && npm run lint

  build_client:
    docker:
      - image: cimg/node:22.15.0
    working_directory: ~/chartbrew
    # Build the client
    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
            - v7-dependencies-{{ checksum "package.json" }}
            # fallback to using the latest cache if no exact match is found
            - v7-dependencies-

      - run:
          name: Building the client
          command: cd client && npm install && npm run build

workflows:
  version: 2
  setup_and_build:
    jobs:
      - setup
      - lint_server:
          requires:
            - setup
      - lint_client:
          requires:
            - setup
      - build_client:
          requires:
            - setup
