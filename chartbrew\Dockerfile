FROM node:22-slim

WORKDIR /code
COPY . .

RUN apt-get update \
  && apt-get install -y gnupg dos2unix \
  && apt-key adv … || true \
  && apt-get update

# Convert line endings, install deps, build UI
RUN dos2unix entrypoint.sh

RUN cd client && npm install \
 && cd ../server && npm install \
 && npx playwright install-deps && npx playwright install

RUN npm run prepareSettings
RUN cd client && npm run build

RUN chmod +x entrypoint.sh

EXPOSE 4018 
EXPOSE 4019
ENTRYPOINT ["./entrypoint.sh"]
