const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:1337/api';

class ImageDisplayTester {
  constructor() {
    this.token = null;
    this.propertyId = null;
  }

  async login() {
    console.log('🔐 Logging in...');
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/local`, {
        identifier: '<EMAIL>',
        password: 'TestPassword123!'
      });
      
      this.token = response.data.jwt;
      console.log('✅ Login successful');
      return true;
    } catch (error) {
      console.log('❌ Login failed:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async testPropertyWithImages() {
    console.log('\n📋 Testing property with images...');
    
    try {
      // Get MY properties with images
      const response = await axios.get(`${API_BASE_URL}/properties/my-properties`, {
        headers: { 'Authorization': `Bearer ${this.token}` },
        params: {
          populate: ['images'],
          'filters[images][$notNull]': true
        }
      });

      const properties = response.data.data;
      console.log(`✅ Found ${properties.length} total properties`);

      // Find a property with images
      const propertyWithImages = properties.find(p => p.images && p.images.length > 0);

      if (propertyWithImages) {
        const property = propertyWithImages;
        this.propertyId = property.id;
        
        console.log(`\n🏠 Property: ${property.title}`);
        console.log(`📸 Images: ${property.images?.length || 0}`);
        
        if (property.images && property.images.length > 0) {
          property.images.forEach((image, index) => {
            console.log(`   Image ${index + 1}:`);
            console.log(`     ID: ${image.id}`);
            console.log(`     URL: ${image.url}`);
            console.log(`     Full URL: http://localhost:1337${image.url}`);
            console.log(`     Name: ${image.name}`);
            console.log(`     Size: ${image.size} bytes`);
          });
        }
        
        return property;
      } else {
        console.log('❌ No properties with images found');
        // Use the first property anyway for testing
        if (properties.length > 0) {
          this.propertyId = properties[0].id;
          return properties[0];
        }
        return null;
      }
    } catch (error) {
      console.log('❌ Failed to fetch properties:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  async testPropertyForEdit() {
    if (!this.propertyId) {
      console.log('❌ No property ID available for edit test');
      return null;
    }

    console.log(`\n✏️  Testing property ${this.propertyId} for edit...`);
    
    try {
      const response = await axios.get(`${API_BASE_URL}/properties/${this.propertyId}/edit`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      const property = response.data.data;
      console.log(`✅ Property loaded for edit: ${property.title}`);
      console.log(`📸 Images available for edit: ${property.images?.length || 0}`);
      
      if (property.images && property.images.length > 0) {
        console.log('   Edit form should display these existing images:');
        property.images.forEach((image, index) => {
          console.log(`     ${index + 1}. ${image.name} (${image.url})`);
        });
      }
      
      return property;
    } catch (error) {
      console.log('❌ Failed to fetch property for edit:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  async testImageAccessibility() {
    console.log('\n🌐 Testing image accessibility...');
    
    try {
      // Test if we can access an uploaded image directly
      const response = await axios.get('http://localhost:1337/uploads/image_67bde55f96.jpg');
      console.log(`✅ Image accessible: Status ${response.status}, Size: ${response.headers['content-length']} bytes`);
      console.log(`   Content-Type: ${response.headers['content-type']}`);
      return true;
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('❌ Image not found (404) - may have been deleted or moved');
      } else {
        console.log('❌ Image access failed:', error.message);
      }
      return false;
    }
  }

  async runAllTests() {
    console.log('🧪 Starting Image Display Tests\n');
    
    // Test 1: Login
    const loginSuccess = await this.login();
    if (!loginSuccess) return;

    // Test 2: Get properties with images
    const property = await this.testPropertyWithImages();
    
    // Test 3: Test property for edit
    await this.testPropertyForEdit();
    
    // Test 4: Test image accessibility
    await this.testImageAccessibility();
    
    console.log('\n🎯 Image Display Tests Complete!');
    
    if (property && property.images && property.images.length > 0) {
      console.log('\n📋 Summary:');
      console.log('✅ Properties with images found');
      console.log('✅ Images have proper URLs');
      console.log('✅ Edit endpoint returns image data');
      console.log('\n🔧 Frontend should now display:');
      console.log('   - Existing images in edit form');
      console.log('   - Proper image URLs in property listings');
      console.log('   - Placeholder images when no images exist');
    }
  }
}

// Run the tests
const tester = new ImageDisplayTester();
tester.runAllTests().catch(console.error);
