/**
 * Property lifecycle hooks for automatic slug generation
 */

// Utility function to generate slug from title
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Check if slug is unique
async function ensureUniqueSlug(strapi: any, slug: string, excludeId?: string): Promise<string> {
  let uniqueSlug = slug;
  let counter = 1;

  while (true) {
    const filters: any = { slug: { $eq: uniqueSlug } };
    
    // Exclude current property when updating
    if (excludeId) {
      filters.documentId = { $ne: excludeId };
    }

    const existingProperties = await strapi.entityService.findMany(
      'api::property.property',
      {
        filters,
        fields: ['id', 'documentId', 'slug']
      }
    );

    if (existingProperties.length === 0) {
      break;
    }

    uniqueSlug = `${slug}-${counter}`;
    counter++;
  }

  return uniqueSlug;
}

export default {
  // Before creating a property
  async beforeCreate(event: any) {
    const { data } = event.params;
    
    console.log('Property beforeCreate lifecycle triggered');
    console.log('Data received:', data);

    // Generate slug if not provided or if title changed
    if (data.title && !data.slug) {
      const baseSlug = generateSlug(data.title);
      data.slug = await ensureUniqueSlug(strapi, baseSlug);
      console.log('Generated slug for new property:', data.slug);
    }
  },

  // Before updating a property
  async beforeUpdate(event: any) {
    const { data, where } = event.params;
    
    console.log('Property beforeUpdate lifecycle triggered');
    console.log('Update data:', data);
    console.log('Where clause:', where);

    // Only regenerate slug if title changed and no explicit slug provided
    if (data.title && !data.slug) {
      try {
        // Get current property to check if title actually changed
        const currentProperty = await strapi.entityService.findOne(
          'api::property.property',
          where.id || where.documentId,
          {
            fields: ['title', 'slug']
          }
        );

        if (currentProperty && currentProperty.title !== data.title) {
          const baseSlug = generateSlug(data.title);
          data.slug = await ensureUniqueSlug(strapi, baseSlug, where.documentId || where.id);
          console.log('Updated slug for property:', data.slug);
        }
      } catch (error) {
        console.error('Error in beforeUpdate lifecycle:', error);
        // Don't fail the update if slug generation fails
      }
    }
  },

  // After creating a property
  async afterCreate(event: any) {
    const { result } = event;
    console.log('Property created with slug:', result.slug);
  },

  // After updating a property
  async afterUpdate(event: any) {
    const { result } = event;
    console.log('Property updated with slug:', result.slug);
  }
};
