/**
 * Migration script to generate slugs for existing properties
 * Run this script after adding the slug field to the property schema
 */

const strapi = require('@strapi/strapi');

// Utility function to generate slug from title
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Check if slug is unique
async function ensureUniqueSlug(strapi, slug, excludeId) {
  let uniqueSlug = slug;
  let counter = 1;

  while (true) {
    const filters = { slug: { $eq: uniqueSlug } };
    
    // Exclude current property when updating
    if (excludeId) {
      filters.documentId = { $ne: excludeId };
    }

    const existingProperties = await strapi.entityService.findMany(
      'api::property.property',
      {
        filters,
        fields: ['id', 'documentId', 'slug']
      }
    );

    if (existingProperties.length === 0) {
      break;
    }

    uniqueSlug = `${slug}-${counter}`;
    counter++;
  }

  return uniqueSlug;
}

async function generatePropertySlugs() {
  console.log('Starting property slug generation...');

  try {
    // Get all properties without slugs
    const properties = await strapi.entityService.findMany(
      'api::property.property',
      {
        filters: {
          $or: [
            { slug: { $null: true } },
            { slug: { $eq: '' } }
          ]
        },
        fields: ['id', 'documentId', 'title', 'slug']
      }
    );

    console.log(`Found ${properties.length} properties without slugs`);

    let updated = 0;
    let errors = 0;

    for (const property of properties) {
      try {
        if (!property.title) {
          console.log(`Skipping property ${property.id} - no title`);
          continue;
        }

        const baseSlug = generateSlug(property.title);
        const uniqueSlug = await ensureUniqueSlug(strapi, baseSlug, property.documentId);

        await strapi.entityService.update(
          'api::property.property',
          property.documentId,
          {
            data: { slug: uniqueSlug }
          }
        );

        console.log(`Updated property ${property.id}: "${property.title}" -> "${uniqueSlug}"`);
        updated++;
      } catch (error) {
        console.error(`Error updating property ${property.id}:`, error.message);
        errors++;
      }
    }

    console.log(`\nSlug generation completed:`);
    console.log(`- Updated: ${updated} properties`);
    console.log(`- Errors: ${errors} properties`);
    console.log(`- Total processed: ${properties.length} properties`);

  } catch (error) {
    console.error('Error during slug generation:', error);
  }
}

// Main execution
async function main() {
  try {
    // Initialize Strapi
    const app = await strapi().load();
    
    // Run the migration
    await generatePropertySlugs();
    
    console.log('Migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { generatePropertySlugs, generateSlug, ensureUniqueSlug };
