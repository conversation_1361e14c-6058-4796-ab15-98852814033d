// Test script to verify submit property functionality
const axios = require('axios');

const API_BASE = 'http://localhost:1337/api';

// Test user credentials
const userCredentials = {
  email: '<EMAIL>',
  password: 'Mb123321'
};

async function testSubmitProperty() {
  console.log('🚀 Testing Submit Property Functionality...\n');

  try {
    // 1. Login
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/local`, {
      identifier: userCredentials.email,
      password: userCredentials.password
    });

    if (loginResponse.status === 200) {
      console.log('✅ Login successful');
      const token = loginResponse.data.jwt;
      const user = loginResponse.data.user;
      console.log(`   User: ${user.username} (${user.email})`);

      // 2. Test nearby place categories endpoint
      console.log('\n🔍 Testing nearby place categories...');
      try {
        const categoriesResponse = await axios.get(`${API_BASE}/nearby-place-categories/enabled`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        console.log('✅ Categories endpoint accessible');
        console.log(`   Found ${categoriesResponse.data.data?.length || 0} categories`);
      } catch (categoryError) {
        console.log('⚠️  Categories endpoint not available (using fallback)');
        console.log(`   Status: ${categoryError.response?.status || 'Connection error'}`);
      }

      // 3. Test property creation
      console.log('\n📝 Testing property creation...');
      const testProperty = {
        title: 'Test Property - Submit Form',
        description: 'This is a test property created via submit form test',
        price: 250000,
        currency: 'USD',
        propertyType: 'apartment',
        offer: 'for-sale',
        bedrooms: 2,
        bathrooms: 1,
        area: 85,
        areaUnit: 'sqm',
        address: '123 Test Street',
        city: 'Test City',
        country: 'Test Country',
        neighborhood: ['Downtown', 'City Center'],
        coordinates: {
          lat: 40.7128,
          lng: -74.0060
        },
        propertyCode: 'TEST-' + Date.now(),
        isLuxury: false,
        features: ['parking', 'balcony'],
        furnished: true,
        petFriendly: false,
        publishedAt: null // Save as draft
      };

      const propertyResponse = await axios.post(`${API_BASE}/properties`, {
        data: testProperty
      }, {
        headers: { 
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (propertyResponse.status === 200) {
        console.log('✅ Property created successfully');
        const property = propertyResponse.data.data;
        console.log(`   Property ID: ${property.id}`);
        console.log(`   Title: ${property.title}`);
        console.log(`   Status: ${property.publishedAt ? 'Published' : 'Draft'}`);

        // 4. Test nearby places generation
        console.log('\n🗺️  Testing nearby places generation...');
        try {
          const nearbyResponse = await axios.post(`${API_BASE}/properties/${property.id}/generate-nearby-places`, {}, {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          if (nearbyResponse.status === 200) {
            console.log('✅ Nearby places generated successfully');
            const nearbyPlaces = nearbyResponse.data.data.nearbyPlaces;
            const categoryCount = Object.keys(nearbyPlaces).length;
            console.log(`   Found ${categoryCount} place categories`);
            
            Object.entries(nearbyPlaces).forEach(([categoryName, categoryData]) => {
              const places = categoryData.places || [];
              console.log(`   - ${categoryData.category.displayName}: ${places.length} places`);
            });
          }
        } catch (nearbyError) {
          console.log('⚠️  Nearby places generation failed');
          console.log(`   Error: ${nearbyError.response?.data?.error?.message || nearbyError.message}`);
        }

        console.log('\n🎉 Test completed successfully!');
        console.log('\n📋 Summary:');
        console.log('✅ User authentication works');
        console.log('✅ Property creation works');
        console.log('✅ Submit property form should work correctly');
        console.log('\n💡 You can now test the submit property form in the browser!');

      } else {
        console.log('❌ Property creation failed');
        console.log(`   Status: ${propertyResponse.status}`);
      }

    } else {
      console.log('❌ Login failed');
      console.log(`   Status: ${loginResponse.status}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testSubmitProperty();
