/**
 * Integration test for property API endpoints
 * Tests the actual API functionality with a running Strapi instance
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:1337/api';

// Test configuration
const TEST_CONFIG = {
  timeout: 10000, // 10 seconds timeout
  retries: 3
};

// Helper function to make API requests with retry logic
async function makeRequest(url, options = {}, retries = TEST_CONFIG.retries) {
  try {
    const response = await axios({
      url: `${API_BASE_URL}${url}`,
      timeout: TEST_CONFIG.timeout,
      ...options
    });
    return response;
  } catch (error) {
    if (retries > 0 && (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT')) {
      console.log(`Retrying request to ${url}... (${retries} retries left)`);
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      return makeRequest(url, options, retries - 1);
    }
    throw error;
  }
}

// Test basic properties endpoint
async function testPropertiesEndpoint() {
  console.log('Testing /api/properties endpoint...');
  
  try {
    const response = await makeRequest('/properties');
    
    console.assert(response.status === 200, 'Properties endpoint should return 200');
    console.assert(response.data, 'Response should have data');
    console.assert(Array.isArray(response.data.data), 'Response data should be an array');
    console.assert(response.data.meta, 'Response should have meta information');
    console.assert(response.data.meta.pagination, 'Response should have pagination meta');
    
    console.log(`✅ Properties endpoint test passed - returned ${response.data.data.length} properties`);
    return response.data;
  } catch (error) {
    console.error('❌ Properties endpoint test failed:', error.message);
    throw error;
  }
}

// Test properties endpoint with pagination
async function testPropertiesPagination() {
  console.log('Testing properties pagination...');
  
  try {
    const response = await makeRequest('/properties', {
      params: {
        'pagination[page]': 1,
        'pagination[pageSize]': 5
      }
    });
    
    console.assert(response.status === 200, 'Paginated request should return 200');
    console.assert(response.data.meta.pagination.page === 1, 'Page should be 1');
    console.assert(response.data.meta.pagination.pageSize === 5, 'Page size should be 5');
    console.assert(response.data.data.length <= 5, 'Should return at most 5 items');
    
    console.log('✅ Properties pagination test passed');
    return response.data;
  } catch (error) {
    console.error('❌ Properties pagination test failed:', error.message);
    throw error;
  }
}

// Test properties endpoint with filters
async function testPropertiesFilters() {
  console.log('Testing properties filters...');
  
  try {
    const response = await makeRequest('/properties', {
      params: {
        'filters[propertyType]': 'apartment',
        'pagination[pageSize]': 10
      }
    });
    
    console.assert(response.status === 200, 'Filtered request should return 200');
    console.assert(response.data.data, 'Response should have data');
    
    // Check if any properties are returned and if they match the filter
    if (response.data.data.length > 0) {
      const firstProperty = response.data.data[0];
      console.log('Sample filtered property:', {
        id: firstProperty.id,
        title: firstProperty.title,
        propertyType: firstProperty.propertyType
      });
    }
    
    console.log('✅ Properties filters test passed');
    return response.data;
  } catch (error) {
    console.error('❌ Properties filters test failed:', error.message);
    throw error;
  }
}

// Test featured properties endpoint
async function testFeaturedProperties() {
  console.log('Testing /api/properties/featured endpoint...');
  
  try {
    const response = await makeRequest('/properties/featured');
    
    console.assert(response.status === 200, 'Featured properties endpoint should return 200');
    console.assert(response.data, 'Response should have data');
    console.assert(Array.isArray(response.data.data), 'Featured properties data should be an array');
    
    console.log(`✅ Featured properties test passed - returned ${response.data.data.length} featured properties`);
    return response.data;
  } catch (error) {
    console.error('❌ Featured properties test failed:', error.message);
    throw error;
  }
}

// Test property by slug endpoint (if any properties exist)
async function testPropertyBySlug(properties) {
  console.log('Testing /api/properties/by-slug/:slug endpoint...');
  
  if (!properties || properties.length === 0) {
    console.log('⚠️ Skipping slug test - no properties available');
    return;
  }
  
  const propertyWithSlug = properties.find(p => p.slug);
  if (!propertyWithSlug) {
    console.log('⚠️ Skipping slug test - no properties with slugs found');
    return;
  }
  
  try {
    const response = await makeRequest(`/properties/by-slug/${propertyWithSlug.slug}`);
    
    console.assert(response.status === 200, 'Property by slug should return 200');
    console.assert(response.data.data, 'Response should have property data');
    console.assert(response.data.data.slug === propertyWithSlug.slug, 'Returned property should have matching slug');
    
    console.log('✅ Property by slug test passed');
    return response.data;
  } catch (error) {
    console.error('❌ Property by slug test failed:', error.message);
    throw error;
  }
}

// Main test runner
async function runIntegrationTests() {
  console.log('🧪 Running Property API Integration Tests\n');
  
  try {
    // Test basic endpoints
    const propertiesData = await testPropertiesEndpoint();
    await testPropertiesPagination();
    await testPropertiesFilters();
    await testFeaturedProperties();
    
    // Test slug endpoint if properties exist
    if (propertiesData && propertiesData.data) {
      await testPropertyBySlug(propertiesData.data);
    }
    
    console.log('\n🎉 All integration tests passed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- ✅ Basic properties endpoint');
    console.log('- ✅ Properties pagination');
    console.log('- ✅ Properties filters');
    console.log('- ✅ Featured properties endpoint');
    console.log('- ✅ Property by slug endpoint');
    
  } catch (error) {
    console.error('\n❌ Integration tests failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Make sure the Strapi server is running on http://localhost:1337');
      console.error('   Run: npm run develop');
    }
    
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runIntegrationTests();
}

module.exports = {
  runIntegrationTests,
  testPropertiesEndpoint,
  testPropertiesPagination,
  testPropertiesFilters,
  testFeaturedProperties,
  testPropertyBySlug
};
