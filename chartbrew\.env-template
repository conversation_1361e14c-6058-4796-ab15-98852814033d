#### PRODUCTION VARS ####

### Database connection parameters
CB_DB_NAME=chartbrew
CB_DB_USERNAME=
CB_DB_PASSWORD=
CB_DB_HOST=localhost
CB_DB_PORT=3306
CB_DB_CERT=
CB_DB_SSL=
CB_DB_SSL_KEY=
CB_DB_SSL_CERT=

# set to 'mysql' or 'postgres'
CB_DB_DIALECT=mysql

# Redis connection parameters
CB_REDIS_HOST=localhost
CB_REDIS_PORT=6379
CB_REDIS_PASSWORD=

### A secret string which will be used to encrypt your data
# [deprecated] This is not used anymore, but it's kept here for legacy reasons
CB_SECRET=change_to_random_string
## The new secret key used for encrypting data
CB_ENCRYPTION_KEY=

### The API host string used on the server app
# Set this to 0.0.0.0 when using docker AND proxying it through apache or nginx
CB_API_HOST=localhost
CB_API_PORT=4019

### Used in the client app

# The address where the front-end app sits and port
VITE_APP_CLIENT_HOST=http://localhost:4018
VITE_APP_CLIENT_PORT=4018

# The address where the backend API sits
VITE_APP_API_HOST=http://localhost:4019

# Email connection parameters for sending team signup invites and forgot password emails
CB_MAIL_HOST=smtp.gmail.com
CB_MAIL_USER=
CB_MAIL_PASS=
CB_MAIL_PORT=465
CB_MAIL_SECURE=true
# The admin email from which all the emails are delivered
CB_ADMIN_MAIL=<EMAIL>

# Restrict users from having their own team
# 0 - users will have their own team
# 1 - users can only be part of teams they are invited to
CB_RESTRICT_TEAMS=0

# Restrict users from creating their own accounts unless invited
# 0 - users can create their own accounts
# 1 - users can only create accounts with invite links
CB_RESTRICT_SIGNUP=0

# Google cloud client ID information (To enable Google integrations)
CB_GOOGLE_CLIENT_ID=
CB_GOOGLE_CLIENT_SECRET=

# OpenAI API key
CB_OPENAI_API_KEY=
CB_OPENAI_MODEL=gpt-4o-mini

########################################

#### DEVELOPMENT VARS ####

# Database connection parameters
CB_DB_NAME_DEV=chartbrewdev
CB_DB_USERNAME_DEV=root
CB_DB_PASSWORD_DEV=
CB_DB_HOST_DEV=localhost
CB_DB_PORT_DEV=3306
CB_DB_CERT_DEV=
CB_DB_SSL_DEV=
CB_DB_SSL_KEY_DEV=
CB_DB_SSL_CERT_DEV=

# set to 'mysql' or 'postgres'
CB_DB_DIALECT_DEV=mysql

# Redis connection parameters
CB_REDIS_HOST_DEV=localhost
CB_REDIS_PORT_DEV=6379
CB_REDIS_PASSWORD_DEV=

# A secret string which will be used to encrypt your data
# [deprecated] This is not used anymore, but it's kept here for legacy reasons
CB_SECRET_DEV=change_to_random_string
# The new secret key used for encrypting data
CB_ENCRYPTION_KEY_DEV=

# The API host string used on the server app
CB_API_HOST_DEV=localhost
CB_API_PORT_DEV=4019

# Used in the client app
VITE_APP_CLIENT_HOST_DEV=http://localhost:4018
VITE_APP_CLIENT_PORT_DEV=4018
VITE_APP_API_HOST_DEV=http://localhost:4019

# Email connection parameters for sending team signup invites and forgot password emails
CB_MAIL_HOST_DEV=smtp.gmail.com
CB_MAIL_USER_DEV=
CB_MAIL_PASS_DEV=
CB_MAIL_PORT_DEV=465
CB_MAIL_SECURE_DEV=true
# The admin email from which all the emails are delivered
CB_ADMIN_MAIL_DEV=<EMAIL>

# Restrict users from having their own team
# 0 - users will have their own team
# 1 - users can only be part of teams they are invited to
CB_RESTRICT_TEAMS_DEV=0

# Restrict users from creating their own accounts unless invited
# 0 - users can create their own accounts
# 1 - users can only create accounts with invite links
CB_RESTRICT_SIGNUP_DEV=0

# Google cloud client ID information (To enable Google integrations)
CB_GOOGLE_CLIENT_ID_DEV=
CB_GOOGLE_CLIENT_SECRET_DEV=

# OpenAI API key
CB_OPENAI_API_KEY_DEV=
CB_OPENAI_MODEL_DEV=gpt-4o-mini


########################################
