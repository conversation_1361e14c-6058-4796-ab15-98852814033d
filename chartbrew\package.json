{"name": "chartbrew", "version": "v4.1.0", "description": "", "main": "index.js", "workspaces": [], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "setup": "npm run prepareSettings && npm install && cd client && npm install && cd ../server/ && npm install && npx playwright install", "prepareSettings": "echo n | cp -vipr .env-template .env | true && cd client && echo n | cp -vipr src/config/settings.template.js src/config/settings.js | true", "client": "cd client && npm start", "server": "cd server && npm run start-dev"}, "repository": {"type": "git", "url": "git+https://github.com/chartbrew/chartbrew.git"}, "author": {"email": "<EMAIL>", "name": "Chartbrew"}, "license": "MIT", "bugs": {"url": "https://github.com/charbrew/chartbrew/issues"}, "homepage": "https://github.com/chartbrew/chartbrew", "devDependencies": {"vuepress": "^1.9.7"}}