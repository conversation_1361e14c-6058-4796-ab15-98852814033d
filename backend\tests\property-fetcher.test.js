/**
 * Test file for property fetcher helper functions
 * This tests the helper functions without requiring a running Strapi instance
 */

const { 
  POPULATE_CONFIGS, 
  parsePagination, 
  parseFilters, 
  parseSort 
} = require('../dist/src/api/property/helpers/propertyFetcher');

// Test parsePagination function
function testParsePagination() {
  console.log('Testing parsePagination...');
  
  // Test with default values
  const result1 = parsePagination({});
  console.log('Default pagination:', result1);
  console.assert(result1.page === 1, 'Default page should be 1');
  console.assert(result1.pageSize === 20, 'Default pageSize should be 20');
  
  // Test with custom values
  const result2 = parsePagination({
    pagination: { page: '3', pageSize: '10' }
  });
  console.log('Custom pagination:', result2);
  console.assert(result2.page === 3, 'Custom page should be 3');
  console.assert(result2.pageSize === 10, 'Custom pageSize should be 10');
  
  // Test with custom defaults
  const result3 = parsePagination({}, { page: 2, pageSize: 15 });
  console.log('Custom defaults:', result3);
  console.assert(result3.page === 2, 'Custom default page should be 2');
  console.assert(result3.pageSize === 15, 'Custom default pageSize should be 15');
  
  console.log('✅ parsePagination tests passed\n');
}

// Test parseFilters function
function testParseFilters() {
  console.log('Testing parseFilters...');
  
  // Test with empty query
  const result1 = parseFilters({});
  console.log('Empty filters:', result1);
  console.assert(Object.keys(result1).length === 0, 'Empty filters should return empty object');
  
  // Test with query filters
  const result2 = parseFilters({
    filters: { city: 'New York', price: { $gte: 100000 } }
  });
  console.log('Query filters:', result2);
  console.assert(result2.city === 'New York', 'City filter should be preserved');
  console.assert(result2.price.$gte === 100000, 'Price filter should be preserved');
  
  // Test with additional filters
  const result3 = parseFilters(
    { filters: { city: 'New York' } },
    { owner: 123, featured: true }
  );
  console.log('Combined filters:', result3);
  console.assert(result3.city === 'New York', 'Query filter should be preserved');
  console.assert(result3.owner === 123, 'Additional filter should be added');
  console.assert(result3.featured === true, 'Additional filter should be added');
  
  console.log('✅ parseFilters tests passed\n');
}

// Test parseSort function
function testParseSort() {
  console.log('Testing parseSort...');
  
  // Test with default sort
  const result1 = parseSort({});
  console.log('Default sort:', result1);
  console.assert(result1.createdAt === 'desc', 'Default sort should be createdAt desc');
  
  // Test with custom sort
  const result2 = parseSort({
    sort: { price: 'asc', createdAt: 'desc' }
  });
  console.log('Custom sort:', result2);
  console.assert(result2.price === 'asc', 'Custom sort should be preserved');
  console.assert(result2.createdAt === 'desc', 'Custom sort should be preserved');
  
  // Test with custom default
  const result3 = parseSort({}, { title: 'asc' });
  console.log('Custom default sort:', result3);
  console.assert(result3.title === 'asc', 'Custom default sort should be used');
  
  console.log('✅ parseSort tests passed\n');
}

// Test POPULATE_CONFIGS
function testPopulateConfigs() {
  console.log('Testing POPULATE_CONFIGS...');
  
  // Test that all expected configs exist
  const expectedConfigs = ['minimal', 'list', 'detailed', 'dashboard', 'featured'];
  expectedConfigs.forEach(config => {
    console.assert(POPULATE_CONFIGS[config], `${config} config should exist`);
  });
  
  // Test list config structure
  const listConfig = POPULATE_CONFIGS.list;
  console.assert(listConfig.images === true, 'List config should include images');
  console.assert(listConfig.owner && listConfig.owner.fields, 'List config should include owner fields');
  console.assert(listConfig.agent && listConfig.agent.fields, 'List config should include agent fields');
  console.assert(listConfig.project && listConfig.project.fields, 'List config should include project fields');
  
  // Test detailed config structure
  const detailedConfig = POPULATE_CONFIGS.detailed;
  console.assert(detailedConfig.images === true, 'Detailed config should include images');
  console.assert(detailedConfig.floorPlan === true, 'Detailed config should include floorPlan');
  console.assert(detailedConfig.owner && detailedConfig.owner.fields, 'Detailed config should include owner fields');
  
  console.log('✅ POPULATE_CONFIGS tests passed\n');
}

// Run all tests
function runTests() {
  console.log('🧪 Running Property Fetcher Helper Tests\n');
  
  try {
    testParsePagination();
    testParseFilters();
    testParseSort();
    testPopulateConfigs();
    
    console.log('🎉 All tests passed successfully!');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testParsePagination,
  testParseFilters,
  testParseSort,
  testPopulateConfigs
};
