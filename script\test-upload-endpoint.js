const axios = require('axios');

const API_URL = 'http://localhost:1337/api';

async function testUploadEndpoint() {
  console.log('📁 Testing Upload Endpoint Configuration...\n');

  try {
    // Login first
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    const token = loginResponse.data.jwt;
    console.log('✅ Authentication successful');
    
    // Test different upload endpoint variations
    console.log('\n1. Testing upload endpoint variations...');
    
    const endpoints = [
      '/upload',
      '/upload/files',
      '/uploads',
      '/media'
    ];
    
    for (const endpoint of endpoints) {
      try {
        console.log(`   Testing ${endpoint}...`);
        const response = await axios.get(`${API_URL}${endpoint}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`   ✅ ${endpoint}: ${response.status} - Available`);
      } catch (error) {
        if (error.response?.status === 405) {
          console.log(`   ✅ ${endpoint}: 405 Method Not Allowed (GET) - Endpoint exists, needs POST`);
        } else if (error.response?.status === 403) {
          console.log(`   ⚠️  ${endpoint}: 403 Forbidden - Endpoint exists, permission issue`);
        } else if (error.response?.status === 404) {
          console.log(`   ❌ ${endpoint}: 404 Not Found`);
        } else {
          console.log(`   ⚠️  ${endpoint}: ${error.response?.status} - ${error.response?.statusText}`);
        }
      }
    }
    
    // Test Strapi's built-in upload endpoint
    console.log('\n2. Testing Strapi built-in upload endpoint...');
    
    try {
      // Test with OPTIONS to see if CORS is configured
      const optionsResponse = await axios.options(`${API_URL}/upload`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log(`✅ Upload endpoint CORS configured: ${optionsResponse.status}`);
    } catch (optionsError) {
      console.log(`⚠️  Upload endpoint CORS: ${optionsError.response?.status}`);
    }
    
    // Check if upload plugin is enabled
    console.log('\n3. Checking upload plugin configuration...');
    
    try {
      // Try to access upload plugin info
      const pluginResponse = await axios.get(`${API_URL}/upload/settings`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Upload plugin settings accessible');
    } catch (pluginError) {
      console.log(`⚠️  Upload plugin settings: ${pluginError.response?.status}`);
    }
    
    // Test file upload with a simple text file simulation
    console.log('\n4. Testing file upload simulation...');
    
    try {
      // Create a simple form data simulation
      const FormData = require('form-data');
      const form = new FormData();
      
      // Add a simple text buffer as a file
      const textBuffer = Buffer.from('This is a test file for upload testing', 'utf8');
      form.append('files', textBuffer, {
        filename: 'test.txt',
        contentType: 'text/plain'
      });
      
      const uploadResponse = await axios.post(`${API_URL}/upload`, form, {
        headers: {
          'Authorization': `Bearer ${token}`,
          ...form.getHeaders()
        }
      });
      
      console.log('✅ File upload working!');
      console.log(`   Uploaded file ID: ${uploadResponse.data[0]?.id}`);
      console.log(`   File URL: ${uploadResponse.data[0]?.url}`);
      
      // Clean up - delete the test file
      if (uploadResponse.data[0]?.id) {
        try {
          await axios.delete(`${API_URL}/upload/files/${uploadResponse.data[0].id}`, {
            headers: { 'Authorization': `Bearer ${token}` }
          });
          console.log('✅ Test file cleaned up');
        } catch (deleteError) {
          console.log('⚠️  Could not delete test file (this is okay)');
        }
      }
      
    } catch (uploadError) {
      console.log(`❌ File upload failed: ${uploadError.response?.status}`);
      if (uploadError.response?.data) {
        console.log('   Error details:', uploadError.response.data);
      }
    }
    
    console.log('\n📋 Upload Endpoint Summary:');
    console.log('   - Standard Strapi upload endpoint: /api/upload');
    console.log('   - Requires POST method with multipart/form-data');
    console.log('   - Requires authentication token');
    console.log('   - Returns array of uploaded file objects');
    
    console.log('\n💡 Frontend Integration:');
    console.log('   - Use FormData for file uploads');
    console.log('   - Include Authorization header');
    console.log('   - Handle multiple file uploads');
    console.log('   - Process returned file URLs for property images');
    
  } catch (error) {
    console.log('❌ Upload test failed:', error.message);
  }
}

testUploadEndpoint().catch(console.error);
