# View Count Analytics System - Technical Implementation Guide

## 📊 Analytics Architecture Overview

### **Core Analytics Interface**
```typescript
interface ViewAnalytics {
  totalViews: number;
  uniqueProperties: Set<string>;
  botRequestsBlocked: number;
  spamRequestsBlocked: number;
  dailyStats: { [date: string]: number };
  hourlyStats: { [hour: string]: number };
}
```

### **Data Collection Points**

1. **View Tracking**: Every legitimate view increments analytics
2. **Bot Detection**: Blocked bot requests are counted
3. **Spam Protection**: Throttled requests are tracked
4. **Temporal Data**: Daily and hourly breakdowns
5. **Property Ranking**: Most viewed properties tracking

---

## 🔧 Implementation Details

### **Analytics Initialization**
```typescript
private analytics: ViewAnalytics = {
  totalViews: 0,
  uniqueProperties: new Set(),
  botRequestsBlocked: 0,
  spamRequestsBlocked: 0,
  dailyStats: {},
  hourlyStats: {}
};
```

### **Data Collection Methods**

#### **1. View Count Analytics**
```typescript
private trackAnalytics(propertyId: string, incrementBy: number, oldViews: number, newViews: number): void {
  try {
    const now = new Date();
    const dateKey = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const hourKey = `${dateKey}-${now.getHours().toString().padStart(2, '0')}`; // YYYY-MM-DD-HH

    // Update analytics
    this.analytics.totalViews += incrementBy;
    this.analytics.uniqueProperties.add(propertyId);
    this.analytics.dailyStats[dateKey] = (this.analytics.dailyStats[dateKey] || 0) + incrementBy;
    this.analytics.hourlyStats[hourKey] = (this.analytics.hourlyStats[hourKey] || 0) + incrementBy;

    // Clean up old analytics data (keep last 30 days)
    this.cleanupOldAnalytics();
  } catch (error) {
    console.error('Error tracking analytics:', error);
  }
}
```

#### **2. Bot Detection Analytics**
```typescript
// In trackView method
const isBotRequest = this.detectBotTraffic(userAgent, ip);
if (isBotRequest) {
  console.log(`🤖 Bot traffic detected for property ${propertyId} - skipping increment`);
  this.analytics.botRequestsBlocked++;
  return false;
}
```

#### **3. Spam Protection Analytics**
```typescript
// In trackView method
if (isSpamView) {
  console.log(`⚠️  Rapid view detected - throttling but maintaining count display`);
  this.analytics.spamRequestsBlocked++;
  return false;
}
```

### **Data Cleanup and Retention**
```typescript
private cleanupOldAnalytics(): void {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const cutoffDate = thirtyDaysAgo.toISOString().split('T')[0];

  // Clean daily stats
  Object.keys(this.analytics.dailyStats).forEach(date => {
    if (date < cutoffDate) {
      delete this.analytics.dailyStats[date];
    }
  });

  // Clean hourly stats
  Object.keys(this.analytics.hourlyStats).forEach(hour => {
    if (hour < cutoffDate) {
      delete this.analytics.hourlyStats[hour];
    }
  });
}
```

---

## 📈 Analytics API Endpoints

### **View Statistics Endpoint**

**Route**: `GET /api/properties/view-stats`
**Handler**: `property.getViewStats`

```typescript
// In property controller
async getViewStats(ctx) {
  try {
    const stats = viewTracker.getStats();
    return { data: stats };
  } catch (error) {
    console.error('Error getting view stats:', error);
    ctx.throw(500, 'Failed to get view statistics');
  }
}
```

### **Enhanced Statistics Response**
```typescript
getStats(): any {
  const now = new Date();
  const today = now.toISOString().split('T')[0];
  const currentHour = `${today}-${now.getHours().toString().padStart(2, '0')}`;

  return {
    // System health
    queueLength: this.viewQueue.length,
    cacheSize: Object.keys(this.viewCache).length,
    sessionCount: Object.keys(this.sessionTracker).length,
    processing: this.processing,
    uptime: Date.now() - (this.startTime || Date.now()),
    
    // Analytics
    analytics: {
      totalViews: this.analytics.totalViews,
      uniqueProperties: this.analytics.uniqueProperties.size,
      botRequestsBlocked: this.analytics.botRequestsBlocked,
      spamRequestsBlocked: this.analytics.spamRequestsBlocked,
      viewsToday: this.analytics.dailyStats[today] || 0,
      viewsThisHour: this.analytics.hourlyStats[currentHour] || 0,
      dailyTrend: this.getDailyTrend(),
      topViewedProperties: this.getTopViewedProperties()
    },
    
    // Configuration
    config: {
      batchSize: this.batchSize,
      flushInterval: this.flushInterval,
      sessionTimeout: this.sessionTimeout,
      displayDelay: this.displayDelay,
      cacheTimeout: this.cacheTimeout
    }
  };
}
```

---

## 📊 Trend Analysis Features

### **Daily Trend Analysis**
```typescript
private getDailyTrend(): { date: string; views: number }[] {
  const trend = [];
  const now = new Date();
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    const dateKey = date.toISOString().split('T')[0];
    trend.push({
      date: dateKey,
      views: this.analytics.dailyStats[dateKey] || 0
    });
  }
  
  return trend;
}
```

### **Top Properties Ranking**
```typescript
private getTopViewedProperties(): { propertyId: string; views: number }[] {
  return Object.entries(this.viewCache)
    .map(([propertyId, cache]) => ({ propertyId, views: cache.count }))
    .sort((a, b) => b.views - a.views)
    .slice(0, 10);
}
```

---

## 🔍 Monitoring and Alerting

### **Performance Metrics**

1. **Queue Health**
   - Monitor `queueLength` for backlog issues
   - Alert if queue exceeds threshold (>100 items)

2. **Processing Status**
   - Monitor `processing` flag for stuck batches
   - Alert if processing takes >30 seconds

3. **Cache Efficiency**
   - Monitor `cacheSize` for memory usage
   - Track cache hit/miss ratios

4. **Session Management**
   - Monitor `sessionCount` for memory leaks
   - Alert if sessions exceed expected limits

### **Business Metrics**

1. **View Trends**
   - Daily view count trends
   - Hour-over-hour growth rates
   - Property popularity rankings

2. **Security Metrics**
   - Bot request blocking rates
   - Spam detection effectiveness
   - Unusual traffic patterns

### **Sample Monitoring Queries**

```bash
# Check system health
curl -s "http://localhost:1337/api/properties/view-stats" | jq '.data.queueLength'

# Monitor daily trends
curl -s "http://localhost:1337/api/properties/view-stats" | jq '.data.analytics.dailyTrend'

# Check bot blocking effectiveness
curl -s "http://localhost:1337/api/properties/view-stats" | jq '.data.analytics.botRequestsBlocked'
```

---

## 🚀 Performance Optimizations

### **Memory Management**

1. **Automatic Cleanup**
   - 30-day data retention
   - Expired session removal
   - Cache size limits

2. **Efficient Data Structures**
   - Set for unique properties tracking
   - Object maps for fast lookups
   - Minimal memory footprint

### **Processing Efficiency**

1. **Batch Processing**
   - Configurable batch sizes
   - Asynchronous processing
   - Error isolation

2. **Cache Strategy**
   - 1-minute cache timeout
   - Intelligent refresh logic
   - Pending increment tracking

---

## 📋 Analytics Data Schema

### **Daily Stats Format**
```json
{
  "2025-07-09": 45,
  "2025-07-08": 32,
  "2025-07-07": 28
}
```

### **Hourly Stats Format**
```json
{
  "2025-07-09-14": 12,
  "2025-07-09-15": 8,
  "2025-07-09-16": 15
}
```

### **Top Properties Format**
```json
[
  {"propertyId": "abc123", "views": 156},
  {"propertyId": "def456", "views": 142},
  {"propertyId": "ghi789", "views": 98}
]
```

---

## 🔧 Configuration Options

### **Tunable Parameters**
```typescript
private batchSize = 10;           // Views per batch
private flushInterval = 5000;     // 5 seconds
private sessionTimeout = 30000;   // 30 seconds
private displayDelay = 60000;     // 1 minute
private cacheTimeout = 60000;     // 1 minute
```

### **Recommended Settings**

**High Traffic Sites**:
- `batchSize`: 20-50
- `flushInterval`: 2000-3000ms
- `sessionTimeout`: 15000ms

**Low Traffic Sites**:
- `batchSize`: 5-10
- `flushInterval`: 10000ms
- `sessionTimeout`: 60000ms

**Development**:
- `batchSize`: 1-5
- `flushInterval`: 1000ms
- `sessionTimeout`: 5000ms

---

## 🧪 Testing Analytics

### **Unit Tests**
```typescript
describe('ViewTracker Analytics', () => {
  test('should track daily stats correctly', () => {
    // Test daily stats accumulation
  });
  
  test('should cleanup old analytics data', () => {
    // Test 30-day retention
  });
  
  test('should rank properties correctly', () => {
    // Test top properties ranking
  });
});
```

### **Integration Tests**
```typescript
describe('Analytics API', () => {
  test('should return comprehensive stats', async () => {
    const response = await request(app)
      .get('/api/properties/view-stats')
      .expect(200);
    
    expect(response.body.data.analytics).toBeDefined();
    expect(response.body.data.analytics.totalViews).toBeGreaterThanOrEqual(0);
  });
});
```

---

*This technical guide provides the foundation for understanding and extending the analytics system.*
