const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Real estate image URLs from Unsplash (free to use)
const realEstateImages = [
  // Villas
  {
    url: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop&auto=format',
    filename: 'villa-luxury-1.jpg',
    type: 'villa'
  },
  {
    url: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop&auto=format',
    filename: 'villa-modern-2.jpg',
    type: 'villa'
  },
  {
    url: 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800&h=600&fit=crop&auto=format',
    filename: 'villa-beachfront-3.jpg',
    type: 'villa'
  },
  
  // Apartments
  {
    url: 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop&auto=format',
    filename: 'apartment-modern-1.jpg',
    type: 'apartment'
  },
  {
    url: 'https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=800&h=600&fit=crop&auto=format',
    filename: 'apartment-luxury-2.jpg',
    type: 'apartment'
  },
  {
    url: 'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop&auto=format',
    filename: 'apartment-city-3.jpg',
    type: 'apartment'
  },
  
  // Penthouses
  {
    url: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop&auto=format',
    filename: 'penthouse-luxury-1.jpg',
    type: 'penthouse'
  },
  {
    url: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800&h=600&fit=crop&auto=format',
    filename: 'penthouse-view-2.jpg',
    type: 'penthouse'
  },
  
  // Studios
  {
    url: 'https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?w=800&h=600&fit=crop&auto=format',
    filename: 'studio-modern-1.jpg',
    type: 'studio'
  },
  {
    url: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&h=600&fit=crop&auto=format',
    filename: 'studio-compact-2.jpg',
    type: 'studio'
  },
  
  // Townhouses
  {
    url: 'https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?w=800&h=600&fit=crop&auto=format',
    filename: 'townhouse-family-1.jpg',
    type: 'townhouse'
  },
  {
    url: 'https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?w=800&h=600&fit=crop&auto=format',
    filename: 'townhouse-garden-2.jpg',
    type: 'townhouse'
  },
  
  // Commercial
  {
    url: 'https://images.unsplash.com/photo-1600607688969-a5bfcd646154?w=800&h=600&fit=crop&auto=format',
    filename: 'commercial-office-1.jpg',
    type: 'commercial'
  },
  {
    url: 'https://images.unsplash.com/photo-1600607688960-e095ff8d5c6e?w=800&h=600&fit=crop&auto=format',
    filename: 'commercial-retail-2.jpg',
    type: 'commercial'
  },
  
  // General luxury/beachfront
  {
    url: 'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&h=600&fit=crop&auto=format',
    filename: 'beachfront-luxury-1.jpg',
    type: 'beachfront'
  },
  {
    url: 'https://images.unsplash.com/photo-1600047509807-ba8f99d2cdde?w=800&h=600&fit=crop&auto=format',
    filename: 'beachfront-villa-2.jpg',
    type: 'beachfront'
  },
  {
    url: 'https://images.unsplash.com/photo-1600566752355-35792bedcfea?w=800&h=600&fit=crop&auto=format',
    filename: 'luxury-interior-1.jpg',
    type: 'luxury'
  },
  {
    url: 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800&h=600&fit=crop&auto=format',
    filename: 'luxury-exterior-2.jpg',
    type: 'luxury'
  },
  {
    url: 'https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?w=800&h=600&fit=crop&auto=format',
    filename: 'modern-architecture-1.jpg',
    type: 'modern'
  },
  {
    url: 'https://images.unsplash.com/photo-1600585154526-990dced4db0d?w=800&h=600&fit=crop&auto=format',
    filename: 'modern-design-2.jpg',
    type: 'modern'
  }
];

// Function to download image from URL
async function downloadImage(url, filepath) {
  try {
    console.log(`📥 Downloading: ${path.basename(filepath)}`);
    
    const response = await axios({
      method: 'GET',
      url: url,
      responseType: 'stream',
      timeout: 30000, // 30 second timeout
      headers: {
        'User-Agent': 'Real Estate Sample Data Generator'
      }
    });
    
    const writer = fs.createWriteStream(filepath);
    response.data.pipe(writer);
    
    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log(`   ✅ Downloaded: ${path.basename(filepath)}`);
        resolve();
      });
      writer.on('error', reject);
    });
  } catch (error) {
    console.error(`   ❌ Failed to download ${path.basename(filepath)}: ${error.message}`);
    throw error;
  }
}

// Main function to download all images
async function downloadRealEstateImages() {
  console.log('🏠 Downloading Real Estate Images from Free Sources\n');
  console.log('📍 Source: Unsplash (Free to use under Unsplash License)');
  console.log(`🖼️  Total images to download: ${realEstateImages.length}\n`);

  // Create images directory if it doesn't exist
  const imagesDir = path.join(__dirname, 'images');
  if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir);
    console.log('📁 Created images directory\n');
  }

  let successCount = 0;
  let failCount = 0;

  // Download images with delay to be respectful to the server
  for (let i = 0; i < realEstateImages.length; i++) {
    const image = realEstateImages[i];
    const filepath = path.join(imagesDir, image.filename);
    
    // Skip if file already exists
    if (fs.existsSync(filepath)) {
      console.log(`⏭️  Skipping: ${image.filename} (already exists)`);
      successCount++;
      continue;
    }

    try {
      await downloadImage(image.url, filepath);
      successCount++;
      
      // Add delay between downloads to be respectful
      if (i < realEstateImages.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
      }
    } catch (error) {
      failCount++;
    }
  }

  console.log('\n📊 Download Summary:');
  console.log(`   ✅ Successfully downloaded: ${successCount}/${realEstateImages.length} images`);
  if (failCount > 0) {
    console.log(`   ❌ Failed downloads: ${failCount}`);
  }

  // Show breakdown by type
  console.log('\n🏠 Images by Property Type:');
  const typeCount = {};
  realEstateImages.forEach(img => {
    typeCount[img.type] = (typeCount[img.type] || 0) + 1;
  });
  
  Object.entries(typeCount).forEach(([type, count]) => {
    console.log(`   ${type}: ${count} images`);
  });

  console.log('\n🎉 Real estate images ready!');
  console.log('\n📝 Next Steps:');
  console.log('   1. Run: node create-test-property.js');
  console.log('   2. The script will automatically use these real images');
  console.log('   3. Each property will get 1-3 random images from the collection');
  
  console.log('\n💡 Tips:');
  console.log('   • You can add more images to the ./images/ folder');
  console.log('   • Supported formats: .jpg, .jpeg, .png, .webp');
  console.log('   • Images are automatically resized by Strapi');
}

// Run the download
downloadRealEstateImages().catch(console.error);
