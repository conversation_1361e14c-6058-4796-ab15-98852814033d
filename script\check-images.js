const axios = require('axios');

async function checkPropertyImages() {
  try {
    console.log('🔍 Checking property images...');

    // First, let's just check if we can get properties at all
    const publicResponse = await axios.get('http://localhost:1337/api/properties?populate=images');
    console.log(`📊 Found ${publicResponse.data.data.length} total properties`);

    if (publicResponse.data.data.length > 0) {
      const firstProperty = publicResponse.data.data[0];
      console.log(`\n🏠 First property: ${firstProperty.title}`);
      console.log(`📸 Images count: ${firstProperty.images ? firstProperty.images.length : 0}`);

      if (firstProperty.images && firstProperty.images.length > 0) {
        console.log('🖼️  Image details:');
        firstProperty.images.forEach((img, index) => {
          console.log(`   ${index + 1}. ID: ${img.id}, URL: ${img.url}`);
        });
      } else {
        console.log('❌ No images found for this property');
      }
    }

    // Now try with authentication
    console.log('\n🔐 Trying with authentication...');
    const loginResponse = await axios.post('http://localhost:1337/api/auth/local', {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    const token = loginResponse.data.jwt;
    console.log('✅ Logged in successfully');

    const myPropertiesResponse = await axios.get('http://localhost:1337/api/properties/my-properties?populate=images', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const myProperties = myPropertiesResponse.data.data;
    console.log(`📊 Found ${myProperties.length} user properties`);

    if (myProperties.length > 0) {
      const userProperty = myProperties[0];
      console.log(`\n🏠 User property: ${userProperty.title}`);
      console.log(`📸 Images count: ${userProperty.images ? userProperty.images.length : 0}`);

      if (userProperty.images && userProperty.images.length > 0) {
        console.log('🖼️  Image details:');
        userProperty.images.forEach((img, index) => {
          console.log(`   ${index + 1}. ID: ${img.id}, URL: ${img.url}`);
        });
      } else {
        console.log('❌ No images found for user property');
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    if (error.response?.data?.error) {
      console.error('Error details:', error.response.data.error);
    }
  }
}

checkPropertyImages();
