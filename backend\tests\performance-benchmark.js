/**
 * Performance Benchmark Script
 * Measures and compares API endpoint performance
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

const API_BASE_URL = 'http://localhost:1337/api';

class PerformanceBenchmark {
  constructor() {
    this.results = {};
    this.iterations = 10; // Number of test iterations
  }

  async measureEndpoint(name, url, params = {}) {
    console.log(`\n🔍 Benchmarking: ${name}`);
    console.log(`URL: ${url}`);
    
    const times = [];
    const memorySamples = [];
    
    for (let i = 0; i < this.iterations; i++) {
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const memBefore = process.memoryUsage();
      const startTime = performance.now();
      
      try {
        const response = await axios({
          url: `${API_BASE_URL}${url}`,
          params,
          timeout: 10000
        });
        
        const endTime = performance.now();
        const memAfter = process.memoryUsage();
        
        const responseTime = endTime - startTime;
        const memoryDelta = memAfter.heapUsed - memBefore.heapUsed;
        
        times.push(responseTime);
        memorySamples.push(memoryDelta);
        
        // Log progress
        process.stdout.write(`${i + 1}/${this.iterations} `);
        
        // Validate response structure
        if (!response.data || !response.data.data) {
          throw new Error('Invalid response structure');
        }
        
      } catch (error) {
        console.error(`\n❌ Error in iteration ${i + 1}:`, error.message);
        times.push(null);
        memorySamples.push(null);
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Calculate statistics
    const validTimes = times.filter(t => t !== null);
    const validMemory = memorySamples.filter(m => m !== null);
    
    if (validTimes.length === 0) {
      console.log('\n❌ All requests failed');
      return null;
    }
    
    const stats = {
      name,
      url,
      params,
      iterations: validTimes.length,
      responseTime: {
        min: Math.min(...validTimes),
        max: Math.max(...validTimes),
        avg: validTimes.reduce((a, b) => a + b, 0) / validTimes.length,
        median: this.calculateMedian(validTimes),
        p95: this.calculatePercentile(validTimes, 95),
        p99: this.calculatePercentile(validTimes, 99)
      },
      memory: {
        avgDelta: validMemory.reduce((a, b) => a + b, 0) / validMemory.length,
        maxDelta: Math.max(...validMemory),
        minDelta: Math.min(...validMemory)
      }
    };
    
    this.results[name] = stats;
    this.printStats(stats);
    
    return stats;
  }
  
  calculateMedian(arr) {
    const sorted = [...arr].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 !== 0 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2;
  }
  
  calculatePercentile(arr, percentile) {
    const sorted = [...arr].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index];
  }
  
  printStats(stats) {
    console.log(`\n📊 Results for ${stats.name}:`);
    console.log(`   Response Time (ms):`);
    console.log(`     Min: ${stats.responseTime.min.toFixed(2)}ms`);
    console.log(`     Avg: ${stats.responseTime.avg.toFixed(2)}ms`);
    console.log(`     Max: ${stats.responseTime.max.toFixed(2)}ms`);
    console.log(`     Median: ${stats.responseTime.median.toFixed(2)}ms`);
    console.log(`     95th percentile: ${stats.responseTime.p95.toFixed(2)}ms`);
    console.log(`     99th percentile: ${stats.responseTime.p99.toFixed(2)}ms`);
    console.log(`   Memory Usage:`);
    console.log(`     Avg Delta: ${(stats.memory.avgDelta / 1024 / 1024).toFixed(2)}MB`);
    console.log(`     Max Delta: ${(stats.memory.maxDelta / 1024 / 1024).toFixed(2)}MB`);
  }
  
  async runBenchmarkSuite() {
    console.log('🚀 Starting Property API Performance Benchmark');
    console.log(`Running ${this.iterations} iterations per endpoint\n`);
    
    const startTime = performance.now();
    
    // Test scenarios
    const scenarios = [
      {
        name: 'Basic Properties List',
        url: '/properties',
        params: { 'pagination[pageSize]': 20 }
      },
      {
        name: 'Small Page Size',
        url: '/properties',
        params: { 'pagination[pageSize]': 5 }
      },
      {
        name: 'Large Page Size',
        url: '/properties',
        params: { 'pagination[pageSize]': 50 }
      },
      {
        name: 'Filtered Properties (Apartments)',
        url: '/properties',
        params: { 
          'filters[propertyType]': 'apartment',
          'pagination[pageSize]': 20 
        }
      },
      {
        name: 'Complex Filter (Price + Type)',
        url: '/properties',
        params: { 
          'filters[propertyType]': 'apartment',
          'filters[price][$gte]': 100000,
          'filters[price][$lte]': 1000000,
          'pagination[pageSize]': 20 
        }
      },
      {
        name: 'Featured Properties',
        url: '/properties/featured',
        params: {}
      },
      {
        name: 'Pagination - Page 1',
        url: '/properties',
        params: { 
          'pagination[page]': 1,
          'pagination[pageSize]': 10 
        }
      },
      {
        name: 'Pagination - Page 5',
        url: '/properties',
        params: { 
          'pagination[page]': 5,
          'pagination[pageSize]': 10 
        }
      }
    ];
    
    // Run benchmarks
    for (const scenario of scenarios) {
      await this.measureEndpoint(scenario.name, scenario.url, scenario.params);
      
      // Brief pause between different endpoints
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    // Generate summary report
    this.generateSummaryReport(totalTime);
  }
  
  generateSummaryReport(totalTime) {
    console.log('\n' + '='.repeat(80));
    console.log('📋 PERFORMANCE BENCHMARK SUMMARY REPORT');
    console.log('='.repeat(80));
    
    console.log(`\n⏱️  Total benchmark time: ${(totalTime / 1000).toFixed(2)} seconds`);
    console.log(`🔄 Total requests made: ${Object.keys(this.results).length * this.iterations}`);
    
    // Sort results by average response time
    const sortedResults = Object.values(this.results)
      .sort((a, b) => a.responseTime.avg - b.responseTime.avg);
    
    console.log('\n🏆 FASTEST TO SLOWEST ENDPOINTS:');
    console.log('-'.repeat(80));
    
    sortedResults.forEach((result, index) => {
      const rank = index + 1;
      const emoji = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : '  ';
      console.log(`${emoji} ${rank}. ${result.name}`);
      console.log(`     Avg: ${result.responseTime.avg.toFixed(2)}ms | ` +
                  `95th: ${result.responseTime.p95.toFixed(2)}ms | ` +
                  `Memory: ${(result.memory.avgDelta / 1024 / 1024).toFixed(2)}MB`);
    });
    
    // Performance insights
    console.log('\n💡 PERFORMANCE INSIGHTS:');
    console.log('-'.repeat(80));
    
    const fastest = sortedResults[0];
    const slowest = sortedResults[sortedResults.length - 1];
    
    console.log(`✅ Fastest endpoint: ${fastest.name} (${fastest.responseTime.avg.toFixed(2)}ms avg)`);
    console.log(`⚠️  Slowest endpoint: ${slowest.name} (${slowest.responseTime.avg.toFixed(2)}ms avg)`);
    
    const avgResponseTime = sortedResults.reduce((sum, r) => sum + r.responseTime.avg, 0) / sortedResults.length;
    console.log(`📊 Average response time across all endpoints: ${avgResponseTime.toFixed(2)}ms`);
    
    // Performance recommendations
    console.log('\n🎯 RECOMMENDATIONS:');
    console.log('-'.repeat(80));
    
    if (avgResponseTime < 200) {
      console.log('✅ Excellent performance! All endpoints are responding quickly.');
    } else if (avgResponseTime < 400) {
      console.log('👍 Good performance. Consider optimizing slower endpoints.');
    } else {
      console.log('⚠️  Performance needs improvement. Focus on optimization.');
    }
    
    // Identify endpoints that need attention
    const slowEndpoints = sortedResults.filter(r => r.responseTime.avg > 300);
    if (slowEndpoints.length > 0) {
      console.log('\n🔧 Endpoints needing optimization:');
      slowEndpoints.forEach(endpoint => {
        console.log(`   - ${endpoint.name}: ${endpoint.responseTime.avg.toFixed(2)}ms avg`);
      });
    }
    
    console.log('\n' + '='.repeat(80));
  }
}

// Run benchmark if this file is executed directly
if (require.main === module) {
  const benchmark = new PerformanceBenchmark();
  
  // Check if --gc flag is provided for garbage collection
  if (process.argv.includes('--gc')) {
    console.log('🗑️  Garbage collection enabled for more accurate memory measurements');
    if (typeof global.gc === 'function') {
      console.log('✅ GC available');
    } else {
      console.log('⚠️  GC not available. Run with: node --expose-gc benchmark.js --gc');
    }
  }
  
  benchmark.runBenchmarkSuite().catch(error => {
    console.error('❌ Benchmark failed:', error);
    process.exit(1);
  });
}

module.exports = PerformanceBenchmark;
