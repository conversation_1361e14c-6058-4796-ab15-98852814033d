/**
 * Test script to verify the nearby places fix
 */

const axios = require('axios');

async function testNearbyPlacesFix() {
  console.log('🧪 Testing Nearby Places Fix...\n');

  const API_BASE = 'http://localhost:1337/api';

  try {
    // Test 1: Get enabled categories
    console.log('1. Testing enabled categories...');
    const categoriesResponse = await axios.get(`${API_BASE}/nearby-place-categories/enabled`);
    const categories = categoriesResponse.data.data;
    
    console.log(`✅ Found ${categories.length} enabled categories:`);
    categories.forEach(cat => {
      console.log(`   - ${cat.displayName} (${cat.name}): ${cat.googlePlaceTypes.length} place types`);
    });

    if (categories.length === 0) {
      console.log('❌ No enabled categories found - this is the issue!');
      return;
    }

    // Test 2: Test the exact API call that frontend makes
    console.log('\n2. Testing frontend API call format...');
    const testCoords = { lat: 40.7128, lng: -74.0060 }; // New York
    const testCategory = categories[0];

    console.log(`Testing with category: ${testCategory.displayName}`);
    console.log(`Place types: ${testCategory.googlePlaceTypes.join(', ')}`);
    console.log(`Coordinates: ${testCoords.lat}, ${testCoords.lng}`);

    const placesResponse = await axios.post(`${API_BASE}/properties/nearby-places`, {
      lat: testCoords.lat,
      lng: testCoords.lng,
      types: testCategory.googlePlaceTypes,
      radius: testCategory.searchRadius || 1500,
      maxResults: testCategory.maxResults || 5
    });

    console.log('\n✅ API Response Structure:');
    console.log('Response keys:', Object.keys(placesResponse.data));
    
    if (placesResponse.data.data) {
      console.log(`✅ Found ${placesResponse.data.data.length} places in 'data' field`);
      if (placesResponse.data.data.length > 0) {
        console.log('Sample place:', {
          name: placesResponse.data.data[0].name,
          vicinity: placesResponse.data.data[0].vicinity,
          place_id: placesResponse.data.data[0].place_id
        });
      }
    } else if (placesResponse.data.places) {
      console.log(`✅ Found ${placesResponse.data.places.length} places in 'places' field`);
    } else {
      console.log('❌ No places found in response');
      console.log('Full response:', JSON.stringify(placesResponse.data, null, 2));
    }

    // Test 3: Test multiple categories
    console.log('\n3. Testing all enabled categories...');
    for (const category of categories.slice(0, 3)) { // Test first 3 categories
      try {
        const response = await axios.post(`${API_BASE}/properties/nearby-places`, {
          lat: testCoords.lat,
          lng: testCoords.lng,
          types: category.googlePlaceTypes,
          radius: category.searchRadius || 1500,
          maxResults: category.maxResults || 5
        });

        const places = response.data.data || response.data.places || [];
        console.log(`   ${category.displayName}: ${places.length} places found`);
      } catch (error) {
        console.log(`   ${category.displayName}: ERROR - ${error.message}`);
      }
    }

    console.log('\n🎉 Nearby Places API is working correctly!');
    console.log('✅ Categories are enabled');
    console.log('✅ API returns data in correct format');
    console.log('✅ Google Places API is responding');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 404) {
      console.log('\n🔍 Possible issues:');
      console.log('   - API endpoint not found');
      console.log('   - Route not properly configured');
    } else if (error.response?.status === 500) {
      console.log('\n🔍 Possible issues:');
      console.log('   - Google Maps API key not configured');
      console.log('   - Backend service error');
    }
  }
}

testNearbyPlacesFix();
