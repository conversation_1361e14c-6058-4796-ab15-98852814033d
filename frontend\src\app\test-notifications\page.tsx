'use client';

import React, { useState } from 'react';
import { useNotifications } from '@/contexts/NotificationContext';
import { useAuth } from '@/contexts/AuthContext';

const TestNotificationsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const { user } = useAuth();
  const { 
    notifications, 
    unreadCount, 
    fetchNotifications, 
    markAsRead, 
    markAllAsRead, 
    deleteNotification 
  } = useNotifications();

  const createTestNotification = async () => {
    if (!user) {
      setMessage('Please log in first');
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem('jwt');
      const response = await fetch('http://localhost:1337/api/notifications/test', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setMessage('Test notification created successfully!');
        // Refresh notifications
        await fetchNotifications();
      } else {
        const error = await response.json();
        setMessage(`Error: ${error.error?.message || 'Failed to create notification'}`);
      }
    } catch (error) {
      setMessage(`Error: ${error.message}`);
    }
    setLoading(false);
  };

  const testMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
      setMessage('Notification marked as read!');
    } catch (error) {
      setMessage(`Error marking as read: ${error.message}`);
    }
  };

  const testDelete = async (notificationId: string) => {
    try {
      await deleteNotification(notificationId);
      setMessage('Notification deleted!');
    } catch (error) {
      setMessage(`Error deleting: ${error.message}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Test Notifications System</h1>
        
        {/* Status */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">System Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-600">User Status</p>
              <p className="text-lg font-semibold">{user ? 'Logged In' : 'Not Logged In'}</p>
              {user && <p className="text-sm text-gray-600">{user.email}</p>}
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <p className="text-sm text-green-600">Total Notifications</p>
              <p className="text-lg font-semibold">{notifications.length}</p>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <p className="text-sm text-orange-600">Unread Count</p>
              <p className="text-lg font-semibold">{unreadCount}</p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Actions</h2>
          <div className="space-y-4">
            <button
              onClick={createTestNotification}
              disabled={loading || !user}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Creating...' : 'Create Test Notification'}
            </button>
            
            <button
              onClick={() => fetchNotifications()}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 ml-4"
            >
              Refresh Notifications
            </button>

            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 ml-4"
              >
                Mark All as Read
              </button>
            )}
          </div>
          
          {message && (
            <div className={`mt-4 p-3 rounded-md ${
              message.includes('Error') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'
            }`}>
              {message}
            </div>
          )}
        </div>

        {/* Notifications List */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Current Notifications</h2>
          {notifications.length === 0 ? (
            <p className="text-gray-500">No notifications found.</p>
          ) : (
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`border rounded-lg p-4 ${
                    !notification.isRead ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{notification.title}</h3>
                      <p className="text-gray-600 mt-1">{notification.message}</p>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                        <span>Type: {notification.type}</span>
                        <span>Priority: {notification.priority}</span>
                        <span>Status: {notification.isRead ? 'Read' : 'Unread'}</span>
                        <span>Created: {new Date(notification.createdAt).toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      {!notification.isRead && (
                        <button
                          onClick={() => testMarkAsRead(notification.id)}
                          className="text-blue-600 hover:text-blue-800 text-sm"
                        >
                          Mark Read
                        </button>
                      )}
                      <button
                        onClick={() => testDelete(notification.id)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <a
            href="/dashboard"
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            ← Back to Dashboard
          </a>
        </div>
      </div>
    </div>
  );
};

export default TestNotificationsPage;
