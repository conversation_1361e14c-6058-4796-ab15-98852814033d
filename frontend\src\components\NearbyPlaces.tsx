'use client';

import React, { useState } from 'react';
import { Star, MapPin, Clock, Phone, Globe, ExternalLink, ChevronDown, ChevronUp } from 'lucide-react';

interface NearbyPlace {
  place_id: string;
  name: string;
  vicinity: string;
  rating?: number;
  user_ratings_total?: number;
  price_level?: number;
  types: string[];
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  photos?: Array<{
    photo_reference: string;
    width: number;
    height: number;
  }>;
  opening_hours?: {
    open_now: boolean;
  };
  business_status?: string;
}

interface PlaceCategory {
  id: number;
  name: string;
  displayName: string;
  icon: string;
  color: string;
}

interface NearbyPlacesSection {
  category: PlaceCategory;
  places: NearbyPlace[];
  error?: string;
}

interface NearbyPlacesProps {
  nearbyPlaces: Record<string, NearbyPlacesSection>;
  className?: string;
  showHeader?: boolean;
  dataSource?: 'saved' | 'generated' | null;
}

const PlaceCard: React.FC<{ place: NearbyPlace }> = ({ place }) => {
  const getPhotoUrl = (photoReference: string) => {
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    if (!apiKey || !photoReference) return null;
    return `https://maps.googleapis.com/maps/api/place/photo?maxwidth=400&photo_reference=${photoReference}&key=${apiKey}`;
  };

  const getPriceLevelText = (level?: number) => {
    if (!level) return null;
    return '$'.repeat(level);
  };

  const openInGoogleMaps = () => {
    const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(place.name)}&query_place_id=${place.place_id}`;
    window.open(url, '_blank');
  };

  const getStatusColor = () => {
    if (place.opening_hours?.open_now === true) return 'text-green-600';
    if (place.opening_hours?.open_now === false) return 'text-red-600';
    return 'text-gray-500';
  };

  const getStatusText = () => {
    if (place.opening_hours?.open_now === true) return 'Open now';
    if (place.opening_hours?.open_now === false) return 'Closed';
    return 'Hours unknown';
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl hover:shadow-lg transition-all duration-300 hover:border-blue-200 hover:-translate-y-1 group cursor-pointer"
         onClick={openInGoogleMaps}>
      <div className="flex">
        {/* Photo */}
        <div className="w-24 h-24 flex-shrink-0 relative">
          {place.photos && place.photos.length > 0 ? (
            <img
              src={getPhotoUrl(place.photos[0].photo_reference) || '/placeholder-business.jpg'}
              alt={place.name}
              className="w-full h-full object-cover rounded-l-xl"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/placeholder-business.jpg';
              }}
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-l-xl flex items-center justify-center">
              <MapPin className="h-7 w-7 text-blue-400" />
            </div>
          )}

          {/* Status indicator */}
          {place.opening_hours && (
            <div className={`absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-medium ${
              place.opening_hours.open_now ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {place.opening_hours.open_now ? 'Open' : 'Closed'}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 text-sm leading-tight mb-2 truncate group-hover:text-blue-600 transition-colors">
                {place.name}
              </h3>

              {/* Rating */}
              {place.rating && (
                <div className="flex items-center space-x-2 mb-2">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-3.5 w-3.5 ${
                          i < Math.floor(place.rating!)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    {place.rating.toFixed(1)}
                  </span>
                  {place.user_ratings_total && (
                    <span className="text-xs text-gray-500">
                      ({place.user_ratings_total} reviews)
                    </span>
                  )}
                </div>
              )}

              {/* Address */}
              <p className="text-xs text-gray-500 mb-2 line-clamp-1">{place.vicinity}</p>

              {/* Status and Price */}
              <div className="flex items-center space-x-2">
                {place.opening_hours && (
                  <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                    place.opening_hours.open_now
                      ? 'bg-green-100 text-green-700'
                      : 'bg-red-100 text-red-700'
                  }`}>
                    {place.opening_hours.open_now ? 'Open' : 'Closed'}
                  </span>
                )}

                {place.price_level && (
                  <span className="text-xs text-gray-500 font-medium">
                    {getPriceLevelText(place.price_level)}
                  </span>
                )}
              </div>
            </div>

            {/* Actions */}
            <button
              onClick={openInGoogleMaps}
              className="ml-2 p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-all duration-200"
              title="View on Google Maps"
            >
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const CategorySection: React.FC<{ section: NearbyPlacesSection }> = ({ section }) => {
  const [isExpanded, setIsExpanded] = useState(true);

  if (section.error) {
    return (
      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-3">
          <span className="text-lg">{section.category.icon}</span>
          <h2 className="text-lg font-semibold text-gray-900">
            {section.category.displayName}
          </h2>
        </div>
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">Error loading places: {section.error}</p>
        </div>
      </div>
    );
  }

  if (section.places.length === 0) {
    return (
      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-3">
          <span className="text-lg">{section.category.icon}</span>
          <h2 className="text-lg font-semibold text-gray-900">
            {section.category.displayName}
          </h2>
        </div>
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <p className="text-sm text-gray-600">No places found in this category</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full mb-4 p-4 bg-gradient-to-r from-white to-gray-50 hover:from-gray-50 hover:to-gray-100 rounded-xl border border-gray-200 hover:border-gray-300 transition-all duration-300 group shadow-sm hover:shadow-md"
      >
        <div className="flex items-center space-x-4">
          <div
            className="w-12 h-12 rounded-xl flex items-center justify-center text-xl shadow-sm"
            style={{ backgroundColor: `${section.category.color}20`, color: section.category.color }}
          >
            {section.category.icon}
          </div>
          <div className="text-left">
            <h2 className="text-lg font-bold text-gray-900 group-hover:text-gray-700 transition-colors">
              {section.category.displayName}
            </h2>
            <p className="text-sm text-gray-600">
              {section.places.length} {section.places.length === 1 ? 'place' : 'places'} nearby
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <span
            className="px-3 py-1.5 text-sm font-bold rounded-full text-white shadow-sm"
            style={{ backgroundColor: section.category.color }}
          >
            {section.places.length}
          </span>
          <div className="p-1 rounded-full group-hover:bg-gray-200 transition-colors">
            {isExpanded ? (
              <ChevronUp className="h-5 w-5 text-gray-500 group-hover:text-gray-700 transition-colors" />
            ) : (
              <ChevronDown className="h-5 w-5 text-gray-500 group-hover:text-gray-700 transition-colors" />
            )}
          </div>
        </div>
      </button>

      {isExpanded && (
        <div className="space-y-3 pl-4 pr-2">
          {section.places.map((place, index) => (
            <div
              key={place.place_id}
              className="animate-fadeIn"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <PlaceCard place={place} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export const NearbyPlaces: React.FC<NearbyPlacesProps> = ({ nearbyPlaces, className = '', showHeader = true, dataSource = null }) => {
  const sections = Object.values(nearbyPlaces);
  const totalPlaces = sections.reduce((total, section) => total + section.places.length, 0);

  if (sections.length === 0) {
    return (
      <div className={`p-8 bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-xl ${className}`}>
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <MapPin className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No Nearby Places Found</h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Generate nearby places to discover restaurants, schools, shopping centers, and other amenities around this property.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {showHeader && (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-2xl font-bold text-gray-900">What's Nearby</h2>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <MapPin className="h-4 w-4" />
              <span>{totalPlaces} places found</span>
            </div>
          </div>
          <p className="text-gray-600 text-lg">
            Discover restaurants, schools, shopping, and more around this property.
          </p>
        </div>
      )}

      <div className="space-y-6">
        {sections.map((section) => (
          <CategorySection key={section.category.name} section={section} />
        ))}
      </div>

      {/* Summary footer */}
      {sections.length > 0 && (
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-blue-800">
              <MapPin className="h-4 w-4" />
              <span className="text-sm font-medium">
                Found {totalPlaces} places across {sections.length} categories within walking distance
              </span>
            </div>
            {dataSource === 'saved' && (
              <div className="flex items-center space-x-1 text-xs text-green-700">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Using saved data</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
