const axios = require('axios');

const BASE_URL = 'http://localhost:1337/api';
const FRONTEND_URL = 'http://localhost:3000';

async function testFrontendEdit() {
  console.log('🚀 Testing frontend property edit functionality...\n');

  try {
    // Step 1: Login
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'Mb123321'
    });

    const token = loginResponse.data.jwt;
    const user = loginResponse.data.user;
    console.log('✅ Login successful');

    // Step 2: Get user properties to find a test property
    console.log('📋 Getting user properties...');
    const propertiesResponse = await axios.get(`${BASE_URL}/properties/my-properties`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    const properties = propertiesResponse.data.data;
    if (properties.length === 0) {
      throw new Error('No properties found for testing');
    }

    const testProperty = properties[0];
    console.log(`Found test property: ${testProperty.title} (ID: ${testProperty.id}, DocumentId: ${testProperty.documentId})`);

    // Step 3: Test getForEdit with documentId
    console.log('\n🔍 Testing getForEdit with documentId...');
    const editResponse = await axios.get(`${BASE_URL}/properties/${testProperty.documentId}/edit`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (editResponse.status === 200 && editResponse.data.data) {
      console.log('✅ getForEdit with documentId successful');
      console.log(`   Property title: ${editResponse.data.data.title}`);
      console.log(`   Property ID: ${editResponse.data.data.id}`);
      console.log(`   Document ID: ${editResponse.data.data.documentId}`);
    } else {
      throw new Error('getForEdit with documentId failed');
    }

    // Step 4: Test property update with documentId
    console.log('\n✏️ Testing property update with documentId...');
    const updateData = {
      title: testProperty.title,
      description: testProperty.description || 'Updated description',
      price: testProperty.price,
      currency: testProperty.currency || 'USD',
      propertyType: testProperty.propertyType,
      status: testProperty.status,
      bedrooms: testProperty.bedrooms || 2,
      bathrooms: testProperty.bathrooms || 1,
      area: testProperty.area,
      areaUnit: testProperty.areaUnit || 'sqft',
      address: testProperty.address,
      city: testProperty.city,
      country: testProperty.country,
      neighborhood: testProperty.neighborhood || [],
      propertyCode: testProperty.propertyCode || '',
      isLuxury: testProperty.isLuxury || false,
      features: testProperty.features || [],
      furnished: testProperty.furnished || false,
      petFriendly: testProperty.petFriendly || false
    };

    const updateResponse = await axios.put(`${BASE_URL}/properties/${testProperty.documentId}`, {
      data: updateData
    }, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (updateResponse.status === 200 && updateResponse.data.data) {
      console.log('✅ Property update with documentId successful');
      console.log(`   Updated title: ${updateResponse.data.data.title}`);
      console.log(`   Response has data: ${updateResponse.data.data ? 'Yes' : 'No'}`);
      console.log(`   Property ID: ${updateResponse.data.data.id}`);
      console.log(`   Document ID: ${updateResponse.data.data.documentId}`);
    } else {
      throw new Error('Property update with documentId failed');
    }

    // Step 5: Test getForEdit with numeric ID
    console.log('\n🔍 Testing getForEdit with numeric ID...');
    const editNumericResponse = await axios.get(`${BASE_URL}/properties/${testProperty.id}/edit`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (editNumericResponse.status === 200 && editNumericResponse.data.data) {
      console.log('✅ getForEdit with numeric ID successful');
      console.log(`   Property title: ${editNumericResponse.data.data.title}`);
      console.log(`   Property ID: ${editNumericResponse.data.data.id}`);
      console.log(`   Document ID: ${editNumericResponse.data.data.documentId}`);
    } else {
      throw new Error('getForEdit with numeric ID failed');
    }

    // Step 6: Test property update with numeric ID
    console.log('\n✏️ Testing property update with numeric ID...');
    const updateNumericResponse = await axios.put(`${BASE_URL}/properties/${testProperty.id}`, {
      data: updateData
    }, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (updateNumericResponse.status === 200 && updateNumericResponse.data.data) {
      console.log('✅ Property update with numeric ID successful');
      console.log(`   Updated title: ${updateNumericResponse.data.data.title}`);
      console.log(`   Response has data: ${updateNumericResponse.data.data ? 'Yes' : 'No'}`);
      console.log(`   Property ID: ${updateNumericResponse.data.data.id}`);
      console.log(`   Document ID: ${updateNumericResponse.data.data.documentId}`);
    } else {
      throw new Error('Property update with numeric ID failed');
    }

    console.log('\n🎉 All frontend edit functionality tests passed!');
    console.log('\n📝 Frontend URLs to test manually:');
    console.log(`   Edit with documentId: ${FRONTEND_URL}/dashboard/properties/${testProperty.documentId}/edit`);
    console.log(`   Edit with numeric ID: ${FRONTEND_URL}/dashboard/properties/${testProperty.id}/edit`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    process.exit(1);
  }
}

testFrontendEdit();
