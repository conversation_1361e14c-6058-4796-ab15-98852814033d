# This workflow warns and then closes issues and PRs that have had no activity for a specified amount of time.
#
# You can adjust the behavior by modifying this file.
# For more information, see:
# https://github.com/actions/stale
name: Mark stale issues and pull requests

on:
  schedule:
  - cron: '00 12 * * *'

jobs:
  stale:

    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write

    steps:
    - uses: actions/stale@v5
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: 'Marked as stale because of inactivity. Feel free to comment if you still have this issue.'
        stale-pr-message: 'Marked as stale because of inactivity. Feel free to comment if this is an active PR.'
        stale-issue-label: 'no-issue-activity'
        stale-pr-label: 'no-pr-activity'
        start-date: '2019-12-01 15:00:00'
