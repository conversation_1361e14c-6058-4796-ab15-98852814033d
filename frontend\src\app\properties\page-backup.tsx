'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';

interface Property {
  documentId: string;
  id: number;
  title: string;
  description: string;
  price: number;
  currency: string;
  propertyType: string;
  offer: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  areaUnit: string;
  address: string;
  city: string;
  country: string;
  neighborhood?: string;
  propertyCode?: string;
  isLuxury?: boolean;
  features?: string[];
  views: number;
  images?: any[];
  createdAt: string;
  updatedAt: string;
  slug?: string;
}

const PropertiesPageContent: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <h1 className="text-3xl font-bold text-gray-900">Properties</h1>
            <p className="text-gray-600 mt-2">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h1 className="text-3xl font-bold text-gray-900">Properties</h1>
          <p className="text-gray-600 mt-2">Find your perfect property</p>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Search Properties</h2>
          <p className="text-gray-600">Enhanced filter system coming soon...</p>
        </div>
      </div>
    </div>
  );
};

const PropertiesPage: React.FC = () => {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    }>
      <PropertiesPageContent />
    </Suspense>
  );
};

export default PropertiesPage;
