# Image Upload & Display Testing Guide

## 🎉 What We've Fixed & Implemented

### ✅ Issues Resolved
1. **Property Edit Form Images Not Showing** - FIXED ✅
   - Enhanced `ImageUploadWithDragDrop` component to display existing images
   - Added proper URL construction for backend images
   - Fixed image display in property listings and edit forms

2. **Image URL Construction** - FIXED ✅
   - Standardized image URL construction across all components
   - Uses `NEXT_PUBLIC_STRAPI_URL` environment variable
   - Consistent fallback to placeholder images

3. **Floor Plan Display** - ENHANCED ✅
   - Enhanced `FloorPlanUploadWithDragDrop` component to show existing floor plans
   - Added support for both image and PDF floor plans
   - Proper preview and file information display

### 🆕 New Features Implemented

#### 1. Enhanced Image Upload Component
**File**: `frontend/src/components/ImageUploadWithDragDrop.tsx`

**Features**:
- ✅ Display existing images from backend
- ✅ Drag & drop multiple images (up to 15)
- ✅ Image reordering (drag to reorder)
- ✅ Primary image indicator (first image)
- ✅ Remove individual images
- ✅ Visual distinction between existing and new images
- ✅ Image preview with thumbnails

#### 2. Enhanced Floor Plan Upload Component
**File**: `frontend/src/components/FloorPlanUploadWithDragDrop.tsx`

**Features**:
- ✅ Display existing floor plans from backend
- ✅ Support for images (PNG, JPG, GIF) and PDF files
- ✅ Drag & drop upload
- ✅ File size and type information
- ✅ Preview for image floor plans
- ✅ Replace existing floor plans

#### 3. Fixed Image Display Across Frontend
**Files Updated**:
- `frontend/src/app/dashboard/properties/page.tsx` - Dashboard property listings
- `frontend/src/app/properties/page.tsx` - Public property listings  
- `frontend/src/app/properties/[id]/page.tsx` - Property detail pages
- `frontend/src/app/dashboard/properties/[id]/edit/page.tsx` - Property edit form

**Improvements**:
- ✅ Consistent image URL construction
- ✅ Proper fallback to placeholder images
- ✅ Environment variable usage for backend URL

## 🧪 Testing Instructions

### 1. Test Property Creation with Images
**URL**: `http://localhost:3000/dashboard/properties/create`

**Test Cases**:
- [ ] Drag & drop single image
- [ ] Drag & drop multiple images (test up to 15)
- [ ] Click to browse and select images
- [ ] Reorder images by dragging
- [ ] Remove individual images
- [ ] Verify first image shows "Primary" badge
- [ ] Upload floor plan (image or PDF)
- [ ] Submit form and verify images are saved

### 2. Test Property Edit with Existing Images
**URL**: `http://localhost:3000/dashboard/properties/100/edit`

**Test Cases**:
- [ ] Verify existing images are displayed
- [ ] Check "Saved" badge on existing images
- [ ] Add new images to existing property
- [ ] Remove existing images
- [ ] Reorder existing and new images
- [ ] Replace existing floor plan
- [ ] Submit changes and verify updates

### 3. Test Image Display in Listings
**URLs**: 
- `http://localhost:3000/dashboard/properties` (Dashboard)
- `http://localhost:3000/properties` (Public listings)

**Test Cases**:
- [ ] Verify images display correctly in property cards
- [ ] Check placeholder images for properties without images
- [ ] Test both grid and list view modes (dashboard)
- [ ] Verify image URLs are properly constructed

### 4. Test Property Detail Page
**URL**: `http://localhost:3000/properties/100`

**Test Cases**:
- [ ] Verify main image gallery displays
- [ ] Test image navigation (next/previous)
- [ ] Check thumbnail strip functionality
- [ ] Test image modal/lightbox
- [ ] Verify floor plan display (if available)

### 5. Test Drag & Drop Functionality

**Image Upload Tests**:
- [ ] Drag single image file over upload area
- [ ] Drag multiple image files at once
- [ ] Verify drag over visual feedback (blue highlight)
- [ ] Test with different image formats (PNG, JPG, GIF)
- [ ] Test file size limits (should handle up to 10MB)
- [ ] Test invalid file types (should be rejected)

**Floor Plan Upload Tests**:
- [ ] Drag image file for floor plan
- [ ] Drag PDF file for floor plan
- [ ] Verify preview for image floor plans
- [ ] Verify file info display for PDF floor plans
- [ ] Test replace functionality

### 6. Test Image Management Features

**Reordering**:
- [ ] Drag first image to different position
- [ ] Verify "Primary" badge moves with first image
- [ ] Test reordering with mix of existing and new images

**Removal**:
- [ ] Remove individual images using X button
- [ ] Remove all images
- [ ] Remove existing images vs new images
- [ ] Verify proper cleanup of preview URLs

## 🔧 Technical Details

### Backend API Endpoints
- `POST /api/properties` - Create property with images
- `PUT /api/properties/:id` - Update property with images
- `GET /api/properties/:id/edit` - Get property for editing
- `GET /api/properties/:id` - Get property details

### Image Storage
- Images stored in: `backend/public/uploads/`
- Accessible via: `http://localhost:1337/uploads/filename.jpg`
- Thumbnails generated automatically by Strapi

### Environment Variables
```
NEXT_PUBLIC_STRAPI_URL=http://localhost:1337
```

## 🐛 Known Issues & Limitations

1. **Validation Errors**: Some property creation tests fail due to validation rules (not image-related)
2. **File Size**: Maximum 10MB per image (configurable in backend)
3. **File Types**: Only images for property photos, images + PDF for floor plans

## 🎯 Success Criteria

All tests should pass with:
- ✅ Images upload successfully
- ✅ Existing images display in edit forms
- ✅ Drag & drop works smoothly
- ✅ Image reordering functions correctly
- ✅ Floor plans upload and display properly
- ✅ All image URLs resolve correctly
- ✅ Placeholder images show when no images exist

## 📞 Support

If any issues are found during testing:
1. Check browser console for JavaScript errors
2. Check backend logs for API errors
3. Verify environment variables are set correctly
4. Ensure both frontend (port 3000) and backend (port 1337) are running
