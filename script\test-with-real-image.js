const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');

const API_URL = 'http://localhost:1337/api';

// Create a simple test image (1x1 pixel PNG)
function createTestImage(filename) {
  // Base64 encoded 1x1 pixel transparent PNG
  const pngData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==', 'base64');
  fs.writeFileSync(filename, pngData);
}

async function testWithRealImage() {
  try {
    console.log('🧪 Testing property creation with real image...');
    
    // Login
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    const token = loginResponse.data.jwt;
    console.log('✅ Logged in successfully');
    
    // Create test image
    const imagePath = 'test-image.png';
    createTestImage(imagePath);
    console.log('📸 Created test image');
    
    // Property data
    const propertyData = {
      title: 'Test Property with Real Image',
      description: 'A test property with a real image file',
      price: 300000,
      currency: 'USD',
      propertyType: 'villa',
      offer: 'for-sale',
      bedrooms: 3,
      bathrooms: 2,
      area: 150,
      areaUnit: 'sqm',
      address: '456 Image Test Street',
      city: 'Sharm El Sheikh',
      country: 'Egypt',
      neighborhood: 'Naama Bay',
      coordinates: {
        lat: 27.9158,
        lng: 34.3300
      },
      propertyCode: `IMG-TEST-${Date.now()}`,
      isLuxury: false,
      features: ['pool', 'garden'],
      furnished: true,
      petFriendly: false,
      views: 0
    };

    // Create FormData with image
    console.log('\n🖼️  Creating property with FormData and image...');
    const formData = new FormData();
    formData.append('data', JSON.stringify(propertyData));
    formData.append('files.images', fs.createReadStream(imagePath));
    
    try {
      const response = await axios.post(`${API_URL}/properties`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          ...formData.getHeaders()
        }
      });
      console.log('✅ Property created with image!');
      console.log('Property ID:', response.data.data.id);
      
      // Check if images were uploaded
      const checkResponse = await axios.get(`${API_URL}/properties/${response.data.data.id}?populate=images`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      const property = checkResponse.data.data;
      console.log(`📸 Images uploaded: ${property.images ? property.images.length : 0}`);
      if (property.images && property.images.length > 0) {
        console.log('🖼️  Image URLs:');
        property.images.forEach((img, index) => {
          console.log(`   ${index + 1}. ${img.url}`);
        });
      }
      
    } catch (error) {
      console.error('❌ Failed to create property with image:');
      console.error('Error:', error.response?.data?.error?.message || error.message);
      if (error.response?.data?.error?.details) {
        console.error('Details:', JSON.stringify(error.response.data.error.details, null, 2));
      }
    }
    
    // Clean up
    try {
      fs.unlinkSync(imagePath);
      console.log('🧹 Cleaned up test image');
    } catch (e) {
      // Ignore cleanup errors
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testWithRealImage();
