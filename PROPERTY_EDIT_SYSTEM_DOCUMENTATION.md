# Property Edit System - Complete Documentation

## Overview

This document provides comprehensive documentation for the property editing system in our real estate application, including the recent fixes for Strapi v5 DocumentId compatibility.

## System Architecture

### Backend (Strapi v5)
- **Framework**: Strapi CMS v5.16.1
- **Database**: SQLite (development)
- **Authentication**: JWT-based authentication
- **File Uploads**: Native Strapi media handling

### Frontend (Next.js)
- **Framework**: Next.js with TypeScript
- **Authentication**: Custom AuthContext with JWT
- **State Management**: React hooks (useState, useEffect)
- **File Uploads**: FormData with drag-and-drop support

## API Endpoints

### Property Edit Endpoints

#### 1. Get Property for Editing
```
GET /api/properties/:id/edit
```

**Parameters:**
- `id`: Property identifier (supports both numeric ID and documentId)

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "data": {
    "id": 1,
    "documentId": "udxctx875q45lwj2q2s6brwn",
    "title": "Property Title",
    "description": "Property description",
    "price": 100000,
    "currency": "USD",
    "propertyType": "apartment",
    "status": "for-sale",
    "bedrooms": 2,
    "bathrooms": 1,
    "area": 1000,
    "areaUnit": "sqft",
    "address": "123 Main St",
    "city": "City Name",
    "country": "Country",
    "neighborhood": ["Downtown", "Business District"],
    "coordinates": { "lat": 40.7128, "lng": -74.0060 },
    "propertyCode": "PROP001",
    "isLuxury": false,
    "features": ["Swimming Pool", "Gym"],
    "yearBuilt": 2020,
    "parking": 1,
    "furnished": false,
    "petFriendly": true,
    "virtualTour": "https://example.com/tour",
    "owner": { ... },
    "agent": { ... },
    "images": [ ... ],
    "floorPlan": { ... },
    "project": { ... }
  }
}
```

#### 2. Update Property
```
PUT /api/properties/:id
```

**Parameters:**
- `id`: Property identifier (supports both numeric ID and documentId)

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json (for data-only updates)
Content-Type: multipart/form-data (for file uploads)
```

**Request Body (JSON):**
```json
{
  "data": {
    "title": "Updated Property Title",
    "description": "Updated description",
    "price": 120000,
    "currency": "USD",
    "propertyType": "apartment",
    "status": "for-sale",
    "bedrooms": 3,
    "bathrooms": 2,
    "area": 1200,
    "areaUnit": "sqft",
    "address": "123 Updated St",
    "city": "Updated City",
    "country": "Updated Country",
    "neighborhood": ["New Neighborhood"],
    "coordinates": { "lat": 40.7128, "lng": -74.0060 },
    "propertyCode": "PROP001-UPDATED",
    "isLuxury": true,
    "features": ["Swimming Pool", "Gym", "Garden"],
    "yearBuilt": 2021,
    "parking": 2,
    "furnished": true,
    "petFriendly": false,
    "virtualTour": "https://example.com/updated-tour"
  }
}
```

**Request Body (FormData for file uploads):**
```
data: JSON.stringify(propertyData)
files.images: File[]
files.floorPlan: File
```

**Response:** Same as GET endpoint with updated data.

## Frontend Implementation

### Property Edit Page
**Location**: `frontend/src/app/dashboard/properties/[id]/edit/page.tsx`

### Key Features

#### 1. Dual ID Support
The edit page works with both numeric IDs and documentIds:
```typescript
// Both URLs work:
/dashboard/properties/1/edit              // numeric ID
/dashboard/properties/udxctx875q45lwj2q2s6brwn/edit  // documentId
```

#### 2. Data Loading
```typescript
useEffect(() => {
  const fetchProperty = async () => {
    const response = await propertiesAPI.getForEdit(params.id as string);
    const property = response.data;
    // Populate form with property data
    setFormData({
      title: property.title,
      description: property.description,
      // ... other fields
    });
  };
  
  if (user) {
    fetchProperty();
  }
}, [params.id, user]);
```

#### 3. Form Submission
```typescript
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  // Prepare property data
  const propertyData = {
    title: formData.title.trim(),
    description: formData.description.trim(),
    price: Number(formData.price),
    // ... other fields
  };

  if (formData.images || formData.floorPlan) {
    // Handle file uploads with FormData
    const formDataToSend = new FormData();
    formDataToSend.append('data', JSON.stringify(propertyData));
    
    if (formData.images) {
      for (let i = 0; i < formData.images.length; i++) {
        formDataToSend.append('files.images', formData.images[i]);
      }
    }
    
    if (formData.floorPlan) {
      formDataToSend.append('files.floorPlan', formData.floorPlan);
    }
    
    // Submit with fetch for file uploads
    const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/api/properties/${params.id}`, {
      method: 'PUT',
      headers: { 'Authorization': `Bearer ${localStorage.getItem('jwt')}` },
      body: formDataToSend
    });
  } else {
    // Submit without files using API wrapper
    await propertiesAPI.update(params.id as string, propertyData);
  }
};
```

### Form Fields

#### Basic Information
- **Title** (required): Property title
- **Description** (required): Detailed property description
- **Price** (required): Property price (numeric)
- **Currency**: Currency code (USD, EUR, etc.)
- **Property Type**: apartment, villa, townhouse, etc.
- **Status**: for-sale, for-rent, sold, rented, off-market

#### Property Details
- **Bedrooms**: Number of bedrooms
- **Bathrooms**: Number of bathrooms  
- **Area** (required): Property area (numeric)
- **Area Unit**: sqft, sqm, etc.
- **Year Built**: Construction year
- **Parking**: Number of parking spaces

#### Location
- **Address** (required): Street address
- **City** (required): City name
- **Country** (required): Country name
- **Neighborhood**: Multiple selection dropdown
- **Coordinates**: Latitude and longitude (map selector)

#### Additional Information
- **Property Code**: Internal reference code
- **Luxury Property**: Boolean checkbox
- **Furnished**: Boolean checkbox
- **Pet Friendly**: Boolean checkbox
- **Features**: Multiple selection (Swimming Pool, Gym, etc.)
- **Virtual Tour**: URL to virtual tour

#### Media
- **Images**: Multiple image upload with drag-and-drop
- **Floor Plan**: Single file upload with drag-and-drop

## Backend Implementation Details

### Controller Methods

#### getForEdit Method
**Purpose**: Retrieve property data for editing with ownership validation.

**Key Features:**
- Dual ID support (numeric and documentId)
- Ownership validation
- Complete data population
- Fallback mechanism for documentId lookup

#### update Method  
**Purpose**: Update property data with file upload support.

**Key Features:**
- Dual ID support
- Ownership validation
- File upload handling
- Complete response data
- Atomic update operations

### Security Features

#### Authentication
- JWT token validation
- User session management
- Token expiration handling

#### Authorization
- Property ownership validation
- Admin role bypass
- Forbidden access prevention

#### Data Validation
- Required field validation
- Type conversion and sanitization
- File upload validation
- Size and format restrictions

## Error Handling

### Backend Errors
```javascript
// Property not found
return ctx.notFound('Property not found');

// Access denied
return ctx.forbidden('You can only edit your own properties');

// Validation errors
return ctx.badRequest('Invalid property data');

// Server errors
return ctx.internalServerError('Failed to update property');
```

### Frontend Errors
```typescript
// Display user-friendly error messages
setError(err.message || 'Failed to update property');

// Handle network errors
if (!response.ok) {
  const errorData = await response.json();
  throw new Error(errorData.error?.message || 'Failed to update property');
}
```

## File Upload System

### Supported Formats
- **Images**: JPEG, PNG, WebP
- **Floor Plans**: PDF, JPEG, PNG

### Upload Process
1. **Frontend**: Files selected via drag-and-drop or file picker
2. **Validation**: Client-side format and size validation
3. **FormData**: Files packaged with property data
4. **Backend**: Strapi media handling and storage
5. **Response**: Updated property with media URLs

### Storage
- **Development**: Local file system
- **Production**: Configurable (AWS S3, Cloudinary, etc.)

## Testing

### Automated Tests
- **Backend API**: Comprehensive endpoint testing
- **Dual ID Support**: Both numeric and documentId testing
- **File Uploads**: Media handling validation
- **Authentication**: Security testing

### Test Scripts
- `test-comprehensive.js`: Full workflow testing
- `test-property-edit.js`: Edit-specific testing
- `test-frontend-edit.js`: Frontend integration testing

### Manual Testing
- Frontend edit forms with both ID types
- File upload functionality
- Error handling scenarios
- Cross-browser compatibility

## Performance Considerations

### Backend Optimizations
- Efficient database queries
- Proper data population
- Minimal API calls
- Caching strategies

### Frontend Optimizations
- Lazy loading of components
- Optimized image handling
- Form validation
- Loading states

## Deployment Notes

### Environment Variables
```env
# Backend
DATABASE_URL=<database_connection_string>
JWT_SECRET=<jwt_secret_key>
STRAPI_ADMIN_BACKEND_URL=http://localhost:1337

# Frontend  
NEXT_PUBLIC_STRAPI_URL=http://localhost:1337
```

### Build Process
1. Backend: `npm run build`
2. Frontend: `npm run build`
3. Database migrations: Automatic with Strapi
4. Media uploads: Ensure proper permissions

## Troubleshooting

### Common Issues
1. **DocumentId not found**: Check fallback mechanism implementation
2. **File upload fails**: Verify FormData structure and headers
3. **Authentication errors**: Check JWT token validity
4. **Permission denied**: Verify property ownership

### Debug Tools
- Backend logging with detailed error messages
- Frontend console logging for API responses
- Network tab inspection for request/response data
- Strapi admin panel for data verification

## Recent Fixes & Improvements

### Strapi v5 DocumentId Compatibility
- ✅ **Fixed critical documentId lookup issue** - Implemented fallback mechanism for `entityService.findOne`
- ✅ **Resolved update response problem** - Fixed null response data from `entityService.update`
- ✅ **Added dual ID support** - Both numeric IDs and documentIds work seamlessly
- ✅ **Enhanced error handling** - Comprehensive logging and debugging
- ✅ **Complete test coverage** - All scenarios tested and validated

### Performance & Reliability
- ✅ **Optimized database queries** - Efficient property lookup mechanisms
- ✅ **Robust error handling** - User-friendly error messages
- ✅ **Enhanced security** - Proper ownership validation
- ✅ **File upload improvements** - Reliable media handling

## Future Enhancements

### Planned Features
- Bulk property editing
- Version history tracking
- Advanced media management
- Real-time collaboration
- Enhanced validation rules

### Technical Improvements
- GraphQL API integration
- Enhanced caching
- Performance monitoring
- Automated testing expansion
