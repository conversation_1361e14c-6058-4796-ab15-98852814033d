/**
 * Test script to verify authentication fix for nearby places
 */

const axios = require('axios');

async function testAuthFix() {
  console.log('🔐 Testing Authentication Fix for Nearby Places...\n');

  const API_BASE = 'http://localhost:1337/api';

  try {
    // Step 1: Login to get a valid token
    console.log('1. Logging in to get authentication token...');
    
    // Try to login with a test user (you may need to create this user first)
    const loginData = {
      identifier: '<EMAIL>', // Change this to an existing user
      password: 'TestPassword123!' // Change this to the correct password
    };

    try {
      const loginResponse = await axios.post(`${API_BASE}/auth/local`, loginData);
      const token = loginResponse.data.jwt;
      const user = loginResponse.data.user;
      
      console.log('✅ Login successful!');
      console.log(`   User: ${user.username} (${user.email})`);
      console.log(`   Token: ${token.substring(0, 20)}...`);

      // Step 2: Find a property with coordinates
      console.log('\n2. Finding a property with coordinates...');
      const propertiesResponse = await axios.get(`${API_BASE}/properties?pagination[limit]=10`);
      const properties = propertiesResponse.data.data;
      
      const propertyWithCoords = properties.find(p => p.coordinates);
      
      if (!propertyWithCoords) {
        console.log('❌ No properties with coordinates found');
        return;
      }

      console.log(`✅ Found property: ${propertyWithCoords.title}`);
      console.log(`   ID: ${propertyWithCoords.id}`);
      console.log(`   Coordinates: ${propertyWithCoords.coordinates.lat}, ${propertyWithCoords.coordinates.lng}`);

      // Step 3: Test the generate-nearby-places endpoint with authentication
      console.log('\n3. Testing generate-nearby-places endpoint with authentication...');
      
      const generateResponse = await axios.post(
        `${API_BASE}/properties/${propertyWithCoords.id}/generate-nearby-places`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (generateResponse.status === 200) {
        console.log('✅ Generate nearby places successful!');
        const nearbyPlaces = generateResponse.data.data.nearbyPlaces;
        const categories = Object.keys(nearbyPlaces);
        const totalPlaces = Object.values(nearbyPlaces).reduce((total, category) => 
          total + (category.places ? category.places.length : 0), 0
        );
        
        console.log(`   Generated ${categories.length} categories with ${totalPlaces} total places`);
        
        categories.forEach(categoryName => {
          const category = nearbyPlaces[categoryName];
          console.log(`   - ${category.category.displayName}: ${category.places.length} places`);
        });

        // Step 4: Verify the data was saved by fetching the property again
        console.log('\n4. Verifying data was saved to property...');
        const updatedPropertyResponse = await axios.get(`${API_BASE}/properties/${propertyWithCoords.id}`);
        const updatedProperty = updatedPropertyResponse.data.data;
        
        if (updatedProperty.nearbyPlaces && Object.keys(updatedProperty.nearbyPlaces).length > 0) {
          console.log('✅ Data successfully saved to property!');
          console.log(`   Saved categories: ${Object.keys(updatedProperty.nearbyPlaces).join(', ')}`);
          
          // Step 5: Test frontend URL
          console.log('\n5. Frontend test URL:');
          const frontendUrl = updatedProperty.slug 
            ? `http://localhost:3000/properties/${updatedProperty.slug}`
            : `http://localhost:3000/properties/${updatedProperty.documentId}`;
          
          console.log(`   🌐 ${frontendUrl}`);
          console.log('   This property should now show saved nearby places data!');
          
        } else {
          console.log('❌ Data was not saved to property');
        }

      } else {
        console.log('❌ Generate nearby places failed:', generateResponse.status);
      }

    } catch (loginError) {
      console.log('❌ Login failed:', loginError.response?.data || loginError.message);
      console.log('\n💡 To test this properly, you need to:');
      console.log('   1. Create a user account in the system');
      console.log('   2. Update the loginData in this script with valid credentials');
      console.log('   3. Or use the frontend login page to create an account');
      
      // Alternative: Test without authentication to confirm the error
      console.log('\n🔍 Testing without authentication to confirm error...');
      try {
        const noAuthResponse = await axios.post(
          `${API_BASE}/properties/1/generate-nearby-places`,
          {}
        );
      } catch (noAuthError) {
        if (noAuthError.response?.status === 401) {
          console.log('✅ Confirmed: Authentication is required (401 error)');
          console.log('   This means the fix is working - the endpoint properly requires auth');
        }
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testAuthFix();
