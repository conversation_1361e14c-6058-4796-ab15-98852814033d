const primary = "#1a7fa0";
const secondary = "#f69977";
const teal = "#88BFc4";
const blue = "#103751";
const orange = "#cf6b4e";
const lightGray = "#ECEFF1";
const darkBlue = "#0c293c";
const dark = "#09151C";
const positive = "#0FC457";
const negative = "#EF4444";
const neutral = "#767676";

module.exports.chartColors = {
  blue: {
    hex: "#4285F4",
    rgb: "rgba(66, 133, 244, 1)",
    rgba: (alpha) => `rgba(66, 133, 244, ${alpha})`,
  },
  amber: {
    hex: "#FF9800",
    rgb: "rgba(255, 152, 0, 1)",
    rgba: (alpha) => `rgba(255, 152, 0, ${alpha})`,
  },
  teal: {
    hex: "#26A69A",
    rgb: "rgba(38, 166, 154, 1)",
    rgba: (alpha) => `rgba(38, 166, 154, ${alpha})`,
  },
  fuchsia: {
    hex: "#D602EE",
    rgb: "rgba(214, 2, 238, 1)",
    rgba: (alpha) => `rgba(214, 2, 238, ${alpha})`,
  },
  lime: {
    hex: "#C0CA33",
    rgb: "rgba(192, 202, 51, 1)",
    rgba: (alpha) => `rgba(192, 202, 51, ${alpha})`,
  },
  deep_fuchsia: {
    hex: "#9C27B0",
    rgb: "rgba(156, 39, 176, 1)",
    rgba: (alpha) => `rgba(156, 39, 176, ${alpha})`,
  },
  orange: {
    hex: "#EE6002",
    rgb: "rgba(238, 96, 2, 1)",
    rgba: (alpha) => `rgba(238, 96, 2, ${alpha})`,
  },
  light_purple: {
    hex: "#C8A1FF",
    rgb: "rgba(200, 161, 255, 1)",
    rgba: (alpha) => `rgba(200, 161, 255, ${alpha})`,
  },
  green: {
    hex: "#43A047",
    rgb: "rgba(67, 160, 71, 1)",
    rgba: (alpha) => `rgba(67, 160, 71, ${alpha})`,
  },
  rose: {
    hex: "#D81B60",
    rgb: "rgba(216, 27, 96, 1)",
    rgba: (alpha) => `rgba(216, 27, 96, ${alpha})`,
  },
  purple: {
    hex: "#6200EE",
    rgb: "rgba(98, 0, 238, 1)",
    rgba: (alpha) => `rgba(98, 0, 238, ${alpha})`,
  },
  yellow: {
    hex: "#FFC107",
    rgb: "rgba(255, 193, 7, 1)",
    rgba: (alpha) => `rgba(255, 193, 7, ${alpha})`,
  },
  deep_purple: {
    hex: "#3B00ED",
    rgb: "rgba(59, 0, 237, 1)",
    rgba: (alpha) => `rgba(59, 0, 237, ${alpha})`,
  },
  error: {
    hex: "#B00020",
    rgb: "rgba(176, 0, 32, 1)",
    rgba: (alpha) => `rgba(176, 0, 32, ${alpha})`,
  },
  pink: {
    hex: "#EB3693",
    rgb: "rgba(235, 54, 147, 1)",
    rgba: (alpha) => `rgba(235, 54, 147, ${alpha})`,
  }
};

module.exports.fillChartColors = [""];

module.exports.Colors = {
  primary,
  secondary,
  teal,
  blue,
  orange,
  lightGray,
  darkBlue,
  dark,
  positive,
  negative,
  neutral,
};
