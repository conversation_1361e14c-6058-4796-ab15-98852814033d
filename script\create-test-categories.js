const axios = require('axios');

const API_BASE = 'http://localhost:1337/api';

// Test categories with accurate Google Place Types
const testCategories = [
  {
    name: 'education',
    displayName: 'Education',
    description: 'Schools, universities, and educational institutions',
    icon: '🎓',
    googlePlaceTypes: ['school', 'university', 'library'],
    enabled: true,
    searchRadius: 2000,
    maxResults: 5,
    priority: 10,
    color: '#3B82F6'
  },
  {
    name: 'restaurants',
    displayName: 'Restaurants & Dining',
    description: 'Restaurants, cafes, and dining options',
    icon: '🍽️',
    googlePlaceTypes: ['restaurant', 'cafe', 'bakery'],
    enabled: true,
    searchRadius: 1000,
    maxResults: 8,
    priority: 9,
    color: '#EF4444'
  },
  {
    name: 'shopping',
    displayName: 'Shopping',
    description: 'Shopping centers, stores, and retail',
    icon: '🛍️',
    googlePlaceTypes: ['shopping_mall', 'supermarket', 'store'],
    enabled: true,
    searchRadius: 1500,
    maxResults: 6,
    priority: 8,
    color: '#10B981'
  }
];

async function createTestCategories() {
  console.log('🚀 Creating test place categories...');

  try {
    for (const category of testCategories) {
      try {
        console.log(`📝 Creating category: ${category.displayName}`);
        
        const response = await axios.post(`${API_BASE}/nearby-place-categories`, {
          data: category
        }, {
          headers: {
            'Content-Type': 'application/json'
          }
        });

        console.log(`✅ Created: ${category.displayName}`);
      } catch (error) {
        if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('unique')) {
          console.log(`⚠️  Category ${category.displayName} already exists, skipping...`);
        } else {
          console.error(`❌ Failed to create ${category.displayName}:`, error.response?.data?.error?.message || error.message);
        }
      }
    }

    console.log('🎉 Test categories creation completed!');
    
    // Test the API endpoint
    console.log('\n🔍 Testing categories API...');
    const testResponse = await axios.get(`${API_BASE}/nearby-place-categories/enabled`);
    console.log(`✅ Found ${testResponse.data.data.length} enabled categories`);
    
  } catch (error) {
    console.error('❌ Failed to create test categories:', error.message);
  }
}

createTestCategories();
