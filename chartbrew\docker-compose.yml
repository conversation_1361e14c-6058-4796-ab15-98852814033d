services:
  db:
    image: mysql:8.4
    restart: unless-stopped
    environment:
      # These values are taken from the .env file
      MYSQL_DATABASE: ${CB_DB_NAME}
      MYSQL_USER: ${CB_DB_USERNAME}
      MYSQL_PASSWORD: ${CB_DB_PASSWORD}
      MYSQL_RANDOM_ROOT_PASSWORD: true
      MYSQL_DEFAULT_AUTH: caching_sha2_password
    volumes:
      - chartbrew-db:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:alpine
    restart: unless-stopped
    environment:
      CB_REDIS_HOST: redis
      REDIS_PASSWORD: ${CB_REDIS_PASSWORD}
    ports:
      - 6379:6379

  chartbrew:
    build: .
    restart: unless-stopped
    env_file: .env
    ports:
      # Feel free to remap these ports on the left side to your desired ports.
      # If you do change these ports you also need to update them in the VITE_APP_CLIENT_HOST and VITE_APP_API_HOST variables in your .env file
      - 4018:4018 # Frontend
      - 4019:4019 # API
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    links:
      - db
      - redis

volumes:
  chartbrew-db:
