// import type { Core } from '@strapi/strapi';

export default {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register(/* { strapi }: { strapi: Core.Strapi } */) {},

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  async bootstrap({ strapi }) {
    // Set up default roles and permissions
    const pluginStore = strapi.store({
      environment: '',
      type: 'plugin',
      name: 'users-permissions',
    });

    const settings = await pluginStore.get({
      key: 'advanced',
    });

    const newSettings = {
      ...settings,
      email_confirmation: false,
      email_reset_password: 'http://localhost:3000/auth/reset-password',
      email_confirmation_redirection: 'http://localhost:3000/auth/email-confirmation',
    };

    await pluginStore.set({
      key: 'advanced',
      value: newSettings,
    });

    console.log('✅ Authentication system configured successfully');

    // Create default membership plans if they don't exist
    try {
      const existingMemberships = await strapi.entityService.findMany('api::membership.membership');

      if (existingMemberships.length === 0) {
        console.log('Creating default membership plans...');

        // Basic Plan
        await strapi.entityService.create('api::membership.membership', {
          data: {
            name: 'Basic',
            slug: 'basic',
            description: 'Perfect for individual users getting started',
            price: 0,
            currency: 'USD',
            duration: 'monthly',
            features: [
              'Up to 3 properties',
              'Up to 3 images per property',
              'Basic search filters',
              'Email support'
            ],
            maxProperties: 3,
            maxImages: 3,
            canCreateProjects: false,
            canAccessAnalytics: false,
            canUsePremiumFilters: false,
            priority: 1,
            isActive: true,
            publishedAt: new Date()
          }
        });

        // Premium Plan
        await strapi.entityService.create('api::membership.membership', {
          data: {
            name: 'Premium',
            slug: 'premium',
            description: 'Great for real estate agents and small agencies',
            price: 29.99,
            currency: 'USD',
            duration: 'monthly',
            features: [
              'Up to 25 properties',
              'Up to 10 images per property',
              'Premium search filters',
              'Analytics dashboard',
              'Priority support',
              'Featured listings'
            ],
            maxProperties: 25,
            maxImages: 10,
            canCreateProjects: true,
            canAccessAnalytics: true,
            canUsePremiumFilters: true,
            priority: 2,
            isActive: true,
            publishedAt: new Date()
          }
        });

        console.log('✅ Default membership plans created successfully');
      }
    } catch (error) {
      console.error('Error creating default membership plans:', error);
    }
  },
};
