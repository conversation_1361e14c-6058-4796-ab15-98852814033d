const axios = require('axios');

const FRONTEND_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:1337/api';

async function testEditAndSubmit() {
  console.log('🏠 Testing Edit Property and Submit Property Pages...\n');

  try {
    // Step 1: Authentication
    console.log('1. Testing authentication...');
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    const token = loginResponse.data.jwt;
    const user = loginResponse.data.user;
    console.log(`✅ Authentication successful - User: ${user.username}`);
    
    // Step 2: Get existing properties for edit testing
    console.log('\n2. Getting existing properties for edit testing...');
    const propertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const properties = propertiesResponse.data.data || propertiesResponse.data;
    console.log(`✅ Found ${properties.length} properties for testing`);
    
    if (properties.length === 0) {
      console.log('⚠️  No properties found. Creating a test property first...');
      
      // Create a test property for edit testing
      const newProperty = await axios.post(`${API_URL}/properties`, {
        data: {
          title: 'Edit Test Property',
          description: 'A property created specifically for testing the edit functionality.',
          price: 275000,
          currency: 'USD',
          propertyType: 'house',
          offer: 'for-sale',
          bedrooms: 3,
          bathrooms: 2,
          area: 120,
          areaUnit: 'sqm',
          address: '123 Edit Test Street',
          city: 'Edit City',
          country: 'United States',
          neighborhood: 'Test Neighborhood'
        }
      }, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      properties.push(newProperty.data.data);
      console.log(`✅ Created test property: ${newProperty.data.data.title}`);
    }
    
    const testProperty = properties[0];
    console.log(`\n📝 Using property for testing:`);
    console.log(`   Title: ${testProperty.title}`);
    console.log(`   ID: ${testProperty.id}`);
    console.log(`   Document ID: ${testProperty.documentId}`);
    
    // Step 3: Test Submit Property Page
    console.log('\n3. Testing Submit Property Page...');
    
    const submitPageResponse = await axios.get(`${FRONTEND_URL}/submit-property`);
    console.log('✅ Submit Property page loads successfully (200)');
    
    // Check if the page contains expected form elements
    const submitPageContent = submitPageResponse.data;
    const hasFormElements = submitPageContent.includes('form') && 
                           submitPageContent.includes('submit') &&
                           submitPageContent.includes('property');
    
    if (hasFormElements) {
      console.log('✅ Submit page contains expected form elements');
    } else {
      console.log('⚠️  Submit page might be missing some form elements');
    }
    
    // Step 4: Test Edit Property Page
    console.log('\n4. Testing Edit Property Page...');
    
    const editPageResponse = await axios.get(`${FRONTEND_URL}/dashboard/properties/${testProperty.documentId}/edit`);
    console.log('✅ Edit Property page loads successfully (200)');
    
    // Check if the page contains expected edit elements
    const editPageContent = editPageResponse.data;
    const hasEditElements = editPageContent.includes('edit') && 
                           editPageContent.includes('property') &&
                           editPageContent.includes('form');
    
    if (hasEditElements) {
      console.log('✅ Edit page contains expected form elements');
    } else {
      console.log('⚠️  Edit page might be missing some form elements');
    }
    
    // Step 5: Test Property Creation API (Submit functionality)
    console.log('\n5. Testing Property Creation API (Submit functionality)...');
    
    const newPropertyData = {
      title: 'API Test Property - Submit',
      description: 'A property created through API testing to verify submit functionality.',
      price: 425000,
      currency: 'USD',
      propertyType: 'villa',
      offer: 'for-sale',
      bedrooms: 4,
      bathrooms: 3,
      area: 200,
      areaUnit: 'sqm',
      address: '456 API Test Avenue',
      city: 'API City',
      country: 'United States',
      neighborhood: 'API Neighborhood',
      coordinates: {
        lat: 40.7128,
        lng: -74.0060
      },
      features: ['garden', 'garage', 'swimming-pool'],
      yearBuilt: 2022,
      parking: 2,
      furnished: false,
      petFriendly: true,
      isLuxury: true
    };
    
    const createResponse = await axios.post(`${API_URL}/properties`, {
      data: newPropertyData
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Property creation API working');
    console.log(`   Created: ${createResponse.data.data.title}`);
    console.log(`   ID: ${createResponse.data.data.id}`);
    console.log(`   Document ID: ${createResponse.data.data.documentId}`);
    
    const createdProperty = createResponse.data.data;
    
    // Step 6: Test Property Update API (Edit functionality)
    console.log('\n6. Testing Property Update API (Edit functionality)...');
    
    const updateData = {
      title: testProperty.title + ' (Updated)',
      description: testProperty.description + ' This property has been updated through API testing.',
      price: testProperty.price + 10000,
      bedrooms: testProperty.bedrooms + 1,
      features: ['updated-feature', 'api-tested']
    };
    
    const updateResponse = await axios.put(`${API_URL}/properties/${testProperty.documentId}`, {
      data: updateData
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Property update API working');
    console.log(`   Updated: ${updateResponse.data.data.title}`);
    console.log(`   New Price: ${updateResponse.data.data.currency} ${updateResponse.data.data.price.toLocaleString()}`);
    console.log(`   New Bedrooms: ${updateResponse.data.data.bedrooms}`);
    
    // Step 7: Test Property Edit Data Retrieval
    console.log('\n7. Testing Property Edit Data Retrieval...');
    
    const editDataResponse = await axios.get(`${API_URL}/properties/${testProperty.documentId}/edit`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Property edit data retrieval working');
    console.log(`   Retrieved data for: ${editDataResponse.data.data.title}`);
    console.log(`   All fields available for editing`);
    
    // Step 8: Test Form Validation (Invalid data)
    console.log('\n8. Testing form validation with invalid data...');
    
    try {
      const invalidResponse = await axios.post(`${API_URL}/properties`, {
        data: {
          title: '', // Empty title should fail
          price: -1000, // Negative price should fail
          bedrooms: -1 // Negative bedrooms should fail
        }
      }, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('⚠️  Validation might be too lenient - invalid data was accepted');
    } catch (validationError) {
      if (validationError.response?.status === 400) {
        console.log('✅ Form validation working - invalid data rejected');
      } else {
        console.log(`⚠️  Unexpected validation error: ${validationError.response?.status}`);
      }
    }
    
    // Step 9: Test File Upload Endpoint
    console.log('\n9. Testing file upload endpoint...');
    
    try {
      // Test upload endpoint availability
      const uploadTestResponse = await axios.get(`${API_URL}/upload`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Upload endpoint accessible');
    } catch (uploadError) {
      if (uploadError.response?.status === 405) {
        console.log('✅ Upload endpoint exists (405 Method Not Allowed for GET is expected)');
      } else {
        console.log(`⚠️  Upload endpoint issue: ${uploadError.response?.status}`);
      }
    }
    
    // Step 10: Clean up test data
    console.log('\n10. Cleaning up test data...');
    
    // Revert the updated property
    await axios.put(`${API_URL}/properties/${testProperty.documentId}`, {
      data: {
        title: testProperty.title,
        description: testProperty.description,
        price: testProperty.price,
        bedrooms: testProperty.bedrooms
      }
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Test property reverted to original state');
    
    // Delete the created test property
    await axios.delete(`${API_URL}/properties/${createdProperty.documentId}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Test property deleted');
    
    // Step 11: Final Summary
    console.log('\n🎉 Edit & Submit Property Pages - COMPLETE TEST RESULTS:');
    console.log('   ✅ Submit Property Page: Loading correctly');
    console.log('   ✅ Edit Property Page: Loading correctly');
    console.log('   ✅ Property Creation API: Working');
    console.log('   ✅ Property Update API: Working');
    console.log('   ✅ Edit Data Retrieval: Working');
    console.log('   ✅ Form Validation: Available');
    console.log('   ✅ File Upload Endpoint: Available');
    console.log('   ✅ Data Cleanup: Working');
    
    console.log('\n📱 Manual Testing Instructions:');
    console.log('   SUBMIT PROPERTY:');
    console.log('   1. Open: http://localhost:3000/submit-property');
    console.log('   2. Fill out the property form');
    console.log('   3. Upload images (optional)');
    console.log('   4. Submit the form');
    console.log('   5. Verify property appears in dashboard');
    console.log('');
    console.log('   EDIT PROPERTY:');
    console.log('   1. Login: http://localhost:3000/auth/login');
    console.log('   2. Go to: http://localhost:3000/dashboard/properties');
    console.log('   3. Click "Edit" on any property');
    console.log('   4. Modify property details');
    console.log('   5. Save changes');
    console.log('   6. Verify updates are reflected');
    
    console.log('\n🚀 Both Edit and Submit functionality are working correctly!');
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error:`, error.response.data);
    }
  }
}

testEditAndSubmit().catch(console.error);
