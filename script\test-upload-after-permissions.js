const axios = require('axios');

const API_URL = 'http://localhost:1337/api';

async function testUploadAfterPermissions() {
  console.log('🧪 Testing Upload After Enabling Permissions...\n');

  try {
    // Step 1: Login
    console.log('1. Authenticating user...');
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    const token = loginResponse.data.jwt;
    console.log('✅ Authentication successful');
    
    // Step 2: Test file listing (GET /api/upload/files)
    console.log('\n2. Testing file listing...');
    try {
      const filesResponse = await axios.get(`${API_URL}/upload/files`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log(`✅ File listing works - Found ${filesResponse.data.length} files`);
    } catch (listError) {
      if (listError.response?.status === 403) {
        console.log('❌ File listing still forbidden - check "find" permission');
      } else {
        console.log(`⚠️  File listing error: ${listError.response?.status}`);
      }
    }
    
    // Step 3: Test file upload (POST /api/upload)
    console.log('\n3. Testing file upload...');
    
    const FormData = require('form-data');
    const form = new FormData();
    
    // Create a test image file (simple text file for testing)
    const testFileContent = Buffer.from('This is a test file for property images', 'utf8');
    form.append('files', testFileContent, {
      filename: 'test-property-image.jpg',
      contentType: 'image/jpeg'
    });
    
    try {
      const uploadResponse = await axios.post(`${API_URL}/upload`, form, {
        headers: {
          'Authorization': `Bearer ${token}`,
          ...form.getHeaders()
        }
      });
      
      console.log('✅ File upload successful!');
      console.log(`   Uploaded file: ${uploadResponse.data[0]?.name}`);
      console.log(`   File ID: ${uploadResponse.data[0]?.id}`);
      console.log(`   File URL: ${uploadResponse.data[0]?.url}`);
      console.log(`   File size: ${uploadResponse.data[0]?.size} bytes`);
      
      const uploadedFile = uploadResponse.data[0];
      
      // Step 4: Test getting specific file (GET /api/upload/files/:id)
      console.log('\n4. Testing specific file retrieval...');
      try {
        const fileResponse = await axios.get(`${API_URL}/upload/files/${uploadedFile.id}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`✅ File retrieval works - File: ${fileResponse.data.name}`);
      } catch (getError) {
        console.log(`⚠️  File retrieval error: ${getError.response?.status}`);
      }
      
      // Step 5: Test file deletion (DELETE /api/upload/files/:id)
      console.log('\n5. Testing file deletion...');
      try {
        await axios.delete(`${API_URL}/upload/files/${uploadedFile.id}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log('✅ File deletion works');
      } catch (deleteError) {
        if (deleteError.response?.status === 403) {
          console.log('❌ File deletion forbidden - check "destroy" permission');
        } else {
          console.log(`⚠️  File deletion error: ${deleteError.response?.status}`);
        }
      }
      
    } catch (uploadError) {
      if (uploadError.response?.status === 403) {
        console.log('❌ File upload still forbidden');
        console.log('💡 Make sure "upload" permission is enabled for Authenticated role');
      } else {
        console.log(`❌ Upload failed: ${uploadError.response?.status}`);
        console.log('   Error details:', uploadError.response?.data);
      }
    }
    
    // Step 6: Test upload with property linking
    console.log('\n6. Testing upload with property linking...');
    
    // Get a property to link the file to
    const propertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const properties = propertiesResponse.data.data || propertiesResponse.data;
    if (properties.length > 0) {
      const testProperty = properties[0];
      
      const linkedForm = new FormData();
      linkedForm.append('files', testFileContent, {
        filename: 'property-linked-image.jpg',
        contentType: 'image/jpeg'
      });
      linkedForm.append('ref', 'api::property.property');
      linkedForm.append('refId', testProperty.documentId);
      linkedForm.append('field', 'images');
      
      try {
        const linkedUploadResponse = await axios.post(`${API_URL}/upload`, linkedForm, {
          headers: {
            'Authorization': `Bearer ${token}`,
            ...linkedForm.getHeaders()
          }
        });
        
        console.log('✅ Property-linked upload successful!');
        console.log(`   Linked to property: ${testProperty.title}`);
        console.log(`   File: ${linkedUploadResponse.data[0]?.name}`);
        
      } catch (linkedError) {
        console.log(`⚠️  Property-linked upload error: ${linkedError.response?.status}`);
      }
    } else {
      console.log('⚠️  No properties available for linking test');
    }
    
    console.log('\n🎉 UPLOAD TEST SUMMARY:');
    console.log('   If all tests pass: Upload functionality is fully working!');
    console.log('   If some fail: Check specific permissions mentioned above');
    console.log('');
    console.log('📝 NEXT STEPS FOR FRONTEND:');
    console.log('   1. Update submit-property form to include file upload');
    console.log('   2. Update edit-property form to manage existing images');
    console.log('   3. Display uploaded images in property listings');
    console.log('   4. Add image gallery functionality');
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error:`, error.response.data);
    }
  }
}

testUploadAfterPermissions().catch(console.error);
