const fetch = require('node-fetch');

async function testSimple() {
  const STRAPI_URL = 'http://localhost:1337';
  
  try {
    // Login first
    const loginResponse = await fetch(`${STRAPI_URL}/api/auth/local`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        identifier: '<EMAIL>',
        password: 'Mb123321'
      })
    });
    
    const loginData = await loginResponse.json();
    const jwt = loginData.jwt;
    console.log('✅ Login successful');
    
    // Test documentId directly
    console.log('Testing documentId: udxctx875q45lwj2q2s6brwn');
    const docResponse = await fetch(`${STRAPI_URL}/api/properties/udxctx875q45lwj2q2s6brwn/edit`, {
      headers: { 'Authorization': `Bearer ${jwt}` }
    });
    
    console.log('DocumentId response status:', docResponse.status);
    if (!docResponse.ok) {
      const errorText = await docResponse.text();
      console.log('DocumentId error:', errorText);
    }
    
    // Test numeric ID
    console.log('Testing numeric ID: 1');
    const numResponse = await fetch(`${STRAPI_URL}/api/properties/1/edit`, {
      headers: { 'Authorization': `Bearer ${jwt}` }
    });
    
    console.log('Numeric ID response status:', numResponse.status);
    if (numResponse.ok) {
      const data = await numResponse.json();
      console.log('Numeric ID success, property title:', data.data?.title);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSimple();
