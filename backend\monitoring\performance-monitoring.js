/**
 * Performance Monitoring Configuration
 * Tracks and alerts on property API performance metrics
 */

const { performance } = require('perf_hooks');

class PropertyAPIMonitor {
  constructor() {
    this.metrics = {
      responseTime: [],
      memoryUsage: [],
      errorRate: [],
      throughput: []
    };
    
    this.thresholds = {
      responseTime: {
        warning: 300,  // ms
        critical: 500  // ms
      },
      memoryUsage: {
        warning: 15,   // MB per request
        critical: 25   // MB per request
      },
      errorRate: {
        warning: 2,    // %
        critical: 5    // %
      },
      throughput: {
        warning: 50,   // req/sec
        critical: 25   // req/sec
      }
    };
    
    this.alerts = [];
    this.isMonitoring = false;
  }
  
  // Middleware to track performance metrics
  createMonitoringMiddleware() {
    return async (ctx, next) => {
      const startTime = performance.now();
      const memBefore = process.memoryUsage();
      
      try {
        await next();
        
        const endTime = performance.now();
        const memAfter = process.memoryUsage();
        
        // Calculate metrics
        const responseTime = endTime - startTime;
        const memoryDelta = (memAfter.heapUsed - memBefore.heapUsed) / 1024 / 1024; // MB
        
        // Record metrics
        this.recordMetric('responseTime', responseTime, ctx.path);
        this.recordMetric('memoryUsage', memoryDelta, ctx.path);
        
        // Check thresholds
        this.checkThresholds(responseTime, memoryDelta, ctx.path);
        
      } catch (error) {
        this.recordError(error, ctx.path);
        throw error;
      }
    };
  }
  
  recordMetric(type, value, endpoint) {
    const timestamp = new Date();
    
    this.metrics[type].push({
      value,
      endpoint,
      timestamp
    });
    
    // Keep only last 1000 entries
    if (this.metrics[type].length > 1000) {
      this.metrics[type] = this.metrics[type].slice(-1000);
    }
  }
  
  recordError(error, endpoint) {
    const timestamp = new Date();
    
    this.metrics.errorRate.push({
      error: error.message,
      endpoint,
      timestamp
    });
    
    // Check error rate threshold
    this.checkErrorRate();
  }
  
  checkThresholds(responseTime, memoryUsage, endpoint) {
    // Response time check
    if (responseTime > this.thresholds.responseTime.critical) {
      this.createAlert('critical', 'responseTime', responseTime, endpoint);
    } else if (responseTime > this.thresholds.responseTime.warning) {
      this.createAlert('warning', 'responseTime', responseTime, endpoint);
    }
    
    // Memory usage check
    if (memoryUsage > this.thresholds.memoryUsage.critical) {
      this.createAlert('critical', 'memoryUsage', memoryUsage, endpoint);
    } else if (memoryUsage > this.thresholds.memoryUsage.warning) {
      this.createAlert('warning', 'memoryUsage', memoryUsage, endpoint);
    }
  }
  
  checkErrorRate() {
    const last5Minutes = new Date(Date.now() - 5 * 60 * 1000);
    const recentErrors = this.metrics.errorRate.filter(e => e.timestamp > last5Minutes);
    const recentRequests = Object.values(this.metrics)
      .flat()
      .filter(m => m.timestamp > last5Minutes).length;
    
    if (recentRequests > 0) {
      const errorRate = (recentErrors.length / recentRequests) * 100;
      
      if (errorRate > this.thresholds.errorRate.critical) {
        this.createAlert('critical', 'errorRate', errorRate, 'all');
      } else if (errorRate > this.thresholds.errorRate.warning) {
        this.createAlert('warning', 'errorRate', errorRate, 'all');
      }
    }
  }
  
  createAlert(severity, metric, value, endpoint) {
    const alert = {
      id: Date.now() + Math.random(),
      severity,
      metric,
      value,
      endpoint,
      timestamp: new Date(),
      message: this.generateAlertMessage(severity, metric, value, endpoint)
    };
    
    this.alerts.push(alert);
    
    // Log alert
    console.log(`🚨 ${severity.toUpperCase()} ALERT: ${alert.message}`);
    
    // In production, send to monitoring service
    this.sendToMonitoringService(alert);
    
    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }
  }
  
  generateAlertMessage(severity, metric, value, endpoint) {
    const threshold = this.thresholds[metric][severity];
    
    switch (metric) {
      case 'responseTime':
        return `Response time ${value.toFixed(2)}ms exceeds ${severity} threshold (${threshold}ms) for ${endpoint}`;
      case 'memoryUsage':
        return `Memory usage ${value.toFixed(2)}MB exceeds ${severity} threshold (${threshold}MB) for ${endpoint}`;
      case 'errorRate':
        return `Error rate ${value.toFixed(2)}% exceeds ${severity} threshold (${threshold}%) for ${endpoint}`;
      default:
        return `${metric} alert for ${endpoint}`;
    }
  }
  
  sendToMonitoringService(alert) {
    // Placeholder for external monitoring service integration
    // Examples: DataDog, New Relic, Prometheus, etc.
    
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to webhook or monitoring API
      // await fetch(process.env.MONITORING_WEBHOOK_URL, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(alert)
      // });
    }
  }
  
  // Generate performance report
  generateReport(timeRange = '1h') {
    const now = new Date();
    const timeRangeMs = this.parseTimeRange(timeRange);
    const since = new Date(now.getTime() - timeRangeMs);
    
    const report = {
      timeRange,
      generatedAt: now,
      metrics: {}
    };
    
    // Calculate metrics for each type
    Object.keys(this.metrics).forEach(metricType => {
      const recentMetrics = this.metrics[metricType]
        .filter(m => m.timestamp > since);
      
      if (recentMetrics.length > 0) {
        const values = recentMetrics.map(m => m.value).filter(v => typeof v === 'number');
        
        if (values.length > 0) {
          report.metrics[metricType] = {
            count: values.length,
            avg: values.reduce((a, b) => a + b, 0) / values.length,
            min: Math.min(...values),
            max: Math.max(...values),
            p95: this.calculatePercentile(values, 95),
            p99: this.calculatePercentile(values, 99)
          };
        }
      }
    });
    
    // Add endpoint breakdown
    report.endpointBreakdown = this.getEndpointBreakdown(since);
    
    // Add recent alerts
    report.recentAlerts = this.alerts
      .filter(a => a.timestamp > since)
      .sort((a, b) => b.timestamp - a.timestamp);
    
    return report;
  }
  
  getEndpointBreakdown(since) {
    const breakdown = {};
    
    Object.values(this.metrics).flat()
      .filter(m => m.timestamp > since && m.endpoint)
      .forEach(metric => {
        if (!breakdown[metric.endpoint]) {
          breakdown[metric.endpoint] = {
            requestCount: 0,
            avgResponseTime: 0,
            avgMemoryUsage: 0,
            errors: 0
          };
        }
        
        breakdown[metric.endpoint].requestCount++;
        
        if (typeof metric.value === 'number') {
          if (metric.endpoint.includes('responseTime')) {
            breakdown[metric.endpoint].avgResponseTime += metric.value;
          } else if (metric.endpoint.includes('memoryUsage')) {
            breakdown[metric.endpoint].avgMemoryUsage += metric.value;
          }
        }
      });
    
    // Calculate averages
    Object.keys(breakdown).forEach(endpoint => {
      const data = breakdown[endpoint];
      if (data.requestCount > 0) {
        data.avgResponseTime /= data.requestCount;
        data.avgMemoryUsage /= data.requestCount;
      }
    });
    
    return breakdown;
  }
  
  parseTimeRange(timeRange) {
    const unit = timeRange.slice(-1);
    const value = parseInt(timeRange.slice(0, -1));
    
    switch (unit) {
      case 'm': return value * 60 * 1000;
      case 'h': return value * 60 * 60 * 1000;
      case 'd': return value * 24 * 60 * 60 * 1000;
      default: return 60 * 60 * 1000; // 1 hour default
    }
  }
  
  calculatePercentile(arr, percentile) {
    const sorted = [...arr].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index];
  }
  
  // Start monitoring
  startMonitoring() {
    this.isMonitoring = true;
    console.log('🔍 Property API monitoring started');
    
    // Generate reports every 5 minutes
    setInterval(() => {
      const report = this.generateReport('5m');
      console.log('📊 Performance Report:', JSON.stringify(report, null, 2));
    }, 5 * 60 * 1000);
  }
  
  // Stop monitoring
  stopMonitoring() {
    this.isMonitoring = false;
    console.log('⏹️  Property API monitoring stopped');
  }
}

// Export singleton instance
const monitor = new PropertyAPIMonitor();

module.exports = {
  PropertyAPIMonitor,
  monitor,
  
  // Convenience functions
  startMonitoring: () => monitor.startMonitoring(),
  stopMonitoring: () => monitor.stopMonitoring(),
  getReport: (timeRange) => monitor.generateReport(timeRange),
  getAlerts: () => monitor.alerts,
  createMiddleware: () => monitor.createMonitoringMiddleware()
};
