/**
 * Test script to debug single property update for offer field
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:1337';
const API_URL = `${BASE_URL}/api`;

// Test user credentials
const TEST_USER = {
  identifier: '<EMAIL>',
  password: 'Mb123321'
};

let authToken = '';

async function login() {
  try {
    console.log('🔐 Logging in test user...');
    const response = await axios.post(`${API_URL}/auth/local`, TEST_USER);
    authToken = response.data.jwt;
    console.log('✅ User login successful');
    return response.data;
  } catch (error) {
    console.error('❌ User login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testSingleUpdate() {
  try {
    console.log('\n🔍 Testing single property update...');
    
    // Get first property
    const response = await axios.get(`${API_URL}/properties?pagination[limit]=1`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    const properties = response.data.data;
    if (properties.length === 0) {
      console.log('❌ No properties found');
      return;
    }
    
    const property = properties[0];
    console.log(`🎯 Testing update for: ${property.title}`);
    console.log(`📋 Current offer value: ${property.offer}`);
    console.log(`🆔 Property ID: ${property.documentId}`);
    
    // Try to update the property
    console.log('\n🔄 Attempting to update offer field...');
    
    try {
      const updateResponse = await axios.put(`${API_URL}/properties/${property.documentId}`, {
        data: {
          offer: 'for-sale'
        }
      }, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Update request successful');
      console.log('📊 Response data:', JSON.stringify(updateResponse.data, null, 2));
      
      // Verify the update by fetching the property again
      console.log('\n🔍 Verifying update...');
      const verifyResponse = await axios.get(`${API_URL}/properties/${property.documentId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      console.log(`📋 Updated offer value: ${verifyResponse.data.data.offer}`);
      
      if (verifyResponse.data.data.offer === 'for-sale') {
        console.log('✅ Update verified successfully!');
      } else {
        console.log('❌ Update verification failed - value not persisted');
      }
      
    } catch (updateError) {
      console.error('❌ Update failed:', updateError.response?.data || updateError.message);
      
      // Try using the edit endpoint instead
      console.log('\n🔄 Trying edit endpoint...');
      try {
        const editUpdateResponse = await axios.put(`${API_URL}/properties/${property.documentId}/update`, {
          offer: 'for-sale'
        }, {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log('✅ Edit endpoint update successful');
        console.log('📊 Edit response:', JSON.stringify(editUpdateResponse.data, null, 2));
        
      } catch (editError) {
        console.error('❌ Edit endpoint also failed:', editError.response?.data || editError.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

async function runTest() {
  console.log('🚀 Starting Single Property Update Test\n');
  
  try {
    await login();
    await testSingleUpdate();
  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
  }
}

// Run the test
runTest();
