const axios = require('axios');

const API_URL = 'http://localhost:1337/api';

async function getPropertyForEdit() {
  console.log('🔍 Getting Property for Edit Testing...\n');

  try {
    // Login first
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    const token = loginResponse.data.jwt;
    console.log('✅ Authentication successful');
    
    // Get user's properties
    const propertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const properties = propertiesResponse.data.data || propertiesResponse.data;
    console.log(`✅ Found ${properties.length} properties`);
    
    if (properties.length > 0) {
      const property = properties[0];
      console.log('\n📝 Property available for edit testing:');
      console.log(`   Title: ${property.title}`);
      console.log(`   ID: ${property.id}`);
      console.log(`   Document ID: ${property.documentId}`);
      console.log(`   Edit URL: http://localhost:3000/dashboard/properties/${property.documentId}/edit`);
      
      // Test the edit page URL
      console.log('\n🌐 Testing edit page URL...');
      const editPageResponse = await axios.get(`http://localhost:3000/dashboard/properties/${property.documentId}/edit`);
      console.log(`✅ Edit page loads successfully (${editPageResponse.status})`);
      
      console.log('\n📱 Manual Testing:');
      console.log(`   1. Open: http://localhost:3000/auth/login`);
      console.log(`   2. Login with: <EMAIL> / TestPassword123!`);
      console.log(`   3. Go to: http://localhost:3000/dashboard/properties/${property.documentId}/edit`);
      console.log(`   4. Verify the form is pre-filled with property data`);
      console.log(`   5. Make changes and save`);
      
      return property.documentId;
    } else {
      console.log('❌ No properties found for edit testing');
      return null;
    }
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
    }
    return null;
  }
}

getPropertyForEdit().then(propertyId => {
  if (propertyId) {
    console.log(`\n🎯 Property ID for testing: ${propertyId}`);
  }
}).catch(console.error);
