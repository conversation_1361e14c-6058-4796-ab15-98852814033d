'use client';

import React, { useState, useMemo } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useProperties, useCities, useNeighborhoods } from '@/hooks/useProperties';
import { Property, PropertyFilters } from '@/types/api';
import { MapPin, Bed, Bath, Square, Eye, Search, Filter, Grid, List, Loader2 } from 'lucide-react';

interface OptimizedPropertiesPageProps {
  initialFilters?: PropertyFilters;
}

const OptimizedPropertiesPage: React.FC<OptimizedPropertiesPageProps> = ({ 
  initialFilters = {} 
}) => {
  const searchParams = useSearchParams();
  const router = useRouter();

  // State for filters and UI
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  
  // Build filters from URL params and initial filters
  const filters = useMemo(() => {
    const urlFilters: PropertyFilters = {};
    
    // Extract filters from URL
    if (searchParams.get('location')) urlFilters.location = searchParams.get('location')!;
    if (searchParams.get('propertyType')) urlFilters.propertyType = searchParams.get('propertyType')!;
    if (searchParams.get('offer')) urlFilters.offer = searchParams.get('offer')!;
    if (searchParams.get('priceMin')) urlFilters.priceMin = Number(searchParams.get('priceMin'));
    if (searchParams.get('priceMax')) urlFilters.priceMax = Number(searchParams.get('priceMax'));
    if (searchParams.get('bedrooms')) urlFilters.bedrooms = Number(searchParams.get('bedrooms'));
    if (searchParams.get('bathrooms')) urlFilters.bathrooms = Number(searchParams.get('bathrooms'));
    if (searchParams.get('city')) urlFilters.city = searchParams.get('city')!;
    if (searchParams.get('neighborhood')) urlFilters.neighborhood = searchParams.get('neighborhood')!;
    if (searchParams.get('page')) urlFilters.page = Number(searchParams.get('page'));
    
    return { ...initialFilters, ...urlFilters };
  }, [searchParams, initialFilters]);

  // React Query hooks
  const { 
    data: propertiesResponse, 
    isLoading: propertiesLoading, 
    error: propertiesError,
    refetch: refetchProperties 
  } = useProperties(filters);

  const { data: cities, isLoading: citiesLoading } = useCities();
  const { data: neighborhoods, isLoading: neighborhoodsLoading } = useNeighborhoods(filters.city);

  // Extract data from response
  const properties = propertiesResponse?.data || [];
  const pagination = propertiesResponse?.meta?.pagination;

  // Update URL when filters change
  const updateFilters = (newFilters: Partial<PropertyFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    
    // Build URL params
    const params = new URLSearchParams();
    Object.entries(updatedFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, String(value));
      }
    });
    
    // Update URL
    router.push(`/properties?${params.toString()}`);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    updateFilters({ page });
  };

  // Loading state
  if (propertiesLoading && !properties.length) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading properties...</span>
      </div>
    );
  }

  // Error state
  if (propertiesError) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">Failed to load properties</p>
        <button 
          onClick={() => refetchProperties()}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Properties</h1>
          {pagination && (
            <p className="text-gray-600 mt-1">
              Showing {((pagination.page - 1) * pagination.pageSize) + 1} - {Math.min(pagination.page * pagination.pageSize, pagination.total)} of {pagination.total} properties
            </p>
          )}
        </div>
        
        {/* View Mode Toggle */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded ${viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
          >
            <Grid className="h-4 w-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded ${viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
          >
            <List className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Quick Filters */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Location Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
            <input
              type="text"
              placeholder="Search by location..."
              value={filters.location || ''}
              onChange={(e) => updateFilters({ location: e.target.value, page: 1 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Property Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Property Type</label>
            <select
              value={filters.propertyType || ''}
              onChange={(e) => updateFilters({ propertyType: e.target.value, page: 1 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              <option value="apartment">Apartment</option>
              <option value="house">House</option>
              <option value="villa">Villa</option>
              <option value="studio">Studio</option>
              <option value="penthouse">Penthouse</option>
            </select>
          </div>

          {/* Offer Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Offer</label>
            <select
              value={filters.offer || ''}
              onChange={(e) => updateFilters({ offer: e.target.value, page: 1 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Offers</option>
              <option value="sale">For Sale</option>
              <option value="rent">For Rent</option>
            </select>
          </div>

          {/* City */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
            <select
              value={filters.city || ''}
              onChange={(e) => updateFilters({ city: e.target.value, neighborhood: '', page: 1 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={citiesLoading}
            >
              <option value="">All Cities</option>
              {cities?.map((city) => (
                <option key={city} value={city}>{city}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Advanced Filters Toggle */}
        <button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="mt-4 flex items-center text-blue-600 hover:text-blue-800"
        >
          <Filter className="h-4 w-4 mr-1" />
          {showAdvancedFilters ? 'Hide' : 'Show'} Advanced Filters
        </button>

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Price Range</label>
                <div className="flex space-x-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={filters.priceMin || ''}
                    onChange={(e) => updateFilters({ priceMin: Number(e.target.value) || undefined, page: 1 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    value={filters.priceMax || ''}
                    onChange={(e) => updateFilters({ priceMax: Number(e.target.value) || undefined, page: 1 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Bedrooms */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Min Bedrooms</label>
                <select
                  value={filters.bedrooms || ''}
                  onChange={(e) => updateFilters({ bedrooms: Number(e.target.value) || undefined, page: 1 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Any</option>
                  <option value="1">1+</option>
                  <option value="2">2+</option>
                  <option value="3">3+</option>
                  <option value="4">4+</option>
                  <option value="5">5+</option>
                </select>
              </div>

              {/* Bathrooms */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Min Bathrooms</label>
                <select
                  value={filters.bathrooms || ''}
                  onChange={(e) => updateFilters({ bathrooms: Number(e.target.value) || undefined, page: 1 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Any</option>
                  <option value="1">1+</option>
                  <option value="2">2+</option>
                  <option value="3">3+</option>
                  <option value="4">4+</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Properties Grid/List */}
      <div className={viewMode === 'grid' 
        ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' 
        : 'space-y-6'
      }>
        {properties.map((property) => (
          <PropertyCard 
            key={property.documentId} 
            property={property} 
            viewMode={viewMode}
          />
        ))}
      </div>

      {/* Pagination */}
      {pagination && pagination.pageCount > 1 && (
        <div className="mt-8 flex justify-center">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
              className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Previous
            </button>
            
            {Array.from({ length: pagination.pageCount }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-2 border rounded-md ${
                  page === pagination.page
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}
            
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.pageCount}
              className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* No Results */}
      {!propertiesLoading && properties.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-600 text-lg">No properties found matching your criteria.</p>
          <button
            onClick={() => updateFilters({})}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Clear Filters
          </button>
        </div>
      )}
    </div>
  );
};

// Property Card Component
const PropertyCard: React.FC<{ property: Property; viewMode: 'grid' | 'list' }> = ({ 
  property, 
  viewMode 
}) => {
  const imageUrl = property.images?.[0]?.url || '/placeholder-property.jpg';
  
  if (viewMode === 'list') {
    return (
      <div className="bg-white rounded-lg shadow-md overflow-hidden flex">
        <div className="w-1/3">
          <img
            src={imageUrl}
            alt={property.title}
            className="w-full h-48 object-cover"
          />
        </div>
        <div className="w-2/3 p-6">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-xl font-semibold text-gray-900">{property.title}</h3>
            <span className="text-2xl font-bold text-blue-600">
              {property.currency} {property.price.toLocaleString()}
            </span>
          </div>
          <p className="text-gray-600 mb-4 line-clamp-2">{property.description}</p>
          <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-1" />
              {property.city}, {property.country}
            </div>
            <div className="flex items-center">
              <Bed className="h-4 w-4 mr-1" />
              {property.bedrooms} beds
            </div>
            <div className="flex items-center">
              <Bath className="h-4 w-4 mr-1" />
              {property.bathrooms} baths
            </div>
            <div className="flex items-center">
              <Square className="h-4 w-4 mr-1" />
              {property.area} {property.areaUnit}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      <div className="relative">
        <img
          src={imageUrl}
          alt={property.title}
          className="w-full h-48 object-cover"
        />
        {property.isLuxury && (
          <span className="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-semibold">
            Luxury
          </span>
        )}
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs flex items-center">
          <Eye className="h-3 w-3 mr-1" />
          {property.views}
        </div>
      </div>
      
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{property.title}</h3>
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{property.description}</p>
        
        <div className="flex items-center justify-between mb-3">
          <span className="text-xl font-bold text-blue-600">
            {property.currency} {property.price.toLocaleString()}
          </span>
          <span className="text-sm text-gray-500 capitalize">{property.offer}</span>
        </div>
        
        <div className="flex items-center space-x-3 text-sm text-gray-500 mb-3">
          <div className="flex items-center">
            <Bed className="h-4 w-4 mr-1" />
            {property.bedrooms}
          </div>
          <div className="flex items-center">
            <Bath className="h-4 w-4 mr-1" />
            {property.bathrooms}
          </div>
          <div className="flex items-center">
            <Square className="h-4 w-4 mr-1" />
            {property.area} {property.areaUnit}
          </div>
        </div>
        
        <div className="flex items-center text-sm text-gray-500">
          <MapPin className="h-4 w-4 mr-1" />
          {property.city}, {property.country}
        </div>
      </div>
    </div>
  );
};

export default OptimizedPropertiesPage;
