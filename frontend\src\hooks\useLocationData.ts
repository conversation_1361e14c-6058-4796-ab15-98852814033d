import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';

// Fetch unique cities from properties
export const useCities = () => {
  return useQuery({
    queryKey: ['cities'],
    queryFn: async () => {
      try {
        const response = await api.get('/properties', {
          params: {
            fields: ['city'],
            pagination: { limit: 1000 }
          }
        });
        
        // Extract unique cities
        const cities = [...new Set(
          response.data.data
            .map((property: any) => property.city)
            .filter(Boolean)
        )].sort();
        
        return cities;
      } catch (error) {
        console.error('Error fetching cities:', error);
        return [];
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Fetch neighborhoods for a specific city
export const useNeighborhoods = (city?: string) => {
  return useQuery({
    queryKey: ['neighborhoods', city],
    queryFn: async () => {
      if (!city) return [];
      
      try {
        const response = await api.get('/properties', {
          params: {
            fields: ['neighborhood'],
            filters: {
              city: { $eq: city }
            },
            pagination: { limit: 1000 }
          }
        });
        
        // Extract unique neighborhoods
        const neighborhoods = [...new Set(
          response.data.data
            .map((property: any) => property.neighborhood)
            .filter(Boolean)
        )].sort();
        
        return neighborhoods;
      } catch (error) {
        console.error('Error fetching neighborhoods:', error);
        return [];
      }
    },
    enabled: !!city,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Fetch countries from properties
export const useCountries = () => {
  return useQuery({
    queryKey: ['countries'],
    queryFn: async () => {
      try {
        const response = await api.get('/properties', {
          params: {
            fields: ['country'],
            pagination: { limit: 1000 }
          }
        });
        
        // Extract unique countries
        const countries = [...new Set(
          response.data.data
            .map((property: any) => property.country)
            .filter(Boolean)
        )].sort();
        
        return countries;
      } catch (error) {
        console.error('Error fetching countries:', error);
        return [];
      }
    },
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });
};

// Get location suggestions based on search term
export const useLocationSuggestions = (searchTerm: string) => {
  return useQuery({
    queryKey: ['location-suggestions', searchTerm],
    queryFn: async () => {
      if (!searchTerm || searchTerm.length < 2) return [];
      
      try {
        const response = await api.get('/properties', {
          params: {
            fields: ['city', 'neighborhood', 'address'],
            filters: {
              $or: [
                { city: { $containsi: searchTerm } },
                { neighborhood: { $containsi: searchTerm } },
                { address: { $containsi: searchTerm } }
              ]
            },
            pagination: { limit: 50 }
          }
        });
        
        const suggestions = [];
        const seen = new Set();
        
        response.data.data.forEach((property: any) => {
          // Add city suggestions
          if (property.city && property.city.toLowerCase().includes(searchTerm.toLowerCase())) {
            const suggestion = { type: 'city', value: property.city, label: property.city };
            const key = `${suggestion.type}-${suggestion.value}`;
            if (!seen.has(key)) {
              suggestions.push(suggestion);
              seen.add(key);
            }
          }
          
          // Add neighborhood suggestions
          if (property.neighborhood && property.neighborhood.toLowerCase().includes(searchTerm.toLowerCase())) {
            const suggestion = { 
              type: 'neighborhood', 
              value: property.neighborhood, 
              label: `${property.neighborhood}, ${property.city}` 
            };
            const key = `${suggestion.type}-${suggestion.value}`;
            if (!seen.has(key)) {
              suggestions.push(suggestion);
              seen.add(key);
            }
          }
          
          // Add address suggestions
          if (property.address && property.address.toLowerCase().includes(searchTerm.toLowerCase())) {
            const suggestion = { 
              type: 'address', 
              value: property.address, 
              label: `${property.address}, ${property.city}` 
            };
            const key = `${suggestion.type}-${suggestion.value}`;
            if (!seen.has(key)) {
              suggestions.push(suggestion);
              seen.add(key);
            }
          }
        });
        
        return suggestions.slice(0, 10); // Limit to 10 suggestions
      } catch (error) {
        console.error('Error fetching location suggestions:', error);
        return [];
      }
    },
    enabled: searchTerm.length >= 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
