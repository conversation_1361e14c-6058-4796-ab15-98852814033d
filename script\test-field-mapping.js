/**
 * Test script to verify property field mapping from 'status' to 'offer'
 * This tests that the backend schema uses 'offer' field and frontend components work correctly
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:1337';
const API_URL = `${BASE_URL}/api`;

// Test user credentials
const TEST_USER = {
  identifier: '<EMAIL>',
  password: 'Mb123321'
};

let authToken = '';

async function login() {
  try {
    console.log('🔐 Logging in test user...');
    const response = await axios.post(`${API_URL}/auth/local`, TEST_USER);
    authToken = response.data.jwt;
    console.log('✅ Login successful');
    return response.data;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testPropertyFieldMapping() {
  try {
    console.log('\n📋 Testing property field mapping...');
    
    // Get properties to check field structure
    const response = await axios.get(`${API_URL}/properties`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    const properties = response.data.data;
    console.log(`📊 Found ${properties.length} properties`);
    
    if (properties.length > 0) {
      const firstProperty = properties[0];
      console.log('\n🔍 Checking first property field structure:');
      console.log('- ID:', firstProperty.id);
      console.log('- DocumentId:', firstProperty.documentId);
      console.log('- Title:', firstProperty.title);
      
      // Check if 'offer' field exists (new field)
      if (firstProperty.offer !== undefined) {
        console.log('✅ OFFER field found:', firstProperty.offer);
      } else {
        console.log('❌ OFFER field missing');
      }
      
      // Check if 'status' field still exists (should not)
      if (firstProperty.status !== undefined) {
        console.log('⚠️  STATUS field still exists:', firstProperty.status);
      } else {
        console.log('✅ STATUS field correctly removed');
      }
      
      // Test property search with offer filter
      console.log('\n🔍 Testing property search with offer filter...');
      const searchResponse = await axios.get(`${API_URL}/properties/search`, {
        params: {
          offerType: 'for-sale'
        },
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      console.log(`📊 Search results: ${searchResponse.data.data.length} properties for 'for-sale'`);
      
      if (searchResponse.data.data.length > 0) {
        const searchProperty = searchResponse.data.data[0];
        console.log('✅ Search working with offer field:', searchProperty.offer);
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Property field mapping test failed:', error.response?.data || error.message);
    return false;
  }
}

async function testPropertyEdit() {
  try {
    console.log('\n✏️  Testing property edit functionality...');
    
    // Get properties for editing
    const response = await axios.get(`${API_URL}/properties`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    const properties = response.data.data;
    if (properties.length === 0) {
      console.log('⚠️  No properties found for edit test');
      return true;
    }
    
    const testProperty = properties[0];
    console.log(`🎯 Testing edit for property: ${testProperty.title}`);
    
    // Test getForEdit endpoint
    const editResponse = await axios.get(`${API_URL}/properties/${testProperty.documentId}/edit`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    const editData = editResponse.data;
    console.log('✅ Property edit data retrieved successfully');
    console.log('- Offer field:', editData.offer);
    
    if (editData.offer) {
      console.log('✅ Edit endpoint returns offer field correctly');
    } else {
      console.log('❌ Edit endpoint missing offer field');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Property edit test failed:', error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Property Field Mapping Tests\n');
  
  try {
    // Login first
    await login();
    
    // Run tests
    const test1 = await testPropertyFieldMapping();
    const test2 = await testPropertyEdit();
    
    console.log('\n📊 Test Results Summary:');
    console.log(`- Field Mapping Test: ${test1 ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`- Property Edit Test: ${test2 ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (test1 && test2) {
      console.log('\n🎉 All tests PASSED! Field mapping from status to offer is working correctly.');
    } else {
      console.log('\n⚠️  Some tests FAILED. Please check the issues above.');
    }
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
  }
}

// Run the tests
runTests();
