// Google Maps integration for neighborhood detection and address services

export interface NeighborhoodInfo {
  name: string;
  type: string; // 'neighborhood', 'sublocality', 'locality', etc.
  formatted_address: string;
  place_id?: string;
}

export interface AddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

export interface GeocodeResult {
  formatted_address: string;
  address_components: AddressComponent[];
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  place_id: string;
  types: string[];
}

export interface LocationInfo {
  address: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  neighborhoods: NeighborhoodInfo[];
  coordinates: {
    lat: number;
    lng: number;
  };
}

class GoogleMapsService {
  private apiKey: string;

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '';
  }

  /**
   * Get neighborhood information from coordinates using reverse geocoding
   */
  async getNeighborhoodsFromCoordinates(lat: number, lng: number): Promise<NeighborhoodInfo[]> {
    if (!this.apiKey) {
      console.warn('Google Maps API key not configured');
      return [];
    }

    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${this.apiKey}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch geocoding data');
      }

      const data = await response.json();

      if (data.status !== 'OK' || !data.results?.length) {
        return [];
      }

      const neighborhoods: NeighborhoodInfo[] = [];

      // Extract neighborhood information from all results
      data.results.forEach((result: GeocodeResult) => {
        result.address_components.forEach((component: AddressComponent) => {
          // Look for neighborhood-related types
          const neighborhoodTypes = [
            'neighborhood',
            'sublocality',
            'sublocality_level_1',
            'sublocality_level_2',
            'locality'
          ];

          const matchedType = component.types.find(type => neighborhoodTypes.includes(type));
          
          if (matchedType && !neighborhoods.some(n => n.name === component.long_name)) {
            neighborhoods.push({
              name: component.long_name,
              type: matchedType,
              formatted_address: result.formatted_address,
              place_id: result.place_id
            });
          }
        });
      });

      return neighborhoods;
    } catch (error) {
      console.error('Error getting neighborhoods from coordinates:', error);
      return [];
    }
  }

  /**
   * Get location information from address string
   */
  async getLocationFromAddress(address: string): Promise<LocationInfo | null> {
    if (!this.apiKey) {
      console.warn('Google Maps API key not configured');
      return null;
    }

    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${this.apiKey}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch geocoding data');
      }

      const data = await response.json();

      if (data.status !== 'OK' || !data.results?.length) {
        return null;
      }

      const result = data.results[0];
      const components = result.address_components;

      // Extract address components
      const getComponent = (types: string[]) => {
        const component = components.find((comp: AddressComponent) =>
          comp.types.some(type => types.includes(type))
        );
        return component?.long_name || '';
      };

      const locationInfo: LocationInfo = {
        address: result.formatted_address,
        city: getComponent(['locality', 'administrative_area_level_2']),
        state: getComponent(['administrative_area_level_1']),
        country: getComponent(['country']),
        postal_code: getComponent(['postal_code']),
        neighborhoods: [],
        coordinates: {
          lat: result.geometry.location.lat,
          lng: result.geometry.location.lng
        }
      };

      // Get neighborhoods for this location
      locationInfo.neighborhoods = await this.getNeighborhoodsFromCoordinates(
        locationInfo.coordinates.lat,
        locationInfo.coordinates.lng
      );

      return locationInfo;
    } catch (error) {
      console.error('Error getting location from address:', error);
      return null;
    }
  }

  /**
   * Search for places/addresses with autocomplete
   */
  async searchPlaces(query: string): Promise<any[]> {
    if (!this.apiKey || !query.trim()) {
      return [];
    }

    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(query)}&key=${this.apiKey}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch places data');
      }

      const data = await response.json();

      if (data.status !== 'OK') {
        return [];
      }

      return data.predictions || [];
    } catch (error) {
      console.error('Error searching places:', error);
      return [];
    }
  }

  /**
   * Get nearby neighborhoods within a radius (in meters)
   */
  async getNearbyNeighborhoods(lat: number, lng: number, radius: number = 5000): Promise<NeighborhoodInfo[]> {
    if (!this.apiKey) {
      console.warn('Google Maps API key not configured');
      return [];
    }

    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${lat},${lng}&radius=${radius}&type=neighborhood&key=${this.apiKey}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch nearby places');
      }

      const data = await response.json();

      if (data.status !== 'OK') {
        return [];
      }

      return data.results.map((place: any) => ({
        name: place.name,
        type: 'neighborhood',
        formatted_address: place.vicinity,
        place_id: place.place_id
      }));
    } catch (error) {
      console.error('Error getting nearby neighborhoods:', error);
      return [];
    }
  }
}

export const googleMapsService = new GoogleMapsService();
