const fs = require('fs');
const path = require('path');

// Create sample real estate images (simple colored rectangles as placeholders)
function createSampleImages() {
  const imagesDir = path.join(__dirname, 'images');
  
  // Ensure images directory exists
  if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir);
  }

  // Sample image data (1x1 pixel PNGs in different colors)
  const imageData = {
    'villa-1.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==', // Red
    'villa-2.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==', // Transparent
    'apartment-1.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', // Blue
    'apartment-2.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+fAQAEhAIBjshbjwAAAABJRU5ErkJggg==', // Green
    'penthouse-1.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==', // Black
    'penthouse-2.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP///8/AwAC/gD/2+XkjwAAAABJRU5ErkJggg==', // White
    'studio-1.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==', // Red
    'studio-2.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', // Blue
    'townhouse-1.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+fAQAEhAIBjshbjwAAAABJRU5ErkJggg==', // Green
    'townhouse-2.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==', // Black
    'duplex-1.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP///8/AwAC/gD/2+XkjwAAAABJRU5ErkJggg==', // White
    'duplex-2.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==', // Red
    'commercial-1.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', // Blue
    'commercial-2.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+fAQAEhAIBjshbjwAAAABJRU5ErkJggg==', // Green
    'land-1.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==', // Black
    'beachfront-1.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', // Blue
    'beachfront-2.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+fAQAEhAIBjshbjwAAAABJRU5ErkJggg==', // Green
    'luxury-1.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP///8/AwAC/gD/2+XkjwAAAABJRU5ErkJggg==', // White
    'luxury-2.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==', // Black
    'modern-1.png': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==', // Red
  };

  console.log('🖼️  Creating sample real estate images...');
  
  Object.entries(imageData).forEach(([filename, base64Data]) => {
    const imagePath = path.join(imagesDir, filename);
    const imageBuffer = Buffer.from(base64Data, 'base64');
    fs.writeFileSync(imagePath, imageBuffer);
    console.log(`   ✅ Created: ${filename}`);
  });

  console.log(`\n🎉 Created ${Object.keys(imageData).length} sample images in ./images/ folder`);
  console.log('\n📝 You can replace these with real images by:');
  console.log('   1. Adding your own .jpg, .png, or .webp files to the ./images/ folder');
  console.log('   2. The script will automatically use all images in that folder');
  console.log('   3. Recommended: Add high-quality real estate photos for better results');
}

createSampleImages();
