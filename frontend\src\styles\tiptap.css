/* TipTap Editor Styles */
.ProseMirror {
  outline: none;
  padding: 0;
  margin: 0;
}

.ProseMirror p {
  margin: 0.5rem 0;
}

.ProseMirror p:first-child {
  margin-top: 0;
}

.ProseMirror p:last-child {
  margin-bottom: 0;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  margin: 1rem 0 0.5rem 0;
  font-weight: bold;
  line-height: 1.2;
}

.ProseMirror h1:first-child,
.ProseMirror h2:first-child,
.ProseMirror h3:first-child,
.ProseMirror h4:first-child,
.ProseMirror h5:first-child,
.ProseMirror h6:first-child {
  margin-top: 0;
}

.ProseMirror h1 {
  font-size: 1.875rem;
}

.ProseMirror h2 {
  font-size: 1.5rem;
}

.ProseMirror h3 {
  font-size: 1.25rem;
}

.ProseMirror h4 {
  font-size: 1.125rem;
}

.ProseMirror h5 {
  font-size: 1rem;
}

.ProseMirror h6 {
  font-size: 0.875rem;
}

.ProseMirror ul,
.ProseMirror ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.ProseMirror ul {
  list-style-type: disc;
}

.ProseMirror ol {
  list-style-type: decimal;
}

.ProseMirror li {
  margin: 0.25rem 0;
}

.ProseMirror blockquote {
  border-left: 4px solid #e5e7eb;
  margin: 1rem 0;
  padding-left: 1rem;
  font-style: italic;
  color: #6b7280;
}

.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
}

.ProseMirror a:hover {
  color: #1d4ed8;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 0.5rem 0;
}

.ProseMirror strong {
  font-weight: bold;
}

.ProseMirror em {
  font-style: italic;
}

.ProseMirror code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.ProseMirror pre {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.ProseMirror pre code {
  background-color: transparent;
  padding: 0;
}

/* Placeholder styles */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Focus styles */
.ProseMirror:focus {
  outline: none;
}

/* Selection styles */
.ProseMirror ::selection {
  background-color: #dbeafe;
}

/* Table styles (if needed in the future) */
.ProseMirror table {
  border-collapse: collapse;
  margin: 1rem 0;
  width: 100%;
}

.ProseMirror table td,
.ProseMirror table th {
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
  text-align: left;
}

.ProseMirror table th {
  background-color: #f9fafb;
  font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .ProseMirror h1 {
    font-size: 1.5rem;
  }
  
  .ProseMirror h2 {
    font-size: 1.25rem;
  }
  
  .ProseMirror h3 {
    font-size: 1.125rem;
  }
}
