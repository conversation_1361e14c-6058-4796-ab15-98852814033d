'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { projectsAPI } from '@/lib/api';

// Query Keys
export const PROJECT_KEYS = {
  all: ['projects'] as const,
  lists: () => [...PROJECT_KEYS.all, 'list'] as const,
  list: (filters: any) => [...PROJECT_KEYS.lists(), filters] as const,
  details: () => [...PROJECT_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...PROJECT_KEYS.details(), id] as const,
  featured: () => [...PROJECT_KEYS.all, 'featured'] as const,
  properties: (id: string) => [...PROJECT_KEYS.detail(id), 'properties'] as const,
};

// Hook for fetching all projects
export const useProjects = (filters?: any) => {
  return useQuery({
    queryKey: PROJECT_KEYS.list(filters),
    queryFn: () => projectsAPI.getAll(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook for single project
export const useProject = (id: string, enabled = true) => {
  return useQuery({
    queryKey: PROJECT_KEYS.detail(id),
    queryFn: () => projectsAPI.getOne(id),
    enabled: !!id && enabled,
    staleTime: 15 * 60 * 1000, // 15 minutes for individual projects
  });
};

// Hook for featured projects
export const useFeaturedProjects = () => {
  return useQuery({
    queryKey: PROJECT_KEYS.featured(),
    queryFn: projectsAPI.getFeatured,
    staleTime: 20 * 60 * 1000, // 20 minutes for featured
  });
};

// Hook for project properties
export const useProjectProperties = (id: string, enabled = true) => {
  return useQuery({
    queryKey: PROJECT_KEYS.properties(id),
    queryFn: () => projectsAPI.getProperties(id),
    enabled: !!id && enabled,
    staleTime: 10 * 60 * 1000,
  });
};
