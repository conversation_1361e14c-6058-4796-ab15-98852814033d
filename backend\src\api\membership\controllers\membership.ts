/**
 * membership controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::membership.membership', ({ strapi }) => ({
  // Get all active memberships
  async find(ctx) {
    try {
      const { query } = ctx;
      const filters = {
        isActive: true,
        ...(query?.filters && typeof query.filters === 'object' ? query.filters : {}),
      };

      const memberships = await strapi.entityService.findMany('api::membership.membership', {
        ...query,
        filters,
        sort: { priority: 'desc' },
        populate: {
          users: {
            fields: ['id', 'username', 'email']
          }
        }
      });
      return { data: memberships };
    } catch (error) {
      console.error('Error in membership find:', error);
      return { data: [], meta: { pagination: { total: 0 } } };
    }
  },

  // Get membership by ID
  async findOne(ctx) {
    try {
      const { id } = ctx.params;
      const membership = await strapi.entityService.findOne('api::membership.membership', id, {
        populate: {
          users: {
            fields: ['id', 'username', 'email']
          }
        }
      });
      return { data: membership };
    } catch (error) {
      console.error('Error in membership findOne:', error);
      return ctx.notFound('Membership not found');
    }
  },

  // Subscribe user to membership
  async subscribe(ctx) {
    const user = ctx.state.user;
    const { membershipId } = ctx.request.body;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    if (!membershipId) {
      return ctx.badRequest('Membership ID is required');
    }

    try {
      // Get membership details
      const membership = await strapi.entityService.findOne('api::membership.membership', membershipId);
      
      if (!membership || !membership.isActive) {
        return ctx.badRequest('Invalid or inactive membership plan');
      }

      // Update user's membership
      const updatedUser = await strapi.entityService.update('plugin::users-permissions.user', user.id, {
        data: {
          membership: membershipId,
          membershipStartDate: new Date(),
          membershipEndDate: calculateEndDate(membership.duration),
        }
      });

      return {
        data: {
          message: 'Successfully subscribed to membership plan',
          user: updatedUser,
          membership: membership
        }
      };
    } catch (error) {
      console.error('Error in membership subscribe:', error);
      return ctx.internalServerError('Failed to subscribe to membership');
    }
  },

  // Get user's current membership
  async myMembership(ctx) {
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      const userWithMembership = await strapi.entityService.findOne('plugin::users-permissions.user', user.id, {
        populate: {
          membership: true
        }
      }) as any;

      return {
        data: {
          membership: userWithMembership?.membership,
          membershipStartDate: userWithMembership?.membershipStartDate,
          membershipEndDate: userWithMembership?.membershipEndDate,
        }
      };
    } catch (error) {
      console.error('Error in myMembership:', error);
      return ctx.internalServerError('Failed to get membership information');
    }
  }
}));

// Helper function to calculate membership end date
function calculateEndDate(duration: string): Date {
  const now = new Date();
  switch (duration) {
    case 'monthly':
      return new Date(now.setMonth(now.getMonth() + 1));
    case 'quarterly':
      return new Date(now.setMonth(now.getMonth() + 3));
    case 'yearly':
      return new Date(now.setFullYear(now.getFullYear() + 1));
    case 'lifetime':
      return new Date(now.setFullYear(now.getFullYear() + 100)); // 100 years from now
    default:
      return new Date(now.setMonth(now.getMonth() + 1));
  }
}
