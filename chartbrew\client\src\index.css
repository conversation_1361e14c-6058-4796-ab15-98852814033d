body {
  margin: 0;
  padding: 0;
  /* background-color: #F1F5F9; */
}

a {
  color: #f69977;
}

a:hover {
  color: #cf6b4e;
}

.Resizer {
  background: #000;
  opacity: .2;
  z-index: 1;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -moz-background-clip: padding;
  -webkit-background-clip: padding;
  background-clip: padding-box;
}

.Resizer:hover {
  -webkit-transition: all 2s ease;
  transition: all 2s ease;
}

.Resizer.horizontal {
  height: 11px;
  margin: -5px 0;
  border-top: 5px solid rgba(255, 255, 255, 0);
  border-bottom: 5px solid rgba(255, 255, 255, 0);
  /* cursor: row-resize; */
  width: 100%;
}

.Resizer.horizontal:hover {
  border-top: 5px solid rgba(0, 0, 0, 0.5);
  border-bottom: 5px solid rgba(0, 0, 0, 0.5);
}

.Resizer.vertical {
  width: 11px;
  margin: 0 -5px;
  border-left: 5px solid rgba(255, 255, 255, 0);
  border-right: 5px solid rgba(255, 255, 255, 0);
  /* cursor: col-resize; */
}

.Resizer.vertical:hover {
  border-left: 5px solid rgba(0, 0, 0, 0.5);
  border-right: 5px solid rgba(0, 0, 0, 0.5);
}

.Resizer.disabled {
  cursor: not-allowed;
}

.Resizer.disabled:hover {
  border-color: transparent;
}

.Pane1 {
  /* display: flex; */
  min-height: 0;
}

.Pane2 {
  /* display: flex; */
  min-height: 0;
  overflow-x: hidden;
  overflow-y: auto;
}

.ui.card.project-segment:hover {
  box-shadow: 0 3px 3px 0 #f69977, 0 0 0 3px #f69977 !important;
}

.rdrInputRange span {
  color: black;
}

.chart-kpi {
  border-radius: 6px;
  mask-image: -webkit-gradient(linear, left top,
      left bottom, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.2)));
  -webkit-mask-image: -webkit-gradient(linear, left top,
      left bottom, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.2)));
}

@keyframes pulse {
  0% {
    transform: scale(0.97);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.3);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 5px rgba(0, 0, 0, 0);
  }

  100% {
    transform: scale(0.97);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}

/* Style for the placeholder */
.react-grid-item.react-grid-placeholder {
  background-color: rgba(100, 149, 237, 0.2); /* Lighter blue, semi-transparent */
  border: 2px dashed #6495ED; /* Lighter blue border */
  border-radius: 1rem;
}

/* Style for the item being dragged */
.react-grid-item.react-draggable-dragging {
  transition: all 0.2s ease;
  box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.3); /* Slightly darker shadow for depth */
}

/* Style for resize handles (optional) */
.react-resizable-handle::after {
  border-color: #6495ED; /* Lighter blue */
}

/* Styles for dark mode */
@media (prefers-color-scheme: dark) {
  .react-grid-item.react-grid-placeholder {
    background-color: rgba(173, 216, 230, 0.6);
    /* Brighter and more vivid blue for dark mode */
    border: 2px dashed #ADD8E6;
    border-radius: 1rem;
  }

  .react-grid-item.react-draggable-dragging {
    box-shadow: 0px 10px 15px rgba(255, 255, 255, 0.3);
    /* Lighter and more visible shadow for dark mode */
  }

  .react-resizable-handle::after {
    border-color: #ADD8E6;
  }
}

.react-resizable-handle {
  background: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  /* Adjust based on your icon size */
  height: 20px;
  bottom: 0;
  right: 0;
}

.react-grid-item>.react-resizable-handle::after {
  background: none;
  border: none;
  /* ... other styles ... */
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}