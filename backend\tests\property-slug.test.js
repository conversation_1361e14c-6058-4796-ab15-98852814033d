/**
 * Tests for property slug functionality
 */

const { generateSlug, ensureUniqueSlug } = require('../scripts/generate-property-slugs');

describe('Property Slug Generation', () => {
  describe('generateSlug', () => {
    test('should generate basic slug from title', () => {
      expect(generateSlug('Luxury Villa in Dubai')).toBe('luxury-villa-in-dubai');
    });

    test('should handle special characters', () => {
      expect(generateSlug('Property #123 - $500K!')).toBe('property-123-500k');
    });

    test('should handle multiple spaces', () => {
      expect(generateSlug('Beautiful   Apartment    Downtown')).toBe('beautiful-apartment-downtown');
    });

    test('should handle leading/trailing spaces and hyphens', () => {
      expect(generateSlug('  -Beautiful Apartment-  ')).toBe('beautiful-apartment');
    });

    test('should handle empty string', () => {
      expect(generateSlug('')).toBe('');
    });

    test('should handle numbers and letters', () => {
      expect(generateSlug('2 Bedroom Apartment 123')).toBe('2-bedroom-apartment-123');
    });
  });

  describe('ensureUniqueSlug', () => {
    // Mock Strapi for testing
    const mockStrapi = {
      entityService: {
        findMany: jest.fn()
      }
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('should return original slug if unique', async () => {
      mockStrapi.entityService.findMany.mockResolvedValue([]);
      
      const result = await ensureUniqueSlug(mockStrapi, 'luxury-villa');
      expect(result).toBe('luxury-villa');
    });

    test('should append number if slug exists', async () => {
      mockStrapi.entityService.findMany
        .mockResolvedValueOnce([{ id: 1 }]) // First call finds existing
        .mockResolvedValueOnce([]); // Second call finds none
      
      const result = await ensureUniqueSlug(mockStrapi, 'luxury-villa');
      expect(result).toBe('luxury-villa-1');
    });

    test('should increment number until unique', async () => {
      mockStrapi.entityService.findMany
        .mockResolvedValueOnce([{ id: 1 }]) // luxury-villa exists
        .mockResolvedValueOnce([{ id: 2 }]) // luxury-villa-1 exists
        .mockResolvedValueOnce([{ id: 3 }]) // luxury-villa-2 exists
        .mockResolvedValueOnce([]); // luxury-villa-3 is unique
      
      const result = await ensureUniqueSlug(mockStrapi, 'luxury-villa');
      expect(result).toBe('luxury-villa-3');
    });

    test('should exclude current property when updating', async () => {
      mockStrapi.entityService.findMany.mockResolvedValue([]);
      
      await ensureUniqueSlug(mockStrapi, 'luxury-villa', 'current-doc-id');
      
      expect(mockStrapi.entityService.findMany).toHaveBeenCalledWith(
        'api::property.property',
        {
          filters: {
            slug: { $eq: 'luxury-villa' },
            documentId: { $ne: 'current-doc-id' }
          },
          fields: ['id', 'documentId', 'slug']
        }
      );
    });
  });
});

// Integration test examples (would require actual Strapi instance)
describe('Property Slug Integration', () => {
  test.skip('should create property with auto-generated slug', async () => {
    // This would test the actual lifecycle hooks
    // Requires running Strapi instance
  });

  test.skip('should update slug when title changes', async () => {
    // This would test the beforeUpdate lifecycle
    // Requires running Strapi instance
  });

  test.skip('should find property by slug via API', async () => {
    // This would test the findBySlug controller method
    // Requires running Strapi instance
  });
});
