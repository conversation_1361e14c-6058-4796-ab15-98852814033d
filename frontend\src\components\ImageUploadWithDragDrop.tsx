'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Upload, X, Image as ImageIcon, Move, Star } from 'lucide-react';

interface ImageFile {
  file: File;
  preview: string;
  id: string;
  isExisting?: boolean;
  uploading?: boolean;
  uploadProgress?: number;
  error?: string;
  isPrimary?: boolean;
}

interface ExistingImage {
  id: number;
  url: string;
  name: string;
  alternativeText?: string;
  isPrimary?: boolean;
}

interface ImageUploadWithDragDropProps {
  onImagesChange: (files: FileList | null) => void;
  existingImages?: ExistingImage[];
  onExistingImagesChange?: (images: ExistingImage[]) => void;
  maxImages?: number;
  className?: string;
}

const ImageUploadWithDragDrop: React.FC<ImageUploadWithDragDropProps> = ({
  onImagesChange,
  existingImages = [],
  onExistingImagesChange,
  maxImages = 10,
  className = ''
}) => {
  const [images, setImages] = useState<ImageFile[]>([]);
  const [dragOver, setDragOver] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateId = () => Math.random().toString(36).substr(2, 9);

  const simulateUploadProgress = (imageId: string, delay: number = 0) => {
    setTimeout(() => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 15 + 5; // Random progress between 5-20%

        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);

          // Mark upload as complete
          setImages(prev => prev.map(img =>
            img.id === imageId
              ? { ...img, uploading: false, uploadProgress: 100 }
              : img
          ));

          // Check if all uploads are complete
          setTimeout(() => {
            setImages(prev => {
              const allComplete = prev.every(img => !img.uploading);
              if (allComplete) {
                setIsUploading(false);
              }
              return prev;
            });
          }, 500);
        } else {
          // Update progress
          setImages(prev => prev.map(img =>
            img.id === imageId
              ? { ...img, uploadProgress: Math.min(progress, 100) }
              : img
          ));
        }
      }, 100 + Math.random() * 200); // Random interval between 100-300ms
    }, delay);
  };

  // Convert existing images to ImageFile format for display
  useEffect(() => {
    const existingImageFiles: ImageFile[] = existingImages.map((img, index) => ({
      file: null as any, // No file for existing images
      preview: getImageUrl(img),
      id: `existing-${img.id}`,
      isExisting: true,
      isPrimary: img.isPrimary || index === 0 // First image is primary if none specified
    }));

    // Only update if the existing images have actually changed
    setImages(prev => {
      const prevExistingIds = prev.filter(img => img.isExisting).map(img => img.id);
      const newExistingIds = existingImageFiles.map(img => img.id);

      // If the existing images haven't changed, keep the current order
      if (prevExistingIds.length === newExistingIds.length &&
          prevExistingIds.every((id, index) => id === newExistingIds[index])) {
        return prev;
      }

      // Merge existing images with any new files that were added
      const newFiles = prev.filter(img => !img.isExisting);
      return [...existingImageFiles, ...newFiles];
    });
  }, [existingImages]);

  const getImageUrl = (image: ExistingImage) => {
    if (image.url) {
      return `${process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337'}${image.url}`;
    }
    return '/api/placeholder/400/300';
  };

  const createImageFile = (file: File, isPrimary: boolean = false): ImageFile => ({
    file,
    preview: URL.createObjectURL(file),
    id: generateId(),
    uploading: false,
    uploadProgress: 0,
    error: undefined,
    isPrimary
  });

  const updateFileList = useCallback((newImages: ImageFile[]) => {
    // Create a new FileList-like object only for new files (not existing images)
    const dt = new DataTransfer();
    const newFiles = newImages.filter(img => !img.isExisting && img.file);
    newFiles.forEach(img => dt.items.add(img.file));
    onImagesChange(dt.files.length > 0 ? dt.files : null);

    // Update existing images separately - preserve order from newImages array
    if (onExistingImagesChange) {
      const existingImagesOnly = newImages
        .filter(img => img.isExisting)
        .map((img) => {
          const originalImage = existingImages.find(existing => `existing-${existing.id}` === img.id);
          if (originalImage) {
            return {
              ...originalImage,
              isPrimary: img.isPrimary
            };
          }
          return originalImage;
        })
        .filter(img => img); // Remove any undefined entries
      onExistingImagesChange(existingImagesOnly);
    }
  }, [onImagesChange, onExistingImagesChange, existingImages]);

  const handleFiles = (files: FileList) => {
    if (isUploading) return; // Prevent new uploads while uploading

    const newImages: ImageFile[] = [];
    const remainingSlots = maxImages - images.length;
    const hasPrimaryImage = images.some(img => img.isPrimary);

    for (let i = 0; i < Math.min(files.length, remainingSlots); i++) {
      const file = files[i];
      if (file.type.startsWith('image/')) {
        // Set first image as primary if no primary image exists
        const isPrimary = !hasPrimaryImage && i === 0 && images.length === 0;
        const imageFile = createImageFile(file, isPrimary);
        imageFile.uploading = true;
        imageFile.uploadProgress = 0;
        newImages.push(imageFile);
      }
    }

    if (newImages.length > 0) {
      const updatedImages = [...images, ...newImages];
      setImages(updatedImages);
      setIsUploading(true);

      // Simulate upload progress for each image
      newImages.forEach((imageFile, index) => {
        simulateUploadProgress(imageFile.id, index * 200); // Stagger the uploads
      });

      updateFileList(updatedImages);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFiles(files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      handleFiles(files);
    }
  };

  const removeImage = (id: string) => {
    const removedImage = images.find(img => img.id === id);
    const updatedImages = images.filter(img => img.id !== id);
    setImages(updatedImages);
    updateFileList(updatedImages);

    // Clean up object URL for new images only
    if (removedImage && !removedImage.isExisting) {
      URL.revokeObjectURL(removedImage.preview);
    }
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    const updatedImages = [...images];
    const [movedImage] = updatedImages.splice(fromIndex, 1);
    updatedImages.splice(toIndex, 0, movedImage);
    setImages(updatedImages);
    updateFileList(updatedImages);
  };

  const setPrimaryImage = (imageId: string) => {
    const updatedImages = images.map(img => ({
      ...img,
      isPrimary: img.id === imageId
    }));
    setImages(updatedImages);
    updateFileList(updatedImages);
  };

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
  };

  const handleImageDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    if (draggedIndex !== null && draggedIndex !== index) {
      moveImage(draggedIndex, index);
      setDraggedIndex(index);
    }
  };

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-3">
        Property Images
        {images.length > 0 && (
          <span className="ml-2 text-xs text-gray-500">
            ({images.length}/{maxImages} images)
          </span>
        )}
      </label>

      {/* Upload Area */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
        className={`relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all ${
          dragOver
            ? 'border-blue-500 bg-blue-50'
            : images.length >= maxImages || isUploading
            ? 'border-gray-200 bg-gray-50 cursor-not-allowed'
            : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
        }`}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileInput}
          className="hidden"
          disabled={images.length >= maxImages || isUploading}
        />
        
        {isUploading ? (
          <div className="h-8 w-8 mx-auto mb-3 border-4 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
        ) : (
          <Upload className={`h-8 w-8 mx-auto mb-3 ${
            images.length >= maxImages ? 'text-gray-400' : 'text-gray-500'
          }`} />
        )}
        
        <p className={`text-sm font-medium ${
          images.length >= maxImages || isUploading ? 'text-gray-400' : 'text-gray-700'
        }`}>
          {isUploading
            ? 'Uploading images...'
            : images.length >= maxImages
            ? 'Maximum images reached'
            : 'Drop images here or click to browse'
          }
        </p>
        <p className="text-xs text-gray-500 mt-1">
          PNG, JPG, GIF up to 10MB each
        </p>
      </div>

      {/* Image Preview Grid */}
      {images.length > 0 && (
        <div className="mt-4">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
            {images.map((image, index) => (
              <div
                key={image.id}
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragEnd={handleDragEnd}
                onDragOver={(e) => handleImageDragOver(e, index)}
                className={`relative group bg-gray-100 rounded-lg overflow-hidden aspect-square cursor-move ${
                  draggedIndex === index ? 'opacity-50' : ''
                }`}
              >
                <img
                  src={image.preview}
                  alt={`Preview ${index + 1}`}
                  className={`w-full h-full object-cover transition-opacity duration-300 ${
                    image.uploading ? 'opacity-50' : 'opacity-100'
                  }`}
                />

                {/* Upload Progress Overlay */}
                {image.uploading && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin mb-2"></div>
                      <div className="text-white text-xs font-medium">
                        {Math.round(image.uploadProgress || 0)}%
                      </div>
                    </div>
                  </div>
                )}

                {/* Upload Progress Bar */}
                {image.uploading && (
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200">
                    <div
                      className="h-full bg-blue-500 transition-all duration-300 ease-out"
                      style={{ width: `${image.uploadProgress || 0}%` }}
                    ></div>
                  </div>
                )}
                
                {/* Primary Image Badge */}
                {index === 0 && (
                  <div className="absolute top-2 left-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full flex items-center space-x-1">
                    <Star className="h-3 w-3 fill-current" />
                    <span>Primary</span>
                  </div>
                )}

                {/* Primary Image Badge */}
                {image.isPrimary && (
                  <div className="absolute top-2 left-8 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
                    <Star className="h-3 w-3 mr-1" />
                    Primary
                  </div>
                )}

                {/* Existing Image Badge */}
                {image.isExisting && (
                  <div className="absolute top-2 right-8 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                    Saved
                  </div>
                )}
                
                {/* Drag Handle */}
                <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Move className="h-4 w-4 text-white bg-black bg-opacity-50 rounded p-0.5" />
                </div>
                
                {/* Set as Primary Button */}
                {!image.isPrimary && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setPrimaryImage(image.id);
                    }}
                    className="absolute bottom-8 left-2 bg-yellow-500 text-white rounded-full p-1.5 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-yellow-600 hover:scale-110 shadow-lg"
                    title="Set as primary image"
                  >
                    <Star className="h-3 w-3" />
                  </button>
                )}

                {/* Remove Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removeImage(image.id);
                  }}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1.5 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-red-600 hover:scale-110 shadow-lg"
                  title="Remove image"
                >
                  <X className="h-3 w-3" />
                </button>
                
                {/* Image Number */}
                <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                  {index + 1}
                </div>
              </div>
            ))}
          </div>
          
          <p className="text-xs text-gray-500 mt-2">
            <strong>Tip:</strong> Drag images to reorder. Click the star icon to set an image as primary. The primary image will be displayed first in listings.
          </p>
        </div>
      )}
    </div>
  );
};

export default ImageUploadWithDragDrop;
