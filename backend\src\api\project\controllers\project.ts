/**
 * project controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::project.project', ({ strapi }) => ({
  // Custom find method
  async find(ctx) {
    const { data, meta } = await super.find(ctx);
    
    // Populate related data
    const populatedData = await Promise.all(
      data.map(async (project) => {
        const populatedProject = await strapi.entityService.findOne(
          'api::project.project',
          project.id,
          {
            populate: {
              images: true,
              floorPlans: true,
              brochure: true,
              properties: {
                fields: ['id', 'title', 'price', 'propertyType', 'offer']
              }
            }
          }
        );
        return populatedProject;
      })
    );

    return { data: populatedData, meta };
  },

  // Custom findOne method
  async findOne(ctx) {
    const { id } = ctx.params;
    
    const project = await strapi.entityService.findOne(
      'api::project.project',
      id,
      {
        populate: {
          images: true,
          floorPlans: true,
          brochure: true,
          properties: {
            populate: {
              images: true,
              owner: {
                fields: ['id', 'username', 'firstName', 'lastName', 'phone', 'company']
              }
            }
          }
        }
      }
    );

    if (!project) {
      return ctx.notFound('Project not found');
    }

    // Increment view count
    await strapi.entityService.update('api::project.project', id, {
      data: {
        views: (project.views || 0) + 1
      }
    });

    return { data: project };
  },

  // Get project properties
  async getProperties(ctx) {
    const { id } = ctx.params;
    
    const project = await strapi.entityService.findOne('api::project.project', id);
    
    if (!project) {
      return ctx.notFound('Project not found');
    }

    const properties = await strapi.entityService.findMany('api::property.property', {
      filters: { project: id },
      populate: {
        images: true,
        owner: {
          fields: ['id', 'username', 'firstName', 'lastName', 'phone', 'company']
        }
      }
    });

    return { data: properties };
  }
}));
