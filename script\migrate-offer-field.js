/**
 * Migration script to update existing properties with default offer values
 * This script sets offer = 'for-sale' for properties where offer is null
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:1337';
const API_URL = `${BASE_URL}/api`;

// Test user credentials for migration
const TEST_USER = {
  identifier: '<EMAIL>',
  password: 'Mb123321'
};

let authToken = '';

async function loginUser() {
  try {
    console.log('🔐 Logging in test user...');
    const response = await axios.post(`${API_URL}/auth/local`, TEST_USER);
    authToken = response.data.jwt;
    console.log('✅ User login successful');
    return response.data;
  } catch (error) {
    console.error('❌ User login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function migrateOfferField() {
  try {
    console.log('\n🔄 Starting offer field migration...');
    
    // Get all properties
    const response = await axios.get(`${API_URL}/properties?pagination[limit]=100`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    const properties = response.data.data;
    console.log(`📊 Found ${properties.length} properties to check`);
    
    let updatedCount = 0;
    let skippedCount = 0;
    
    for (const property of properties) {
      if (property.offer === null || property.offer === undefined) {
        try {
          console.log(`🔄 Updating property: ${property.title} (ID: ${property.documentId})`);
          
          // Update the property with default offer value
          await axios.put(`${API_URL}/properties/${property.documentId}`, {
            data: {
              offer: 'for-sale' // Default value
            }
          }, {
            headers: {
              'Authorization': `Bearer ${authToken}`,
              'Content-Type': 'application/json'
            }
          });
          
          updatedCount++;
          console.log(`✅ Updated: ${property.title}`);
          
        } catch (error) {
          console.error(`❌ Failed to update ${property.title}:`, error.response?.data || error.message);
        }
      } else {
        console.log(`⏭️  Skipping ${property.title} - already has offer: ${property.offer}`);
        skippedCount++;
      }
    }
    
    console.log('\n📊 Migration Summary:');
    console.log(`- Properties updated: ${updatedCount}`);
    console.log(`- Properties skipped: ${skippedCount}`);
    console.log(`- Total processed: ${properties.length}`);
    
    return { updatedCount, skippedCount, total: properties.length };
    
  } catch (error) {
    console.error('❌ Migration failed:', error.response?.data || error.message);
    throw error;
  }
}

async function verifyMigration() {
  try {
    console.log('\n🔍 Verifying migration results...');
    
    const response = await axios.get(`${API_URL}/properties?pagination[limit]=10`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    const properties = response.data.data;
    let nullCount = 0;
    let validCount = 0;
    
    properties.forEach(property => {
      if (property.offer === null || property.offer === undefined) {
        nullCount++;
        console.log(`⚠️  Property still has null offer: ${property.title}`);
      } else {
        validCount++;
        console.log(`✅ Property has valid offer: ${property.title} - ${property.offer}`);
      }
    });
    
    console.log('\n📊 Verification Results:');
    console.log(`- Properties with valid offer: ${validCount}`);
    console.log(`- Properties with null offer: ${nullCount}`);
    
    return nullCount === 0;
    
  } catch (error) {
    console.error('❌ Verification failed:', error.response?.data || error.message);
    return false;
  }
}

async function runMigration() {
  console.log('🚀 Starting Offer Field Migration\n');
  
  try {
    // Login as user
    await loginUser();
    
    // Run migration
    const results = await migrateOfferField();
    
    // Verify results
    const verified = await verifyMigration();
    
    if (verified && results.updatedCount > 0) {
      console.log('\n🎉 Migration completed successfully!');
      console.log('All properties now have valid offer values.');
    } else if (results.updatedCount === 0) {
      console.log('\n✅ No migration needed - all properties already have offer values.');
    } else {
      console.log('\n⚠️  Migration completed with some issues. Please check the logs above.');
    }
    
  } catch (error) {
    console.error('\n💥 Migration failed:', error.message);
  }
}

// Run the migration
runMigration();
