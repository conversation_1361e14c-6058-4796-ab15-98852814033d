import type { Schema, Struct } from '@strapi/strapi';

export interface SharedLocation extends Struct.ComponentSchema {
  collectionName: 'components_shared_locations';
  info: {
    description: 'Location information with coordinates';
    displayName: 'Location';
  };
  attributes: {
    address: Schema.Attribute.String & Schema.Attribute.Required;
    city: Schema.Attribute.String & Schema.Attribute.Required;
    country: Schema.Attribute.String & Schema.Attribute.Required;
    latitude: Schema.Attribute.Decimal;
    longitude: Schema.Attribute.Decimal;
    neighborhood: Schema.Attribute.String;
    state: Schema.Attribute.String;
    zipCode: Schema.Attribute.String;
  };
}

export interface SharedPriceRange extends Struct.ComponentSchema {
  collectionName: 'components_shared_price_ranges';
  info: {
    description: 'Price range for projects';
    displayName: 'Price Range';
  };
  attributes: {
    currency: Schema.Attribute.Enumeration<
      ['USD', 'EUR', 'GBP', 'AED', 'SAR']
    > &
      Schema.Attribute.DefaultTo<'USD'>;
    maxPrice: Schema.Attribute.Decimal & Schema.Attribute.Required;
    minPrice: Schema.Attribute.Decimal & Schema.Attribute.Required;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'shared.location': SharedLocation;
      'shared.price-range': SharedPriceRange;
    }
  }
}
