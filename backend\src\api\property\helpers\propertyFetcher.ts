/**
 * Reusable property fetching helper with configurable options
 * Provides consistent property fetching across different endpoints
 */

/**
 * Predefined populate configurations for different use cases
 */
export const POPULATE_CONFIGS = {
  // Minimal population for list views
  minimal: {
    images: {
      fields: ['id', 'url', 'alternativeText']
    }
  },
  
  // Standard population for list views
  list: {
    images: true,
    owner: {
      fields: ['id', 'username', 'email', 'firstName', 'lastName']
    },
    agent: {
      fields: ['id', 'username', 'email', 'firstName', 'lastName']
    },
    project: {
      fields: ['id', 'name', 'slug']
    }
  },
  
  // Detailed population for single property views
  detailed: {
    images: true,
    floorPlan: true,
    owner: {
      fields: ['id', 'username', 'email', 'firstName', 'lastName', 'phone', 'company']
    },
    agent: {
      fields: ['id', 'username', 'email', 'firstName', 'lastName', 'phone', 'company']
    },
    project: {
      fields: ['id', 'name', 'slug', 'description']
    }
  },
  
  // Dashboard population for user property management
  dashboard: {
    images: true,
    project: {
      fields: ['id', 'name', 'slug']
    }
  },
  
  // Featured properties population
  featured: {
    images: true,
    floorPlan: true,
    owner: {
      fields: ['id', 'username', 'firstName', 'lastName']
    },
    agent: {
      fields: ['id', 'username', 'firstName', 'lastName']
    }
  }
};

/**
 * Parse pagination parameters from query
 */
export function parsePagination(query: any = {}, defaults = { page: 1, pageSize: 20 }) {
  const pagination = query.pagination || {};
  return {
    page: parseInt(pagination.page) || defaults.page,
    pageSize: parseInt(pagination.pageSize) || defaults.pageSize
  };
}

/**
 * Parse and combine filters
 */
export function parseFilters(query: any = {}, additionalFilters: any = {}) {
  const queryFilters = query.filters || {};
  return {
    ...queryFilters,
    ...additionalFilters
  };
}

/**
 * Parse sort parameters
 */
export function parseSort(query: any = {}, defaultSort: any = { createdAt: 'desc' }) {
  return query.sort || defaultSort;
}

/**
 * Calculate relevance score for a property
 */
function calculateRelevanceScore(property: any, searchTerm?: string): number {
  let score = 0;

  // Featured properties get a significant boost
  if (property.featured) {
    score += 100;
  }

  // Views/popularity factor (normalize to 0-50 range)
  const views = property.views || 0;
  score += Math.min(views / 10, 50);

  // Recency factor (newer properties get boost, max 30 points)
  const createdAt = new Date(property.createdAt);
  const daysSinceCreated = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
  const recencyScore = Math.max(0, 30 - (daysSinceCreated / 7)); // Lose 1 point per week
  score += recencyScore;

  // Search term matching (if search term provided)
  if (searchTerm && searchTerm.trim()) {
    const searchLower = searchTerm.toLowerCase();
    const title = (property.title || '').toLowerCase();
    const description = (property.description || '').toLowerCase();
    const address = (property.address || '').toLowerCase();
    const city = (property.city || '').toLowerCase();

    // Title match gets highest boost
    if (title.includes(searchLower)) {
      score += 50;
    }

    // Description match gets medium boost
    if (description.includes(searchLower)) {
      score += 25;
    }

    // Location match gets lower boost
    if (address.includes(searchLower) || city.includes(searchLower)) {
      score += 15;
    }
  }

  return score;
}

/**
 * Sort properties by relevance
 */
function sortByRelevance(properties: any[], searchTerm?: string, order: 'asc' | 'desc' = 'desc'): any[] {
  const propertiesWithScore = properties.map(property => ({
    ...property,
    _relevanceScore: calculateRelevanceScore(property, searchTerm)
  }));

  return propertiesWithScore.sort((a, b) => {
    const scoreA = a._relevanceScore;
    const scoreB = b._relevanceScore;

    if (order === 'desc') {
      return scoreB - scoreA;
    } else {
      return scoreA - scoreB;
    }
  }).map(({ _relevanceScore, ...property }) => property); // Remove the score from final result
}

/**
 * Main property fetching function with configurable options
 */
export async function fetchProperties({
  query = {},
  filters = {},
  populationType = 'list',
  customPopulate = null,
  pagination = { page: 1, pageSize: 20 },
  defaultSort = { createdAt: 'desc' },
  includeCount = true
}: {
  query?: any;
  filters?: any;
  populationType?: string;
  customPopulate?: any;
  pagination?: { page: number; pageSize: number };
  defaultSort?: any;
  includeCount?: boolean;
}) {
  try {
    // Parse parameters
    const paginationConfig = parsePagination(query, pagination);
    const combinedFilters = parseFilters(query, filters);
    const sort = parseSort(query, defaultSort);

    // Determine populate configuration
    const populate = customPopulate || POPULATE_CONFIGS[populationType] || POPULATE_CONFIGS.list;

    // Check if relevance sorting is requested
    const isRelevanceSort = Array.isArray(sort) && sort.some(s => s.includes('relevance')) ||
                           (typeof sort === 'object' && sort.relevance) ||
                           (typeof sort === 'string' && sort.includes('relevance'));

    let entities;
    let total = null;

    if (isRelevanceSort) {
      // For relevance sorting, we need to fetch all matching entities first
      // then sort them by relevance score, then apply pagination

      // Get search term from filters for relevance calculation
      let searchTerm = '';
      if (combinedFilters.$or && Array.isArray(combinedFilters.$or)) {
        // Extract search term from $or conditions
        const titleCondition = combinedFilters.$or.find((condition: any) => condition.title?.$containsi);
        if (titleCondition) {
          searchTerm = titleCondition.title.$containsi;
        }
      }

      // Fetch all entities without pagination for relevance sorting
      const allEntities = await strapi.entityService.findMany('api::property.property', {
        filters: combinedFilters,
        populate
      });

      // Determine sort order
      let sortOrder: 'asc' | 'desc' = 'desc';
      if (Array.isArray(sort)) {
        const relevanceSort = sort.find(s => s.includes('relevance'));
        if (relevanceSort && relevanceSort.includes(':asc')) {
          sortOrder = 'asc';
        }
      }

      // Sort by relevance
      const sortedEntities = sortByRelevance(allEntities, searchTerm, sortOrder);

      // Apply pagination to sorted results
      const startIndex = (paginationConfig.page - 1) * paginationConfig.pageSize;
      const endIndex = startIndex + paginationConfig.pageSize;
      entities = sortedEntities.slice(startIndex, endIndex);

      // Set total for pagination
      total = sortedEntities.length;
    } else {
      // Standard sorting - use entityService with built-in pagination
      const entityOptions = {
        start: (paginationConfig.page - 1) * paginationConfig.pageSize,
        limit: paginationConfig.pageSize,
        filters: combinedFilters,
        sort,
        populate
      };

      entities = await strapi.entityService.findMany('api::property.property', entityOptions);

      if (includeCount) {
        total = await strapi.entityService.count('api::property.property', {
          filters: combinedFilters
        });
      }
    }

    // Build response
    const response: any = {
      data: entities
    };

    // Add pagination metadata if count was requested or relevance sorting was used
    if ((includeCount || isRelevanceSort) && total !== null) {
      const pageCount = Math.ceil((total as number) / paginationConfig.pageSize);
      response.meta = {
        pagination: {
          page: paginationConfig.page,
          pageSize: paginationConfig.pageSize,
          pageCount,
          total
        }
      };
    }

    return response;
  } catch (error) {
    console.error('Error in fetchProperties helper:', error);
    throw error;
  }
}

/**
 * Fetch a single property by ID with configurable population
 */
export async function fetchPropertyById(id: string, populationType = 'detailed', customPopulate = null) {
  try {
    const populate = customPopulate || POPULATE_CONFIGS[populationType] || POPULATE_CONFIGS.detailed;
    
    // Check if ID looks like a documentId or numeric ID
    const isNumericId = /^\d+$/.test(id);
    
    let property = null;
    
    if (!isNumericId) {
      // Try documentId lookup first
      try {
        property = await strapi.entityService.findOne('api::property.property', id, { populate });
      } catch (error) {
        // If documentId lookup fails, try manual search
        const allProperties = await strapi.entityService.findMany('api::property.property', { populate });
        property = allProperties.find((p: any) => p.documentId === id);
      }
    } else {
      // Try numeric ID lookup
      const properties = await strapi.entityService.findMany('api::property.property', {
        filters: { id: parseInt(id) },
        populate
      });
      property = properties.length > 0 ? properties[0] : null;
    }
    
    return property;
  } catch (error) {
    console.error('Error in fetchPropertyById helper:', error);
    throw error;
  }
}

/**
 * Fetch properties by slug
 */
export async function fetchPropertyBySlug(slug: string, populationType = 'detailed') {
  try {
    const populate = POPULATE_CONFIGS[populationType] || POPULATE_CONFIGS.detailed;
    
    const properties = await strapi.entityService.findMany('api::property.property', {
      filters: { slug: { $eq: slug } },
      populate
    });
    
    return properties.length > 0 ? properties[0] : null;
  } catch (error) {
    console.error('Error in fetchPropertyBySlug helper:', error);
    throw error;
  }
}
