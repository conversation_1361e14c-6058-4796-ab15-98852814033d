'use client';

import React, { useState, useEffect } from 'react';
import { MapPin, Navigation, Clock, Star, Phone, Globe, ChevronDown, ChevronUp } from 'lucide-react';

interface Coordinates {
  lat: number;
  lng: number;
}

interface NearbyPlace {
  place_id: string;
  name: string;
  vicinity: string;
  rating?: number;
  user_ratings_total?: number;
  price_level?: number;
  opening_hours?: {
    open_now: boolean;
  };
  photos?: Array<{
    photo_reference: string;
    height: number;
    width: number;
  }>;
  types: string[];
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
}

interface PlaceCategory {
  name: string;
  displayName: string;
  places: NearbyPlace[];
  icon: string;
  color: string;
}

interface MapPreviewWithNearbyPlacesProps {
  coordinates?: Coordinates;
  address?: string;
  className?: string;
}

export const MapPreviewWithNearbyPlaces: React.FC<MapPreviewWithNearbyPlacesProps> = ({
  coordinates,
  address,
  className = ''
}) => {
  const [nearbyPlaces, setNearbyPlaces] = useState<PlaceCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (coordinates) {
      fetchNearbyPlaces();
    } else {
      setNearbyPlaces([]);
    }
  }, [coordinates]);

  const fetchNearbyPlaces = async () => {
    if (!coordinates) return;

    setLoading(true);
    setError(null);

    try {
      // Try to get enabled categories first, with fallback
      let enabledCategories = [];

      try {
        const categoriesResponse = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/api/nearby-place-categories/enabled`);

        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          enabledCategories = categoriesData.data || [];
        }
      } catch (categoryError) {
        console.warn('Categories API not available, using fallback categories');
      }

      // Fallback categories if API is not available
      if (enabledCategories.length === 0) {
        enabledCategories = [
          {
            name: 'education',
            displayName: 'Education',
            googlePlaceTypes: ['school', 'primary_school', 'secondary_school', 'university', 'library'],
            searchRadius: 1000,
            maxResults: 10,
            icon: '🎓',
            color: '#3B82F6'
          },
          {
            name: 'restaurants',
            displayName: 'Restaurants & Food',
            googlePlaceTypes: ['restaurant', 'cafe', 'bar', 'bakery', 'meal_delivery', 'meal_takeaway'],
            searchRadius: 1000,
            maxResults: 10,
            icon: '🍽️',
            color: '#EF4444'
          },
          {
            name: 'shopping',
            displayName: 'Shopping',
            googlePlaceTypes: ['shopping_mall', 'supermarket', 'convenience_store', 'department_store', 'clothing_store', 'electronics_store', 'store'],
            searchRadius: 1000,
            maxResults: 10,
            icon: '🛍️',
            color: '#10B981'
          },
          {
            name: 'healthcare',
            displayName: 'Healthcare',
            googlePlaceTypes: ['hospital', 'doctor', 'dentist', 'pharmacy', 'physiotherapist', 'veterinary_care'],
            searchRadius: 1000,
            maxResults: 10,
            icon: '🏥',
            color: '#F59E0B'
          },
          {
            name: 'transportation',
            displayName: 'Transportation',
            googlePlaceTypes: ['bus_station', 'train_station', 'subway_station', 'light_rail_station', 'transit_station', 'taxi_stand', 'airport', 'gas_station'],
            searchRadius: 1000,
            maxResults: 10,
            icon: '🚌',
            color: '#8B5CF6'
          }
        ];
      }

      // Fetch nearby places for each category
      console.log('Fetching places for', enabledCategories.length, 'categories');
      const placePromises = enabledCategories.map(async (category: any) => {
        try {
          console.log(`Fetching places for category: ${category.name}`, {
            types: category.googlePlaceTypes,
            radius: category.searchRadius,
            maxResults: category.maxResults
          });
          const places = await searchNearbyPlaces(coordinates, category.googlePlaceTypes, category.searchRadius, category.maxResults);
          console.log(`Found ${places.length} places for category ${category.name}`);
          return {
            name: category.name,
            displayName: category.displayName,
            places: places,
            icon: category.icon,
            color: category.color
          };
        } catch (err) {
          console.error(`Failed to fetch places for category ${category.name}:`, err);
          return {
            name: category.name,
            displayName: category.displayName,
            places: [],
            icon: category.icon,
            color: category.color
          };
        }
      });

      const results = await Promise.all(placePromises);
      setNearbyPlaces(results.filter(category => category.places.length > 0));

    } catch (err: any) {
      setError(err.message || 'Failed to fetch nearby places');
    } finally {
      setLoading(false);
    }
  };

  const searchNearbyPlaces = async (
    coords: Coordinates,
    placeTypes: string[],
    radius: number = 1500,
    maxResults: number = 5
  ): Promise<NearbyPlace[]> => {
    try {
      // Use backend API to search for nearby places
      const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/api/properties/nearby-places`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          lat: coords.lat,
          lng: coords.lng,
          types: placeTypes,
          radius: radius,
          maxResults: maxResults
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API request failed:', response.status, errorText);
        throw new Error(`API request failed: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Raw API response:', data);

      // Handle both response formats: { data: [...] } and { success: true, places: [...] }
      const places = data.data || data.places || [];
      console.log('Extracted places:', places);

      if (places && places.length > 0) {
        console.log('Successfully fetched real places data:', places.length, 'places');
        return places.map((place: any) => ({
          place_id: place.place_id || '',
          name: place.name || '',
          vicinity: place.vicinity || place.formatted_address || '',
          rating: place.rating,
          user_ratings_total: place.user_ratings_total,
          price_level: place.price_level,
          opening_hours: place.opening_hours ? { open_now: place.opening_hours.open_now } : undefined,
          photos: place.photos?.map((photo: any) => ({
            photo_reference: photo.photo_reference,
            height: photo.height || 300,
            width: photo.width || 300
          })),
          types: place.types || [],
          geometry: {
            location: {
              lat: place.geometry?.location?.lat || coords.lat,
              lng: place.geometry?.location?.lng || coords.lng
            }
          }
        }));
      } else {
        console.warn('No places found from API');
        return [];
      }
    } catch (error) {
      console.error('Error fetching nearby places from API:', error);
      return [];
    }
  };



  const toggleCategory = (categoryName: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryName)) {
      newExpanded.delete(categoryName);
    } else {
      newExpanded.add(categoryName);
    }
    setExpandedCategories(newExpanded);
  };

  const calculateDistance = (place: NearbyPlace): string => {
    if (!coordinates) return '';
    
    const R = 6371; // Earth's radius in km
    const dLat = (place.geometry.location.lat - coordinates.lat) * Math.PI / 180;
    const dLng = (place.geometry.location.lng - coordinates.lng) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(coordinates.lat * Math.PI / 180) * Math.cos(place.geometry.location.lat * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    
    return distance < 1 ? `${Math.round(distance * 1000)}m` : `${distance.toFixed(1)}km`;
  };

  const renderPriceLevel = (level?: number) => {
    if (!level) return null;
    return (
      <span className="text-green-600 font-medium">
        {'$'.repeat(level)}
      </span>
    );
  };

  if (!coordinates) {
    return (
      <div className={`bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center ${className}`}>
        <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-3" />
        <p className="text-gray-600 font-medium">Select coordinates to preview nearby places</p>
        <p className="text-sm text-gray-500 mt-1">
          Use the coordinate selector above to see what's around your property
        </p>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4">
        <div className="flex items-center space-x-2">
          <Navigation className="h-5 w-5" />
          <h3 className="font-semibold">Nearby Places Preview</h3>
        </div>
        <p className="text-blue-100 text-sm mt-1">
          {address || `${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`}
        </p>
      </div>

      {/* Content */}
      <div className="p-4">
        {loading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"></div>
            <p className="text-gray-600">Finding nearby places...</p>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {!loading && !error && nearbyPlaces.length === 0 && (
          <div className="text-center py-8">
            <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-600">No nearby places found</p>
            <p className="text-sm text-gray-500 mt-1">
              Try enabling more place categories in admin settings
            </p>
          </div>
        )}

        {!loading && nearbyPlaces.length > 0 && (
          <div className="space-y-3">
            {nearbyPlaces.map((category) => (
              <div key={category.name} className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                <button
                  onClick={() => toggleCategory(category.name)}
                  className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-200 transition-all duration-200"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 w-10 h-10 bg-white rounded-lg flex items-center justify-center shadow-sm">
                      <span className="text-xl">{category.icon}</span>
                    </div>
                    <div className="text-left">
                      <h4 className="font-semibold text-gray-900">{category.displayName}</h4>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-sm text-gray-600">{category.places.length} places found</span>
                        {category.places.length > 0 && (
                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                            Within {Math.round(1500/1000)}km
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {category.places.length > 0 && (
                      <span className="text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">
                        Available
                      </span>
                    )}
                    {expandedCategories.has(category.name) ? (
                      <ChevronUp className="h-5 w-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-500" />
                    )}
                  </div>
                </button>

                {expandedCategories.has(category.name) && (
                  <div className="divide-y divide-gray-100">
                    {category.places.map((place) => (
                      <div key={place.place_id} className="p-4 hover:bg-gray-50 transition-colors">
                        <div className="flex items-start space-x-3">
                          {/* Place Info */}
                          <div className="flex-1 min-w-0">
                            {/* Name and Distance */}
                            <div className="flex items-start justify-between mb-2">
                              <h5 className="font-semibold text-gray-900 truncate pr-2">{place.name}</h5>
                              <span className="text-sm font-medium text-blue-600 whitespace-nowrap">
                                {calculateDistance(place)}
                              </span>
                            </div>

                            {/* Address */}
                            <p className="text-sm text-gray-600 mb-3 line-clamp-2">{place.vicinity}</p>

                            {/* Metadata Row */}
                            <div className="flex items-center flex-wrap gap-x-4 gap-y-2">
                              {/* Rating */}
                              {place.rating && (
                                <div className="flex items-center space-x-1">
                                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                                  <span className="text-sm font-medium text-gray-900">{place.rating}</span>
                                  {place.user_ratings_total && (
                                    <span className="text-xs text-gray-500">({place.user_ratings_total})</span>
                                  )}
                                </div>
                              )}

                              {/* Opening Hours */}
                              {place.opening_hours && (
                                <div className="flex items-center space-x-1">
                                  <Clock className="h-4 w-4 text-gray-400" />
                                  <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                                    place.opening_hours.open_now
                                      ? 'bg-green-100 text-green-700'
                                      : 'bg-red-100 text-red-700'
                                  }`}>
                                    {place.opening_hours.open_now ? 'Open Now' : 'Closed'}
                                  </span>
                                </div>
                              )}

                              {/* Price Level */}
                              {renderPriceLevel(place.price_level)}
                            </div>

                            {/* Place Types */}
                            {place.types && place.types.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-2">
                                {place.types.slice(0, 3).map((type) => (
                                  <span
                                    key={type}
                                    className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md"
                                  >
                                    {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                  </span>
                                ))}
                                {place.types.length > 3 && (
                                  <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md">
                                    +{place.types.length - 3} more
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
