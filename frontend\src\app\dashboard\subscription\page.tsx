'use client';

import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/Dashboard/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import { membershipAPI } from '@/lib/api';
import {
  Crown,
  Check,
  Star,
  Building2,
  Image,
  BarChart3,
  Zap,
  Shield,
  Calendar,
  CreditCard
} from 'lucide-react';

interface Membership {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  duration: string;
  features: string[];
  maxProperties: number;
  maxImages: number;
  canCreateProjects: boolean;
  canAccessAnalytics: boolean;
  canUsePremiumFilters: boolean;
  priority: number;
  isActive: boolean;
}

interface UserMembership {
  membership: Membership | null;
  membershipStartDate: string | null;
  membershipEndDate: string | null;
}

const SubscriptionPage: React.FC = () => {
  const { user } = useAuth();
  const [memberships, setMemberships] = useState<Membership[]>([]);
  const [userMembership, setUserMembership] = useState<UserMembership | null>(null);
  const [loading, setLoading] = useState(true);
  const [subscribing, setSubscribing] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [membershipsResponse, userMembershipResponse] = await Promise.all([
          membershipAPI.getAll(),
          membershipAPI.getMyMembership()
        ]);

        setMemberships(membershipsResponse.data || []);
        setUserMembership(userMembershipResponse.data || null);
      } catch (error) {
        console.error('Failed to fetch subscription data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchData();
    } else {
      setLoading(false);
    }
  }, [user]);

  const handleSubscribe = async (membershipId: string) => {
    setSubscribing(membershipId);
    try {
      await membershipAPI.subscribe(membershipId);
      // Refresh user membership data
      const userMembershipResponse = await membershipAPI.getMyMembership();
      setUserMembership(userMembershipResponse.data);
      alert('Successfully subscribed to membership plan!');
    } catch (error) {
      console.error('Failed to subscribe:', error);
      alert('Failed to subscribe. Please try again.');
    } finally {
      setSubscribing(null);
    }
  };

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'basic':
        return Shield;
      case 'premium':
        return Star;
      case 'professional':
        return Crown;
      default:
        return Building2;
    }
  };

  const getPlanColor = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'basic':
        return 'blue';
      case 'premium':
        return 'purple';
      case 'professional':
        return 'gold';
      default:
        return 'gray';
    }
  };

  const isCurrentPlan = (membershipId: string) => {
    return userMembership?.membership?.id === membershipId;
  };

  const formatDuration = (duration: string) => {
    switch (duration) {
      case 'monthly':
        return 'per month';
      case 'quarterly':
        return 'per quarter';
      case 'yearly':
        return 'per year';
      case 'lifetime':
        return 'lifetime';
      default:
        return duration;
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Choose Your Membership Plan
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Unlock powerful features and grow your real estate business with our flexible membership plans.
          </p>
        </div>

        {/* Current Membership Status */}
        {userMembership?.membership && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Current Plan: {userMembership.membership.name}
                </h3>
                <p className="text-gray-600">
                  {userMembership.membershipEndDate && (
                    <>
                      Valid until: {new Date(userMembership.membershipEndDate).toLocaleDateString()}
                    </>
                  )}
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-600">
                  ${userMembership.membership.price}
                </div>
                <div className="text-sm text-gray-500">
                  {formatDuration(userMembership.membership.duration)}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Membership Plans */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {memberships.map((membership) => {
            const Icon = getPlanIcon(membership.name);
            const color = getPlanColor(membership.name);
            const isCurrent = isCurrentPlan(membership.id);
            const isPopular = membership.name.toLowerCase() === 'premium';

            return (
              <div
                key={membership.id}
                className={`
                  relative bg-white rounded-lg shadow-lg border-2 transition-all duration-200 hover:shadow-xl
                  ${isCurrent ? 'border-green-500 bg-green-50' : 'border-gray-200 hover:border-blue-300'}
                  ${isPopular ? 'ring-2 ring-purple-500 ring-opacity-50' : ''}
                `}
              >
                {isPopular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-purple-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}

                {isCurrent && (
                  <div className="absolute -top-3 right-4">
                    <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      Current Plan
                    </span>
                  </div>
                )}

                <div className="p-6">
                  {/* Plan Header */}
                  <div className="text-center mb-6">
                    <div className={`inline-flex p-3 rounded-full bg-${color}-100 mb-4`}>
                      <Icon className={`h-8 w-8 text-${color}-600`} />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {membership.name}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {membership.description}
                    </p>
                    <div className="text-center">
                      <span className="text-4xl font-bold text-gray-900">
                        ${membership.price}
                      </span>
                      <span className="text-gray-500 ml-2">
                        {formatDuration(membership.duration)}
                      </span>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center text-sm text-gray-600">
                      <Building2 className="h-4 w-4 mr-2 text-blue-500" />
                      {membership.maxProperties === 999999 ? 'Unlimited' : membership.maxProperties} properties
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Image className="h-4 w-4 mr-2 text-green-500" />
                      Up to {membership.maxImages} images per property
                    </div>
                    {membership.canAccessAnalytics && (
                      <div className="flex items-center text-sm text-gray-600">
                        <BarChart3 className="h-4 w-4 mr-2 text-purple-500" />
                        Analytics dashboard
                      </div>
                    )}
                    {membership.canUsePremiumFilters && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Zap className="h-4 w-4 mr-2 text-yellow-500" />
                        Premium search filters
                      </div>
                    )}
                    {membership.canCreateProjects && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Crown className="h-4 w-4 mr-2 text-orange-500" />
                        Project management
                      </div>
                    )}
                    
                    {membership.features && membership.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-sm text-gray-600">
                        <Check className="h-4 w-4 mr-2 text-green-500" />
                        {feature}
                      </div>
                    ))}
                  </div>

                  {/* Action Button */}
                  <button
                    onClick={() => handleSubscribe(membership.id)}
                    disabled={isCurrent || subscribing === membership.id}
                    className={`
                      w-full py-3 px-4 rounded-lg font-medium transition-all duration-200
                      ${isCurrent
                        ? 'bg-green-100 text-green-800 cursor-not-allowed'
                        : subscribing === membership.id
                        ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                        : isPopular
                        ? 'bg-purple-600 text-white hover:bg-purple-700'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                      }
                    `}
                  >
                    {isCurrent ? (
                      'Current Plan'
                    ) : subscribing === membership.id ? (
                      'Subscribing...'
                    ) : membership.price === 0 ? (
                      'Get Started Free'
                    ) : (
                      `Subscribe for $${membership.price}`
                    )}
                  </button>
                </div>
              </div>
            );
          })}
        </div>

        {/* FAQ or Additional Info */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Need Help Choosing?
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Basic Plan</h4>
              <p>Perfect for individuals just getting started with real estate listings.</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Premium Plan</h4>
              <p>Great for real estate agents and small agencies looking to grow their business.</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default SubscriptionPage;
