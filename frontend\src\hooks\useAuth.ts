'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authAPI } from '@/lib/api';

// Query Keys
export const AUTH_KEYS = {
  user: ['auth', 'user'] as const,
};

// Hook for current user
export const useCurrentUser = () => {
  return useQuery({
    queryKey: AUTH_KEYS.user,
    queryFn: authAPI.me,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: false, // Don't retry on auth failures
  });
};

// Login mutation
export const useLogin = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ identifier, password }: { identifier: string; password: string }) =>
      authAPI.login(identifier, password),
    onSuccess: (data) => {
      // Store token and user data
      localStorage.setItem('jwt', data.jwt);
      localStorage.setItem('user', JSON.stringify(data.user));
      
      // Set user data in cache
      queryClient.setQueryData(AUTH_KEYS.user, data.user);
    },
  });
};

// Register mutation
export const useRegister = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ username, email, password }: { username: string; email: string; password: string }) =>
      authAPI.register(username, email, password),
    onSuccess: (data) => {
      localStorage.setItem('jwt', data.jwt);
      localStorage.setItem('user', JSON.stringify(data.user));
      queryClient.setQueryData(AUTH_KEYS.user, data.user);
    },
  });
};

// Logout mutation
export const useLogout = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async () => {
      // Clear local storage
      localStorage.removeItem('jwt');
      localStorage.removeItem('user');
    },
    onSuccess: () => {
      // Clear all cached data
      queryClient.clear();
    },
  });
};

// Forgot password mutation
export const useForgotPassword = () => {
  return useMutation({
    mutationFn: ({ email }: { email: string }) => authAPI.forgotPassword(email),
  });
};

// Reset password mutation
export const useResetPassword = () => {
  return useMutation({
    mutationFn: ({ code, password, passwordConfirmation }: { 
      code: string; 
      password: string; 
      passwordConfirmation: string; 
    }) => authAPI.resetPassword(code, password, passwordConfirmation),
  });
};
