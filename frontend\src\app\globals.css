@import "tailwindcss";
@import "../styles/tiptap.css";

/* Custom animations for nearby places */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
  opacity: 0;
}

/* Custom scrollbar styles for filter sidebar */
.filter-sidebar-scroll {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.filter-sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.filter-sidebar-scroll::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.filter-sidebar-scroll::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.filter-sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
