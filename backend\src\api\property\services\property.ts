/**
 * property service
 */

import { factories } from '@strapi/strapi';

const axios = require('axios');

export default factories.createCoreService('api::property.property', ({ strapi }) => ({
  /**
   * Find nearby places using Google Places API
   * @param {Object} params - Search parameters
   * @param {number} params.lat - Latitude
   * @param {number} params.lng - Longitude
   * @param {Array} params.types - Google place types to search for
   * @param {number} params.radius - Search radius in meters (default: 1000)
   * @param {number} params.maxResults - Maximum results to return (default: 10)
   * @returns {Promise<Array>} Array of nearby places
   */
  async findNearbyPlaces({ lat, lng, types, radius = 1000, maxResults = 10 }) {
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
      throw new Error('Google Maps API key not configured');
    }

    if (!lat || !lng) {
      throw new Error('Latitude and longitude are required');
    }

    if (!types || !Array.isArray(types) || types.length === 0) {
      throw new Error('Place types array is required');
    }



    try {
      const allPlaces = [];

      // Search for each place type
      for (const type of types) {
        const url = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
        const params = {
          location: `${lat},${lng}`,
          radius: radius,
          type: type,
          key: apiKey
        };

        const response = await axios.get(url, { params });

        if (response.data.status === 'OK') {
          const places = response.data.results.map(place => ({
            place_id: place.place_id,
            name: place.name,
            vicinity: place.vicinity || place.formatted_address || '',
            rating: place.rating,
            user_ratings_total: place.user_ratings_total,
            price_level: place.price_level,
            opening_hours: place.opening_hours ? { open_now: place.opening_hours.open_now } : undefined,
            photos: place.photos?.map(photo => ({
              photo_reference: photo.photo_reference,
              height: photo.height || 300,
              width: photo.width || 300
            })),
            types: place.types || [],
            geometry: {
              location: {
                lat: place.geometry?.location?.lat || lat,
                lng: place.geometry?.location?.lng || lng
              }
            }
          }));

          allPlaces.push(...places);
        }
      }

      // Remove duplicates based on place_id and limit results
      const uniquePlaces = allPlaces.filter((place, index, self) =>
        index === self.findIndex(p => p.place_id === place.place_id)
      );

      const limitedPlaces = uniquePlaces.slice(0, maxResults);

      return limitedPlaces;

    } catch (error) {
      throw new Error(`Google Places API error: ${error.message}`);
    }
  }
}));
