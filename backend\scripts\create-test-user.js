const strapi = require('@strapi/strapi');

async function createTestUser() {
  console.log('🔧 Creating test user directly in database...');
  
  try {
    // Initialize Strapi
    const app = await strapi().load();
    
    // Check if user already exists
    const existingUser = await strapi.entityService.findMany('plugin::users-permissions.user', {
      filters: { email: '<EMAIL>' }
    });
    
    if (existingUser.length > 0) {
      console.log('✅ Test user already exists!');
      console.log(`   User ID: ${existingUser[0].id}`);
      console.log(`   Email: ${existingUser[0].email}`);
      console.log(`   Username: ${existingUser[0].username}`);
      console.log('\n📝 Use these credentials to test the frontend dashboard:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: Dashboard123!');
      return;
    }
    
    // Get the authenticated role
    const authenticatedRole = await strapi.entityService.findMany('plugin::users-permissions.role', {
      filters: { type: 'authenticated' }
    });
    
    if (authenticatedRole.length === 0) {
      throw new Error('Authenticated role not found');
    }
    
    // Hash the password
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('Dashboard123!', 10);
    
    // Create the user
    const user = await strapi.entityService.create('plugin::users-permissions.user', {
      data: {
        username: 'dashboardtest',
        email: '<EMAIL>',
        password: hashedPassword,
        confirmed: true,
        blocked: false,
        role: authenticatedRole[0].id,
        firstName: 'Dashboard',
        lastName: 'Test',
        phone: '+1234567890',
        company: 'Test Company'
      }
    });
    
    console.log('✅ Test user created successfully!');
    console.log(`   User ID: ${user.id}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Username: ${user.username}`);
    console.log('\n📝 Use these credentials to test the frontend dashboard:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: Dashboard123!');
    
    // Test login to get JWT token
    const jwt = require('jsonwebtoken');
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    
    const token = jwt.sign(
      { id: user.id },
      jwtSecret,
      { expiresIn: '30d' }
    );
    
    console.log(`\n🔑 JWT Token for testing: ${token.substring(0, 30)}...`);
    
  } catch (error) {
    console.error('❌ Error creating test user:', error.message);
  } finally {
    process.exit(0);
  }
}

createTestUser();
