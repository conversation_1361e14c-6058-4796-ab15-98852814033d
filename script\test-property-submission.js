// Test script to submit a property via API
const axios = require('axios');

const API_BASE_URL = 'http://localhost:1337/api';

// Test user credentials (you'll need to create a user first)
const testUser = {
  identifier: '<EMAIL>',
  password: 'testpassword123'
};

// Test property data
const testProperty = {
  title: 'Beautiful Modern Apartment',
  description: 'A stunning 2-bedroom apartment in the heart of the city with modern amenities and great views.',
  price: 250000,
  currency: 'USD',
  propertyType: 'apartment',
  status: 'for-sale',
  bedrooms: 2,
  bathrooms: 2,
  area: 85,
  areaUnit: 'sqm',
  address: '123 Main Street',
  city: 'New York',
  country: 'USA',
  neighborhood: 'Downtown',
  propertyCode: 'NYC001',
  isLuxury: false,
  features: ['balcony', 'parking', 'elevator'],
  yearBuilt: 2020,
  parking: 1,
  furnished: false,
  petFriendly: true
};

async function testPropertySubmission() {
  try {
    console.log('🔐 Logging in...');
    
    // First, login to get JWT token
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/local`, testUser);
    const token = loginResponse.data.jwt;
    
    console.log('✅ Login successful');
    console.log('🏠 Submitting property...');
    
    // Submit property with authentication
    const propertyResponse = await axios.post(
      `${API_BASE_URL}/properties`,
      { data: testProperty },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Property submitted successfully!');
    console.log('Property ID:', propertyResponse.data.data.id);
    console.log('Property Title:', propertyResponse.data.data.title);
    
    // Test fetching the property
    console.log('📋 Fetching submitted property...');
    const fetchResponse = await axios.get(
      `${API_BASE_URL}/properties/${propertyResponse.data.data.id}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    console.log('✅ Property fetched successfully!');
    console.log('Fetched property:', fetchResponse.data.data.title);
    
    // Test fetching user's properties
    console.log('📋 Fetching user properties...');
    const myPropertiesResponse = await axios.get(
      `${API_BASE_URL}/properties/my-properties`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    console.log('✅ User properties fetched successfully!');
    console.log('Total properties:', myPropertiesResponse.data.data.length);
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    
    if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('identifier')) {
      console.log('\n💡 Tip: You need to create a user account first. You can:');
      console.log('1. Go to http://localhost:3000/auth/register');
      console.log('2. Create an account with email: <EMAIL>');
      console.log('3. Run this script again');
    }
  }
}

// Run the test
testPropertySubmission();
