# 🔌 API Documentation

Complete API documentation for the Real Estate Platform backend built with Strapi CMS.

## 📋 Table of Contents

- [Base URL & Authentication](#-base-url--authentication)
- [Authentication Endpoints](#-authentication-endpoints)
- [Property Endpoints](#-property-endpoints)
- [User Management](#-user-management)
- [Location Services](#-location-services)
- [File Upload](#-file-upload)
- [Erro<PERSON>ling](#-error-handling)
- [Rate Limiting](#-rate-limiting)

## 🌐 Base URL & Authentication

### Base URL
```
Development: http://localhost:1337/api
Production: https://your-domain.com/api
```

### Authentication
All authenticated endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Content Type
All POST/PUT requests should include:
```
Content-Type: application/json
```

## 🔐 Authentication Endpoints

### Login
```http
POST /auth/local
```

**Request Body:**
```json
{
  "identifier": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "john_doe",
    "email": "<EMAIL>",
    "confirmed": true,
    "blocked": false,
    "createdAt": "2025-06-30T10:00:00.000Z",
    "updatedAt": "2025-06-30T10:00:00.000Z"
  }
}
```

### Register
```http
POST /auth/local/register
```

**Request Body:**
```json
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:** Same as login response

### Get Current User
```http
GET /users/me
```

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": 1,
  "username": "john_doe",
  "email": "<EMAIL>",
  "confirmed": true,
  "blocked": false,
  "membership": {
    "id": 1,
    "tier": "basic",
    "propertyLimit": 5,
    "propertiesUsed": 2
  }
}
```

## 🏠 Property Endpoints

### List Properties
```http
GET /properties
```

**Query Parameters:**
- `pagination[page]` - Page number (default: 1)
- `pagination[pageSize]` - Items per page (default: 25)
- `filters[title][$containsi]` - Search in title
- `filters[city][$eq]` - Filter by city
- `filters[propertyType][$eq]` - Filter by type
- `filters[price][$gte]` - Minimum price
- `filters[price][$lte]` - Maximum price
- `filters[bedrooms][$eq]` - Number of bedrooms
- `filters[bathrooms][$eq]` - Number of bathrooms
- `populate` - Relations to populate (e.g., "images,owner")

**Example:**
```http
GET /properties?filters[city][$eq]=New York&filters[price][$gte]=100000&populate=images
```

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "title": "Modern Apartment in Downtown",
      "description": "Beautiful 2-bedroom apartment...",
      "price": 250000,
      "currency": "USD",
      "propertyType": "apartment",
      "status": "for-sale",
      "bedrooms": 2,
      "bathrooms": 1,
      "area": 85,
      "areaUnit": "sqm",
      "address": "123 Main St",
      "city": "New York",
      "country": "USA",
      "neighborhood": ["Downtown", "Financial District"],
      "coordinates": {
        "lat": 40.7128,
        "lng": -74.0060
      },
      "features": ["parking", "balcony", "elevator"],
      "furnished": true,
      "petFriendly": false,
      "isLuxury": false,
      "propertyCode": "NYC-001",
      "publishedAt": "2025-06-30T10:00:00.000Z",
      "createdAt": "2025-06-30T09:00:00.000Z",
      "updatedAt": "2025-06-30T10:00:00.000Z",
      "images": [
        {
          "id": 1,
          "url": "/uploads/property1_image1.jpg",
          "alternativeText": "Living room"
        }
      ]
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 25,
      "pageCount": 1,
      "total": 1
    }
  }
}
```

### Get Property Details
```http
GET /properties/:id
```

**Parameters:**
- `id` - Property ID

**Query Parameters:**
- `populate` - Relations to populate

**Response:** Single property object (same structure as list item)

### Create Property
```http
POST /properties
```

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "data": {
    "title": "New Property",
    "description": "Property description",
    "price": 300000,
    "currency": "USD",
    "propertyType": "house",
    "status": "for-sale",
    "bedrooms": 3,
    "bathrooms": 2,
    "area": 120,
    "areaUnit": "sqm",
    "address": "456 Oak Street",
    "city": "Los Angeles",
    "country": "USA",
    "neighborhood": ["Beverly Hills"],
    "coordinates": {
      "lat": 34.0522,
      "lng": -118.2437
    },
    "features": ["garden", "garage"],
    "furnished": false,
    "petFriendly": true,
    "isLuxury": true
  }
}
```

**Response:** Created property object

### Update Property
```http
PUT /properties/:id
```

**Headers:** `Authorization: Bearer <token>`

**Request Body:** Same as create (partial updates allowed)

**Response:** Updated property object

### Delete Property
```http
DELETE /properties/:id
```

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "data": {
    "id": 1,
    "title": "Deleted Property"
  }
}
```

### Generate Nearby Places
```http
POST /properties/:id/generate-nearby-places
```

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "data": {
    "nearbyPlaces": {
      "education": {
        "category": {
          "name": "education",
          "displayName": "Education",
          "icon": "🎓"
        },
        "places": [
          {
            "place_id": "ChIJ...",
            "name": "Central High School",
            "vicinity": "123 School St",
            "rating": 4.2,
            "types": ["school", "point_of_interest"],
            "geometry": {
              "location": {
                "lat": 40.7130,
                "lng": -74.0058
              }
            }
          }
        ]
      }
    }
  }
}
```

## 👤 User Management

### Get User Profile
```http
GET /users/me
```

**Headers:** `Authorization: Bearer <token>`

### Update User Profile
```http
PUT /users/:id
```

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "username": "new_username",
  "email": "<EMAIL>"
}
```

### Get User Membership
```http
GET /memberships
```

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "tier": "premium",
      "propertyLimit": 50,
      "propertiesUsed": 12,
      "features": ["unlimited_photos", "priority_support"],
      "expiresAt": "2025-12-31T23:59:59.000Z"
    }
  ]
}
```

## 🗺️ Location Services

### Get Enabled Place Categories
```http
GET /nearby-place-categories/enabled
```

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "education",
      "displayName": "Education",
      "googlePlaceTypes": ["school", "university", "library"],
      "searchRadius": 1000,
      "maxResults": 10,
      "icon": "🎓",
      "color": "#3B82F6",
      "enabled": true,
      "priority": 1
    }
  ]
}
```

### Get Google Place Types
```http
GET /nearby-place-categories/google-place-types
```

**Response:**
```json
{
  "data": {
    "education": ["school", "primary_school", "secondary_school", "university"],
    "food": ["restaurant", "meal_takeaway", "cafe", "bakery"],
    "shopping": ["shopping_mall", "store", "supermarket", "clothing_store"],
    "healthcare": ["hospital", "pharmacy", "doctor", "dentist"],
    "transportation": ["subway_station", "bus_station", "train_station"]
  }
}
```

### Get Cities
```http
GET /properties/cities
```

**Response:**
```json
{
  "data": [
    "New York",
    "Los Angeles",
    "Chicago",
    "Houston"
  ]
}
```

### Get Neighborhoods by City
```http
GET /properties/neighborhoods?city=New York
```

**Response:**
```json
{
  "data": [
    "Manhattan",
    "Brooklyn",
    "Queens",
    "Bronx"
  ]
}
```

## 📁 File Upload

### Upload Files
```http
POST /upload
```

**Headers:** 
- `Authorization: Bearer <token>`
- `Content-Type: multipart/form-data`

**Request Body (Form Data):**
- `files` - File(s) to upload
- `ref` - Content type (e.g., "api::property.property")
- `refId` - Entity ID
- `field` - Field name (e.g., "images")

**Response:**
```json
[
  {
    "id": 1,
    "name": "property_image.jpg",
    "alternativeText": null,
    "caption": null,
    "width": 1920,
    "height": 1080,
    "formats": {
      "thumbnail": {
        "url": "/uploads/thumbnail_property_image.jpg",
        "width": 245,
        "height": 138
      },
      "medium": {
        "url": "/uploads/medium_property_image.jpg",
        "width": 750,
        "height": 422
      }
    },
    "hash": "property_image_abc123",
    "ext": ".jpg",
    "mime": "image/jpeg",
    "size": 245.67,
    "url": "/uploads/property_image.jpg",
    "previewUrl": null,
    "provider": "local",
    "createdAt": "2025-06-30T10:00:00.000Z",
    "updatedAt": "2025-06-30T10:00:00.000Z"
  }
]
```

## ❌ Error Handling

### Error Response Format
```json
{
  "error": {
    "status": 400,
    "name": "ValidationError",
    "message": "Validation failed",
    "details": {
      "errors": [
        {
          "path": ["title"],
          "message": "Title is required",
          "name": "ValidationError"
        }
      ]
    }
  }
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

### Error Types
- **ValidationError** - Invalid input data
- **AuthenticationError** - Invalid credentials
- **ForbiddenError** - Insufficient permissions
- **NotFoundError** - Resource not found
- **RateLimitError** - Too many requests

## ⚡ Rate Limiting

### Current Limits
- **Authentication**: 5 requests per minute per IP
- **Property Creation**: 10 requests per hour per user
- **File Upload**: 20 requests per hour per user
- **General API**: 100 requests per minute per user

### Rate Limit Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

### Rate Limit Exceeded Response
```json
{
  "error": {
    "status": 429,
    "name": "RateLimitError",
    "message": "Too many requests, please try again later",
    "details": {
      "resetTime": "2025-06-30T11:00:00.000Z"
    }
  }
}
```

---

## 📝 Additional Notes

### Pagination
All list endpoints support pagination with the following parameters:
- `pagination[page]` - Page number (starts from 1)
- `pagination[pageSize]` - Items per page (max 100)

### Filtering
Use Strapi's filtering syntax:
- `$eq` - Equal
- `$ne` - Not equal
- `$lt` - Less than
- `$lte` - Less than or equal
- `$gt` - Greater than
- `$gte` - Greater than or equal
- `$in` - In array
- `$notIn` - Not in array
- `$contains` - Contains (case sensitive)
- `$containsi` - Contains (case insensitive)
- `$startsWith` - Starts with
- `$endsWith` - Ends with

### Sorting
Use the `sort` parameter:
- `sort=title:asc` - Sort by title ascending
- `sort=price:desc` - Sort by price descending
- `sort=createdAt:desc,title:asc` - Multiple sort fields

### Population
Use the `populate` parameter to include related data:
- `populate=*` - Populate all relations
- `populate=images` - Populate specific relation
- `populate=images,owner` - Populate multiple relations

---

**For more detailed information, refer to the [Strapi Documentation](https://docs.strapi.io/)**
