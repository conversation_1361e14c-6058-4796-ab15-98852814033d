'use client';

import React, { useState, useEffect } from 'react';
import { googleMapsService, NeighborhoodInfo } from '@/lib/googleMaps';
import { MapPin, Search, X, Plus, Settings } from 'lucide-react';

interface NeighborhoodSelectorProps {
  value: NeighborhoodInfo[];
  onChange: (neighborhoods: NeighborhoodInfo[]) => void;
  coordinates?: { lat: number; lng: number };
  address?: string;
  maxSelections?: number;
  placeholder?: string;
}

const NeighborhoodSelector: React.FC<NeighborhoodSelectorProps> = ({
  value = [],
  onChange,
  coordinates,
  address,
  maxSelections = 5,
  placeholder = "Search or detect neighborhoods..."
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<NeighborhoodInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [detectedNeighborhoods, setDetectedNeighborhoods] = useState<NeighborhoodInfo[]>([]);
  const [searchRadius, setSearchRadius] = useState(2000); // 2km default
  const [showSettings, setShowSettings] = useState(false);

  // Auto-detect neighborhoods when coordinates change
  useEffect(() => {
    if (coordinates?.lat && coordinates?.lng) {
      detectNeighborhoods();
    }
  }, [coordinates]);

  // Auto-detect neighborhoods when address changes
  useEffect(() => {
    if (address && !coordinates) {
      detectFromAddress();
    }
  }, [address]);

  const detectNeighborhoods = async () => {
    if (!coordinates?.lat || !coordinates?.lng) return;

    setLoading(true);
    try {
      const [nearby, reverse] = await Promise.all([
        googleMapsService.getNearbyNeighborhoods(coordinates.lat, coordinates.lng, searchRadius),
        googleMapsService.getNeighborhoodsFromCoordinates(coordinates.lat, coordinates.lng)
      ]);

      // Combine and deduplicate results
      const combined = [...reverse, ...nearby];
      const unique = combined.filter((item, index, self) => 
        index === self.findIndex(t => t.name === item.name)
      );

      setDetectedNeighborhoods(unique);
    } catch (error) {
      console.error('Error detecting neighborhoods:', error);
    } finally {
      setLoading(false);
    }
  };

  const detectFromAddress = async () => {
    if (!address) return;

    setLoading(true);
    try {
      const locationInfo = await googleMapsService.getLocationFromAddress(address);
      if (locationInfo?.neighborhoods) {
        setDetectedNeighborhoods(locationInfo.neighborhoods);
      }
    } catch (error) {
      console.error('Error detecting neighborhoods from address:', error);
    } finally {
      setLoading(false);
    }
  };

  const searchNeighborhoods = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setLoading(true);
    try {
      // Use Places API to search for neighborhoods
      const places = await googleMapsService.searchPlaces(query + ' neighborhood');
      
      // Convert places to neighborhood format
      const neighborhoods: NeighborhoodInfo[] = places.map(place => ({
        name: place.structured_formatting?.main_text || place.description,
        type: 'search_result',
        formatted_address: place.description,
        place_id: place.place_id
      }));

      setSearchResults(neighborhoods);
    } catch (error) {
      console.error('Error searching neighborhoods:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    setShowDropdown(true);
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      searchNeighborhoods(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  const addNeighborhood = (neighborhood: NeighborhoodInfo) => {
    if (value.length >= maxSelections) return;
    if (value.some(n => n.name === neighborhood.name)) return;

    onChange([...value, neighborhood]);
    setSearchQuery('');
    setShowDropdown(false);
  };

  const removeNeighborhood = (index: number) => {
    const newValue = value.filter((_, i) => i !== index);
    onChange(newValue);
  };

  const addDetectedNeighborhood = (neighborhood: NeighborhoodInfo) => {
    addNeighborhood(neighborhood);
  };

  return (
    <div className="space-y-4">
      {/* Selected Neighborhoods */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {value.map((neighborhood, index) => (
            <div
              key={index}
              className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
            >
              <MapPin className="h-3 w-3 mr-1" />
              {neighborhood.name}
              <button
                onClick={() => removeNeighborhood(index)}
                className="ml-2 hover:text-blue-600"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Search Input */}
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearchChange}
            onFocus={() => setShowDropdown(true)}
            placeholder={placeholder}
            className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded"
          >
            <Settings className="h-4 w-4 text-gray-400" />
          </button>
        </div>

        {/* Settings Panel */}
        {showSettings && (
          <div className="absolute right-0 top-12 w-64 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-20">
            <h4 className="font-medium text-gray-900 mb-3">Search Settings</h4>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search Radius: {(searchRadius / 1000).toFixed(1)}km
              </label>
              <input
                type="range"
                min="500"
                max="10000"
                step="500"
                value={searchRadius}
                onChange={(e) => setSearchRadius(Number(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>0.5km</span>
                <span>10km</span>
              </div>
            </div>
            <button
              onClick={() => {
                detectNeighborhoods();
                setShowSettings(false);
              }}
              className="mt-3 w-full px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
            >
              Update Detection
            </button>
          </div>
        )}

        {/* Dropdown */}
        {showDropdown && (
          <div className="absolute top-12 left-0 right-0 bg-white rounded-lg shadow-lg border border-gray-200 max-h-64 overflow-y-auto z-10">
            {loading && (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm">Searching neighborhoods...</p>
              </div>
            )}

            {/* Detected Neighborhoods */}
            {detectedNeighborhoods.length > 0 && (
              <div className="p-2">
                <h4 className="text-sm font-medium text-gray-700 px-2 py-1">Detected Nearby</h4>
                {detectedNeighborhoods.slice(0, 5).map((neighborhood, index) => (
                  <button
                    key={`detected-${index}`}
                    onClick={() => addDetectedNeighborhood(neighborhood)}
                    disabled={value.some(n => n.name === neighborhood.name) || value.length >= maxSelections}
                    className="w-full text-left px-3 py-2 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    <MapPin className="h-4 w-4 mr-2 text-green-500" />
                    <div>
                      <div className="font-medium">{neighborhood.name}</div>
                      <div className="text-xs text-gray-500">{neighborhood.type}</div>
                    </div>
                    <Plus className="h-4 w-4 ml-auto text-gray-400" />
                  </button>
                ))}
              </div>
            )}

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="p-2 border-t border-gray-100">
                <h4 className="text-sm font-medium text-gray-700 px-2 py-1">Search Results</h4>
                {searchResults.slice(0, 5).map((neighborhood, index) => (
                  <button
                    key={`search-${index}`}
                    onClick={() => addNeighborhood(neighborhood)}
                    disabled={value.some(n => n.name === neighborhood.name) || value.length >= maxSelections}
                    className="w-full text-left px-3 py-2 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    <Search className="h-4 w-4 mr-2 text-blue-500" />
                    <div>
                      <div className="font-medium">{neighborhood.name}</div>
                      <div className="text-xs text-gray-500">{neighborhood.formatted_address}</div>
                    </div>
                    <Plus className="h-4 w-4 ml-auto text-gray-400" />
                  </button>
                ))}
              </div>
            )}

            {!loading && detectedNeighborhoods.length === 0 && searchResults.length === 0 && (
              <div className="p-4 text-center text-gray-500">
                <p className="text-sm">No neighborhoods found</p>
                <p className="text-xs mt-1">Try entering an address or coordinates</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Info */}
      <div className="text-xs text-gray-500">
        {value.length}/{maxSelections} neighborhoods selected
        {coordinates && (
          <span className="ml-2">• Auto-detecting from location</span>
        )}
      </div>
    </div>
  );
};

export default NeighborhoodSelector;
