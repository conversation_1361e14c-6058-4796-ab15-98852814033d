'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { MapPin, Calendar, Building, Eye, Star, DollarSign } from 'lucide-react';
import { projectsAPI } from '@/lib/api';

interface Project {
  id: number;
  documentId: string;
  name: string;
  developer: string;
  city: string;
  address: string;
  projectType: string;
  status: string;
  completionDate?: string;
  minPrice?: number;
  maxPrice?: number;
  currency: string;
  totalUnits?: number;
  availableUnits?: number;
  images?: any[];
  views: number;
  featured: boolean;
}

const FeaturedProjects: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedProjects = async () => {
      try {
        setLoading(true);
        const response = await projectsAPI.getFeatured();
        setProjects(response.data || []);
      } catch (err) {
        console.error('Error fetching featured projects:', err);
        setError('Failed to load featured projects');
        // Fallback to mock data
        setProjects([
          {
            id: 1,
            documentId: 'mock-1',
            name: 'Skyline Towers',
            developer: 'Premium Developments',
            city: 'Downtown District',
            address: 'Downtown District',
            projectType: 'residential',
            status: 'under-construction',
            completionDate: '2025-12-01',
            minPrice: 450000,
            currency: 'USD',
            totalUnits: 120,
            availableUnits: 85,
            views: 342,
            featured: true
          },
          {
            id: 2,
            documentId: 'mock-2',
            name: 'Marina Bay Complex',
            developer: 'Coastal Properties',
            city: 'Waterfront Area',
            address: 'Waterfront Area',
            projectType: 'mixed-use',
            status: 'planning',
            completionDate: '2026-06-01',
            minPrice: 680000,
            currency: 'USD',
            totalUnits: 85,
            availableUnits: 85,
            views: 278,
            featured: true
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProjects();
  }, []);

  const getImageUrl = (project: Project) => {
    if (project.images && project.images.length > 0) {
      const image = project.images[0];
      return `${process.env.NEXT_PUBLIC_STRAPI_URL}${image.url}`;
    }
    return '/api/placeholder/400/300';
  };

  const formatPrice = (price: number, currency: string) => {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });
    return formatter.format(price);
  };
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planning':
        return 'bg-yellow-500';
      case 'under-construction':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'on-hold':
        return 'bg-orange-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'planning':
        return 'Planning';
      case 'under-construction':
        return 'Under Construction';
      case 'completed':
        return 'Completed';
      case 'on-hold':
        return 'On Hold';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Featured Projects
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Explore upcoming and ongoing development projects
            </p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {[1, 2].map((i) => (
              <div key={i} className="bg-white rounded-lg shadow-lg overflow-hidden animate-pulse">
                <div className="h-64 bg-gray-300"></div>
                <div className="p-6">
                  <div className="h-8 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded mb-3"></div>
                  <div className="space-y-2 mb-4">
                    <div className="h-4 bg-gray-300 rounded"></div>
                    <div className="h-4 bg-gray-300 rounded"></div>
                    <div className="h-4 bg-gray-300 rounded"></div>
                  </div>
                  <div className="h-8 bg-gray-300 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Featured Projects
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Explore upcoming and ongoing development projects
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {projects.map((project) => (
            <div key={project.documentId} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="relative">
                <img
                  src={getImageUrl(project)}
                  alt={project.name}
                  className="w-full h-64 object-cover"
                />
                <div className="absolute top-4 left-4 flex gap-2">
                  <span className={`${getStatusColor(project.status)} text-white px-3 py-1 rounded-full text-sm font-semibold`}>
                    {getStatusText(project.status)}
                  </span>
                  <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </span>
                </div>
                <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-2 py-1 rounded flex items-center">
                  <Eye className="h-4 w-4 mr-1" />
                  <span className="text-sm">{project.views}</span>
                </div>
                <div className="absolute bottom-4 left-4">
                  <span className="bg-white/90 text-gray-800 px-3 py-1 rounded-full text-sm font-semibold capitalize">
                    {project.projectType.replace('-', ' ')}
                  </span>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-2xl font-semibold text-gray-900 mb-2">
                  {project.name}
                </h3>

                <p className="text-gray-600 mb-3 font-medium">
                  by {project.developer}
                </p>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-gray-600">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span className="text-sm">{project.city}</span>
                  </div>
                  {project.completionDate && (
                    <div className="flex items-center text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span className="text-sm">
                        Completion: {new Date(project.completionDate).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                  <div className="flex items-center justify-between text-gray-600">
                    {project.totalUnits && (
                      <div className="flex items-center">
                        <Building className="h-4 w-4 mr-2" />
                        <span className="text-sm">{project.totalUnits} Total Units</span>
                      </div>
                    )}
                    {project.availableUnits && (
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1" />
                        <span className="text-sm text-green-600 font-semibold">{project.availableUnits} Available</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-600">
                      {project.minPrice && project.maxPrice && project.minPrice !== project.maxPrice
                        ? 'Price Range'
                        : 'Starting from'}
                    </div>
                    <div className="text-2xl font-bold text-blue-600">
                      {project.minPrice && project.maxPrice && project.minPrice !== project.maxPrice
                        ? `${formatPrice(project.minPrice, project.currency)} - ${formatPrice(project.maxPrice, project.currency)}`
                        : formatPrice(project.minPrice || project.maxPrice || 0, project.currency)}
                    </div>
                  </div>
                  <Link
                    href={`/projects/${project.documentId}`}
                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors font-semibold"
                  >
                    Learn More
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link
            href="/projects"
            className="bg-blue-600 text-white px-8 py-3 rounded-md font-semibold hover:bg-blue-700 transition-colors"
          >
            View All Projects
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProjects;
