#!/usr/bin/env node

/**
 * Script to clear various Strapi caches
 * Usage: node scripts/clear-cache.js [options]
 * Options:
 *   --all: Clear all caches
 *   --build: Clear build cache
 *   --uploads: Clear upload cache
 *   --help: Show help
 */

const fs = require('fs');
const path = require('path');

const args = process.argv.slice(2);
const options = {
  all: args.includes('--all'),
  build: args.includes('--build'),
  uploads: args.includes('--uploads'),
  help: args.includes('--help')
};

function showHelp() {
  console.log(`
Strapi Cache Clearing Utility

Usage: node scripts/clear-cache.js [options]

Options:
  --all      Clear all caches (build, uploads, temp files)
  --build    Clear build cache (dist/ directory)
  --uploads  Clear upload cache
  --help     Show this help message

Examples:
  node scripts/clear-cache.js --all
  node scripts/clear-cache.js --build
  node scripts/clear-cache.js --uploads
`);
}

function clearDirectory(dirPath, description) {
  try {
    if (fs.existsSync(dirPath)) {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ Cleared ${description}: ${dirPath}`);
      return true;
    } else {
      console.log(`ℹ️  ${description} directory doesn't exist: ${dirPath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error clearing ${description}:`, error.message);
    return false;
  }
}

function clearBuildCache() {
  console.log('\n🧹 Clearing build cache...');
  const distPath = path.join(__dirname, '..', 'dist');
  clearDirectory(distPath, 'Build cache');
}

function clearUploadCache() {
  console.log('\n🧹 Clearing upload cache...');
  const uploadsPath = path.join(__dirname, '..', 'public', 'uploads');
  const tmpPath = path.join(__dirname, '..', '.tmp');
  
  // Note: Be careful with uploads - you might want to backup first
  console.log('⚠️  Warning: This will clear uploaded files. Make sure you have backups!');
  
  // Clear temp files
  clearDirectory(tmpPath, 'Temporary files');
  
  // Uncomment the line below if you really want to clear uploads
  // clearDirectory(uploadsPath, 'Upload cache');
  console.log('ℹ️  Upload files preserved. Uncomment line in script to clear them.');
}

function clearAllCaches() {
  console.log('\n🧹 Clearing all caches...');
  clearBuildCache();
  clearUploadCache();
  
  // Clear other potential cache directories
  const cacheDirs = [
    path.join(__dirname, '..', 'node_modules', '.cache'),
    path.join(__dirname, '..', '.cache'),
  ];
  
  cacheDirs.forEach(dir => {
    clearDirectory(dir, 'Cache directory');
  });
}

function main() {
  console.log('🚀 Strapi Cache Clearing Utility');
  
  if (options.help || args.length === 0) {
    showHelp();
    return;
  }
  
  if (options.all) {
    clearAllCaches();
  } else {
    if (options.build) {
      clearBuildCache();
    }
    if (options.uploads) {
      clearUploadCache();
    }
  }
  
  console.log('\n✨ Cache clearing completed!');
  console.log('💡 Remember to restart your Strapi server for changes to take effect.');
}

main();
