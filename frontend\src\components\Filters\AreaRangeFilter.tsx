'use client';

import React, { useState, useEffect } from 'react';
import { Home, Maximize, Minimize } from 'lucide-react';

interface AreaRangeFilterProps {
  minArea: string;
  maxArea: string;
  areaUnit?: string;
  onMinAreaChange: (value: string) => void;
  onMaxAreaChange: (value: string) => void;
  onAreaUnitChange?: (value: string) => void;
  className?: string;
}

const AREA_UNITS = [
  { value: 'sqft', label: 'Square Feet (sq ft)', symbol: 'sq ft' },
  { value: 'sqm', label: 'Square Meters (sq m)', symbol: 'sq m' },
  { value: 'acres', label: 'Acres', symbol: 'acres' },
  { value: 'hectares', label: 'Hectares', symbol: 'hectares' },
];

const AREA_PRESETS = [
  { label: 'Under 500 sq ft', min: '', max: '500' },
  { label: '500 - 1,000 sq ft', min: '500', max: '1000' },
  { label: '1,000 - 2,000 sq ft', min: '1000', max: '2000' },
  { label: '2,000 - 3,000 sq ft', min: '2000', max: '3000' },
  { label: 'Over 3,000 sq ft', min: '3000', max: '' },
];

export const AreaRangeFilter: React.FC<AreaRangeFilterProps> = ({
  minArea,
  maxArea,
  areaUnit = 'sqft',
  onMinAreaChange,
  onMaxAreaChange,
  onAreaUnitChange,
  className = '',
}) => {
  const [validationError, setValidationError] = useState<string>('');
  const [showPresets, setShowPresets] = useState(false);

  const currentUnit = AREA_UNITS.find(u => u.value === areaUnit) || AREA_UNITS[0];

  // Validate area range
  useEffect(() => {
    if (minArea && maxArea) {
      const min = parseFloat(minArea);
      const max = parseFloat(maxArea);
      
      if (min > max) {
        setValidationError('Minimum area cannot be greater than maximum area');
      } else {
        setValidationError('');
      }
    } else {
      setValidationError('');
    }
  }, [minArea, maxArea]);

  const formatNumber = (value: string) => {
    if (!value) return '';
    const num = parseFloat(value);
    if (isNaN(num)) return value;
    return num.toLocaleString();
  };

  const handleMinAreaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/,/g, '');
    onMinAreaChange(value);
  };

  const handleMaxAreaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/,/g, '');
    onMaxAreaChange(value);
  };

  const applyPreset = (preset: typeof AREA_PRESETS[0]) => {
    onMinAreaChange(preset.min);
    onMaxAreaChange(preset.max);
    setShowPresets(false);
  };

  const clearAreaRange = () => {
    onMinAreaChange('');
    onMaxAreaChange('');
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Area Unit Selector */}
      {onAreaUnitChange && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Area Unit
          </label>
          <select
            value={areaUnit}
            onChange={(e) => onAreaUnitChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {AREA_UNITS.map(unit => (
              <option key={unit.value} value={unit.value}>
                {unit.label}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Area Range Inputs */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Area Range
        </label>
        <div className="grid grid-cols-2 gap-3">
          <div className="relative">
            <input
              type="text"
              placeholder={`Min Area (${currentUnit.symbol})`}
              value={formatNumber(minArea)}
              onChange={handleMinAreaChange}
              className={`w-full px-4 py-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                validationError ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {minArea && (
              <Minimize className="absolute right-3 top-3 h-5 w-5 text-red-500" />
            )}
          </div>

          <div className="relative">
            <input
              type="text"
              placeholder={`Max Area (${currentUnit.symbol})`}
              value={formatNumber(maxArea)}
              onChange={handleMaxAreaChange}
              className={`w-full px-4 py-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                validationError ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {maxArea && (
              <Maximize className="absolute right-3 top-3 h-5 w-5 text-green-500" />
            )}
          </div>
        </div>

        {validationError && (
          <p className="mt-2 text-sm text-red-600">{validationError}</p>
        )}
      </div>

      {/* Area Presets */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Quick Ranges</span>
          <button
            type="button"
            onClick={() => setShowPresets(!showPresets)}
            className="text-sm text-blue-600 hover:text-blue-700"
          >
            {showPresets ? 'Hide' : 'Show'} Presets
          </button>
        </div>

        {showPresets && (
          <div className="grid grid-cols-1 gap-2">
            {AREA_PRESETS.map((preset, index) => (
              <button
                key={index}
                type="button"
                onClick={() => applyPreset(preset)}
                className="text-left px-3 py-2 text-sm bg-gray-50 hover:bg-gray-100 rounded-md transition-colors"
              >
                {preset.label}
              </button>
            ))}
            <button
              type="button"
              onClick={clearAreaRange}
              className="text-left px-3 py-2 text-sm bg-red-50 hover:bg-red-100 text-red-700 rounded-md transition-colors"
            >
              Clear Area Range
            </button>
          </div>
        )}
      </div>

      {/* Current Range Display */}
      {(minArea || maxArea) && (
        <div className="p-3 bg-green-50 rounded-md">
          <div className="flex items-center space-x-2">
            <Home className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-800">
              Current Range: {minArea ? formatNumber(minArea) : '0'} - {maxArea ? formatNumber(maxArea) : '∞'} {currentUnit.symbol}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
