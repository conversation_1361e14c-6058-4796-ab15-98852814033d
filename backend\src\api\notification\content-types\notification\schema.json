{"kind": "collectionType", "collectionName": "notifications", "info": {"singularName": "notification", "pluralName": "notifications", "displayName": "Notification", "description": "User notifications system"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 255}, "message": {"type": "text", "required": true}, "type": {"type": "enumeration", "enum": ["info", "success", "warning", "error", "property_inquiry", "property_approved", "property_rejected", "message_received", "system"], "default": "info", "required": true}, "isRead": {"type": "boolean", "default": false, "required": true}, "readAt": {"type": "datetime"}, "recipient": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "required": true}, "sender": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "relatedProperty": {"type": "relation", "relation": "manyToOne", "target": "api::property.property"}, "relatedProject": {"type": "relation", "relation": "manyToOne", "target": "api::project.project"}, "relatedMessage": {"type": "relation", "relation": "manyToOne", "target": "api::message.message"}, "actionUrl": {"type": "string", "maxLength": 500}, "actionText": {"type": "string", "maxLength": 100}, "metadata": {"type": "json"}, "priority": {"type": "enumeration", "enum": ["low", "normal", "high", "urgent"], "default": "normal", "required": true}, "expiresAt": {"type": "datetime"}}}