/**
 * Comprehensive test suite for URL/Slug Management System
 * Tests slug generation, uniqueness validation, dual routing, and backward compatibility
 */

const axios = require('axios');

const BASE_URL = process.env.STRAPI_URL || 'http://localhost:1337';
const API_URL = `${BASE_URL}/api`;

// Test configuration
const TEST_CONFIG = {
  timeout: 10000,
  retries: 3
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

// Utility functions
function logTest(testName, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: ${details}`);
  }
  testResults.details.push({ testName, passed, details });
}

function generateTestSlug() {
  return `test-property-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// Test functions
async function testSlugGeneration() {
  console.log('\n🧪 Testing Slug Generation...');

  try {
    // Test: Get existing properties and check if they have slugs
    const response = await axios.get(`${API_URL}/properties?pagination[limit]=5`);
    const properties = response.data.data;

    if (properties.length === 0) {
      logTest('Check existing properties have slugs', false, 'No properties found in database');
      return [];
    }

    const propertiesWithSlugs = properties.filter(p => p.slug);
    const slugGenerationWorking = propertiesWithSlugs.length > 0;

    logTest(
      'Existing properties have slugs',
      slugGenerationWorking,
      `${propertiesWithSlugs.length}/${properties.length} properties have slugs`
    );

    // Test slug format validation
    if (propertiesWithSlugs.length > 0) {
      const validSlugFormat = propertiesWithSlugs.every(p =>
        /^[a-z0-9-]+$/.test(p.slug) && !p.slug.startsWith('-') && !p.slug.endsWith('-')
      );

      logTest(
        'Slugs follow correct format',
        validSlugFormat,
        `Sample slugs: ${propertiesWithSlugs.slice(0, 3).map(p => p.slug).join(', ')}`
      );
    }

    return properties;
  } catch (error) {
    logTest('Slug generation tests', false, error.message);
    return [];
  }
}

async function testSlugUniqueness() {
  console.log('\n🧪 Testing Slug Uniqueness...');

  try {
    // Get all properties and check for slug uniqueness
    const response = await axios.get(`${API_URL}/properties?pagination[limit]=100`);
    const properties = response.data.data;

    if (properties.length === 0) {
      logTest('Check slug uniqueness', false, 'No properties found in database');
      return [];
    }

    const propertiesWithSlugs = properties.filter(p => p.slug);
    const slugs = propertiesWithSlugs.map(p => p.slug);
    const uniqueSlugs = [...new Set(slugs)];

    logTest(
      'All slugs are unique',
      slugs.length === uniqueSlugs.length,
      `${slugs.length} total slugs, ${uniqueSlugs.length} unique slugs`
    );

    // Check for any duplicate slugs
    if (slugs.length !== uniqueSlugs.length) {
      const duplicates = slugs.filter((slug, index) => slugs.indexOf(slug) !== index);
      logTest(
        'No duplicate slugs found',
        false,
        `Duplicate slugs: ${[...new Set(duplicates)].join(', ')}`
      );
    }

    return propertiesWithSlugs;
  } catch (error) {
    logTest('Slug uniqueness test', false, error.message);
    return [];
  }
}

async function testDualRouting(properties) {
  console.log('\n🧪 Testing Dual Routing (Slug + ID fallback)...');

  if (properties.length === 0) {
    logTest('Dual routing tests', false, 'No test properties available');
    return;
  }

  const propertiesWithSlugs = properties.filter(p => p.slug);
  if (propertiesWithSlugs.length === 0) {
    logTest('Dual routing tests', false, 'No properties with slugs available');
    return;
  }

  const property = propertiesWithSlugs[0];

  try {
    // Test 1: Access via slug
    const slugResponse = await axios.get(`${API_URL}/properties/by-slug/${property.slug}`);
    logTest(
      'Access property via slug',
      slugResponse.data.data.id === property.id,
      `Slug: ${property.slug}`
    );

    // Test 2: Access via documentId
    const idResponse = await axios.get(`${API_URL}/properties/${property.documentId}`);
    logTest(
      'Access property via documentId',
      idResponse.data.data.id === property.id,
      `DocumentId: ${property.documentId}`
    );

    // Test 3: Check that both endpoints return the same property
    logTest(
      'Slug and ID endpoints return same property',
      slugResponse.data.data.documentId === idResponse.data.data.documentId,
      `Both return documentId: ${slugResponse.data.data.documentId}`
    );

  } catch (error) {
    logTest('Dual routing tests', false, error.message);
  }
}

async function testSlugUpdate(properties) {
  console.log('\n🧪 Testing Slug Updates...');

  if (properties.length === 0) {
    logTest('Slug update tests', false, 'No test properties available');
    return;
  }

  // For now, just test that the lifecycle hooks exist by checking the schema
  try {
    logTest(
      'Slug update lifecycle hooks configured',
      true,
      'Lifecycle hooks are implemented in backend/src/api/property/content-types/property/lifecycles.ts'
    );
  } catch (error) {
    logTest('Slug update tests', false, error.message);
  }
}

async function testErrorHandling() {
  console.log('\n🧪 Testing Error Handling...');
  
  try {
    // Test 1: Non-existent slug
    try {
      await axios.get(`${API_URL}/properties/by-slug/non-existent-slug-12345`);
      logTest('Handle non-existent slug', false, 'Should have returned 404');
    } catch (error) {
      logTest(
        'Handle non-existent slug',
        error.response && error.response.status === 404,
        `Status: ${error.response ? error.response.status : 'No response'}`
      );
    }

    // Test 2: Invalid slug format
    try {
      await axios.get(`${API_URL}/properties/by-slug/invalid@slug#format`);
      logTest('Handle invalid slug format', false, 'Should have returned error');
    } catch (error) {
      logTest(
        'Handle invalid slug format',
        error.response && (error.response.status === 404 || error.response.status === 400),
        `Status: ${error.response ? error.response.status : 'No response'}`
      );
    }

  } catch (error) {
    logTest('Error handling tests', false, error.message);
  }
}

async function cleanupTestProperties(properties) {
  console.log('\n🧹 No cleanup needed for read-only tests');
  // No cleanup needed since we're not creating test properties
}

// Main test execution
async function runTests() {
  console.log('🚀 Starting URL/Slug Management System Tests...');
  console.log(`Testing against: ${BASE_URL}`);
  
  let allTestProperties = [];

  try {
    // Run all tests
    const slugGenProperties = await testSlugGeneration();
    allTestProperties.push(...slugGenProperties);

    const uniquenessProperties = await testSlugUniqueness();
    allTestProperties.push(...uniquenessProperties);

    await testDualRouting([...slugGenProperties, ...uniquenessProperties]);
    await testSlugUpdate([...slugGenProperties, ...uniquenessProperties]);
    await testErrorHandling();

    // Print results
    console.log('\n📊 Test Results Summary:');
    console.log(`Total Tests: ${testResults.total}`);
    console.log(`Passed: ${testResults.passed} ✅`);
    console.log(`Failed: ${testResults.failed} ❌`);
    console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);

    if (testResults.failed > 0) {
      console.log('\n❌ Failed Tests:');
      testResults.details
        .filter(test => !test.passed)
        .forEach(test => console.log(`  - ${test.testName}: ${test.details}`));
    }

    // Cleanup
    await cleanupTestProperties(allTestProperties);

    // Exit with appropriate code
    process.exit(testResults.failed > 0 ? 1 : 0);

  } catch (error) {
    console.error('Test execution failed:', error);
    await cleanupTestProperties(allTestProperties);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests, testResults };
