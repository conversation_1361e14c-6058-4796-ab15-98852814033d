'use client';

import React, { useState } from 'react';
import { NeighborhoodSelector } from '@/components/NeighborhoodSelector';
import { NeighborhoodInfo } from '@/lib/googleMaps';

const TestMapsPage: React.FC = () => {
  const [neighborhoods, setNeighborhoods] = useState<NeighborhoodInfo[]>([]);
  const [testAddress, setTestAddress] = useState('1600 Amphitheatre Parkway, Mountain View, CA');
  const [testCoordinates, setTestCoordinates] = useState<{ lat: number; lng: number } | undefined>();

  const handleAddressTest = () => {
    // Clear coordinates to test address-based detection
    setTestCoordinates(undefined);
  };

  const handleCoordinatesTest = () => {
    // Set test coordinates (Google HQ)
    setTestCoordinates({ lat: 37.4221, lng: -122.0841 });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Google Maps API Test</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">API Key Status</h2>
          <p className="text-sm text-gray-600 mb-4">
            API Key: {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? '✅ Configured' : '❌ Missing'}
          </p>
          {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY && (
            <p className="text-xs text-gray-500">
              Key: {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY.substring(0, 10)}...
            </p>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Test Address
              </label>
              <input
                type="text"
                value={testAddress}
                onChange={(e) => setTestAddress(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="flex space-x-4">
              <button
                onClick={handleAddressTest}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Test Address Detection
              </button>
              <button
                onClick={handleCoordinatesTest}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Test Coordinates Detection
              </button>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Neighborhood Selector Test</h2>
          
          <NeighborhoodSelector
            value={neighborhoods}
            onChange={setNeighborhoods}
            coordinates={testCoordinates}
            address={testCoordinates ? undefined : testAddress}
            maxSelections={5}
            placeholder="Test neighborhood detection and search..."
          />
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Selected Neighborhoods</h2>
          
          {neighborhoods.length === 0 ? (
            <p className="text-gray-500">No neighborhoods selected</p>
          ) : (
            <div className="space-y-2">
              {neighborhoods.map((neighborhood, index) => (
                <div key={index} className="p-3 bg-gray-50 rounded-md">
                  <div className="font-medium">{neighborhood.name}</div>
                  <div className="text-sm text-gray-600">Type: {neighborhood.type}</div>
                  <div className="text-sm text-gray-600">Address: {neighborhood.formatted_address}</div>
                  {neighborhood.place_id && (
                    <div className="text-xs text-gray-500">Place ID: {neighborhood.place_id}</div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TestMapsPage;
