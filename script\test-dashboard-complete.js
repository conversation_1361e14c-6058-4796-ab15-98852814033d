const axios = require('axios');

const API_URL = 'http://localhost:1337/api';

async function testDashboardComplete() {
  console.log('🧪 Complete Dashboard Test with Working User...\n');

  try {
    // Test login with the created user
    console.log('1. Testing login...');
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    console.log('✅ Login successful!');
    console.log(`   User ID: ${loginResponse.data.user.id}`);
    console.log(`   Username: ${loginResponse.data.user.username}`);
    console.log(`   Email: ${loginResponse.data.user.email}`);
    
    const token = loginResponse.data.jwt;
    console.log(`   JWT Token: ${token.substring(0, 30)}...`);
    
    // Test dashboard endpoints
    console.log('\n2. Testing dashboard endpoints...');
    
    // Test my properties
    const myPropertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ My Properties endpoint working');
    console.log(`   User has ${myPropertiesResponse.data.data?.length || 0} properties`);
    
    // Test user profile endpoint
    try {
      const userResponse = await axios.get(`${API_URL}/users/me`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ User profile endpoint working');
      console.log(`   User profile loaded: ${userResponse.data.username}`);
    } catch (userError) {
      console.log('⚠️  User profile endpoint issue:', userError.response?.status);
    }
    
    // Test featured properties (public endpoint)
    const featuredResponse = await axios.get(`${API_URL}/properties/featured`);
    console.log('✅ Featured properties endpoint working');
    console.log(`   Found ${featuredResponse.data.data?.length || 0} featured properties`);
    
    // Test all properties (public endpoint)
    const allPropertiesResponse = await axios.get(`${API_URL}/properties`);
    console.log('✅ All properties endpoint working');
    console.log(`   Found ${allPropertiesResponse.data.data?.length || 0} total properties`);
    
    console.log('\n3. Dashboard API Summary:');
    console.log('   ✅ Authentication: Working');
    console.log('   ✅ User Properties: Working');
    console.log('   ✅ Featured Properties: Working');
    console.log('   ✅ All Properties: Working');
    
    console.log('\n🎉 Dashboard is fully functional!');
    console.log('\n📝 Frontend Test Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: TestPassword123!');
    console.log('\n💡 Next steps:');
    console.log('   1. Open http://localhost:3000/auth/login');
    console.log('   2. Login with the credentials above');
    console.log('   3. Navigate to http://localhost:3000/dashboard');
    console.log('   4. Verify all dashboard features work');
    
  } catch (error) {
    console.log('❌ Test failed:', error.response?.data || error.message);
  }
}

testDashboardComplete().catch(console.error);
