const axios = require('axios');

const API_URL = 'http://localhost:1337/api';

async function enableRegistration() {
  console.log('🔧 Attempting to enable user registration...\n');

  try {
    // First, let's check if there are any existing users we can use
    console.log('1. Checking existing users...');
    
    // Try some common test credentials that might exist
    const testCredentials = [
      { email: '<EMAIL>', password: 'admin123' },
      { email: '<EMAIL>', password: 'test123' },
      { email: '<EMAIL>', password: 'password' },
      { email: '<EMAIL>', password: 'demo123' }
    ];
    
    for (const creds of testCredentials) {
      try {
        console.log(`   Trying ${creds.email}...`);
        const loginResponse = await axios.post(`${API_URL}/auth/local`, {
          identifier: creds.email,
          password: creds.password
        });
        
        console.log(`✅ Found working credentials!`);
        console.log(`   Email: ${creds.email}`);
        console.log(`   Password: ${creds.password}`);
        console.log(`   User ID: ${loginResponse.data.user.id}`);
        
        // Test dashboard access
        const token = loginResponse.data.jwt;
        const myPropertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        console.log('✅ Dashboard access confirmed');
        console.log(`   User has ${myPropertiesResponse.data.data?.length || 0} properties`);
        
        console.log('\n📝 Use these credentials to test the frontend dashboard:');
        console.log(`   Email: ${creds.email}`);
        console.log(`   Password: ${creds.password}`);
        
        return; // Exit if we found working credentials
        
      } catch (error) {
        console.log(`   ❌ ${creds.email} failed`);
      }
    }
    
    console.log('\n2. No existing test users found. Checking registration status...');
    
    // Try to register with a simple user
    try {
      const registerResponse = await axios.post(`${API_URL}/auth/local/register`, {
        username: 'testuser123',
        email: '<EMAIL>',
        password: 'TestPassword123!'
      });
      
      console.log('✅ Registration is enabled! User created successfully!');
      console.log(`   Email: <EMAIL>`);
      console.log(`   Password: TestPassword123!`);
      console.log(`   User ID: ${registerResponse.data.user.id}`);
      
    } catch (regError) {
      console.log('❌ Registration is disabled');
      console.log('\n💡 To fix this, you need to:');
      console.log('   1. Go to http://localhost:1337/admin');
      console.log('   2. Navigate to Settings > Users & Permissions plugin > Roles');
      console.log('   3. Click on "Public" role');
      console.log('   4. Find "Auth" section and enable "Register"');
      console.log('   5. Save the changes');
      console.log('\n   OR create a user manually in the admin panel:');
      console.log('   1. Go to Content Manager > User (users-permissions)');
      console.log('   2. Click "Create new entry"');
      console.log('   3. Fill in the details and save');
    }
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

enableRegistration().catch(console.error);
