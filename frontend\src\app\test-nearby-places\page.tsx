'use client';

import React, { useState } from 'react';
import Layout from '@/components/Layout/Layout';
import { GooglePlaceTypesSelector } from '@/components/GooglePlaceTypesSelector';
import { CoordinateSelector } from '@/components/CoordinateSelector';
import { NearbyPlaces } from '@/components/NearbyPlaces';
import { MapPin, Settings, TestTube } from 'lucide-react';

const TestNearbyPlacesPage: React.FC = () => {
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | undefined>();
  const [nearbyPlaces, setNearbyPlaces] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testNearbyPlaces = async () => {
    if (!coordinates) {
      alert('Please set coordinates first');
      return;
    }

    if (selectedTypes.length === 0) {
      alert('Please select at least one place type');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Create a test property with coordinates
      const testProperty = {
        title: 'Test Property',
        description: 'Test property for nearby places',
        price: 100000,
        currency: 'USD',
        propertyType: 'apartment',
        offer: 'for-sale',
        bedrooms: 2,
        bathrooms: 1,
        area: 100,
        areaUnit: 'sqm',
        address: 'Test Address',
        city: 'Test City',
        country: 'Test Country',
        coordinates: coordinates,
        neighborhood: []
      };

      // Create property
      const createResponse = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/api/properties`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('jwt')}`
        },
        body: JSON.stringify({ data: testProperty })
      });

      if (!createResponse.ok) {
        throw new Error('Failed to create test property');
      }

      const createdProperty = await createResponse.json();
      const propertyId = createdProperty.data.id;

      // Generate nearby places
      const generateResponse = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/api/properties/${propertyId}/generate-nearby-places`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('jwt')}`
        }
      });

      if (generateResponse.ok) {
        const data = await generateResponse.json();
        setNearbyPlaces(data.data.nearbyPlaces);
      } else {
        const errorData = await generateResponse.json();
        setError(`Failed to generate nearby places: ${errorData.error?.message || 'Unknown error'}`);
      }

      // Clean up - delete test property
      await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/api/properties/${propertyId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('jwt')}`
        }
      });

    } catch (err: any) {
      setError(err.message || 'Failed to test nearby places');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <TestTube className="h-8 w-8 text-blue-600" />
              <h1 className="text-3xl font-bold text-gray-900">Test Nearby Places System</h1>
            </div>
            <p className="text-gray-600">
              Test the Yelp-style nearby places functionality with accurate Google Place Types.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Configuration Panel */}
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Settings className="h-5 w-5 text-gray-600" />
                  <h2 className="text-xl font-semibold text-gray-900">Configuration</h2>
                </div>

                {/* Coordinate Selector */}
                <div className="mb-6">
                  <CoordinateSelector
                    value={coordinates}
                    onChange={setCoordinates}
                    address="New York, NY, USA"
                  />
                </div>

                {/* Google Place Types Selector */}
                <GooglePlaceTypesSelector
                  selectedTypes={selectedTypes}
                  onChange={setSelectedTypes}
                  maxSelections={5}
                />

                {/* Test Button */}
                <div className="mt-6">
                  <button
                    onClick={testNearbyPlaces}
                    disabled={loading || !coordinates || selectedTypes.length === 0}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <MapPin className="h-4 w-4" />
                    <span>{loading ? 'Testing...' : 'Test Nearby Places'}</span>
                  </button>
                </div>

                {/* Error Display */}
                {error && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-600">{error}</p>
                  </div>
                )}
              </div>

              {/* Instructions */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">How to Test:</h3>
                <ol className="text-sm text-blue-800 space-y-1">
                  <li>1. Set property coordinates (use current location or enter manually)</li>
                  <li>2. Select Google Place Types you want to find nearby</li>
                  <li>3. Click "Test Nearby Places" to see results</li>
                  <li>4. View the Yelp-style display of nearby places</li>
                </ol>
              </div>
            </div>

            {/* Results Panel */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Results</h2>
              
              {!nearbyPlaces && !loading && (
                <div className="text-center py-12">
                  <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-600">Configure settings and click "Test Nearby Places" to see results</p>
                </div>
              )}

              {loading && (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-3"></div>
                  <p className="text-gray-600">Finding nearby places...</p>
                </div>
              )}

              {nearbyPlaces && (
                <div className="max-h-96 overflow-y-auto">
                  <NearbyPlaces nearbyPlaces={nearbyPlaces} />
                </div>
              )}
            </div>
          </div>

          {/* Admin Note */}
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="font-medium text-yellow-900 mb-2">For Admin Users:</h3>
            <p className="text-sm text-yellow-800">
              The Google Place Types selector shows all available types from the Google Places API. 
              When creating place categories in the admin panel, use these exact values for the 
              "googlePlaceTypes" field to ensure accurate results.
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default TestNearbyPlacesPage;
