// Strapi 5 middleware configuration for Chartbrew integration
// Replace your config/middlewares.js with this content

module.exports = [
  'strapi::logger',
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': [
            "'self'", 
            'http:', 
            'https:', 
            'http://localhost:*',
            'http://127.0.0.1:*',
            'ws://localhost:*',
            'wss://localhost:*'
          ],
          'frame-src': [
            "'self'", 
            'http://localhost:*',
            'http://127.0.0.1:*'
          ],
          'img-src': [
            "'self'", 
            'data:', 
            'blob:', 
            'http:', 
            'https:'
          ],
          'script-src': [
            "'self'",
            "'unsafe-inline'",
            "'unsafe-eval'",
            'http://localhost:*',
            'http://127.0.0.1:*'
          ],
          upgradeInsecureRequests: null,
        },
      },
    },
  },
  {
    name: 'strapi::cors',
    config: {
      enabled: true,
      headers: ['*'],
      origin: [
        'http://localhost:1337',
        'http://127.0.0.1:1337',
        'http://localhost:3000',
        'http://127.0.0.1:3000', 
        'http://localhost:4018', // Chartbrew frontend
        'http://127.0.0.1:4018',
        'http://localhost:4019', // Chartbrew API
        'http://127.0.0.1:4019',
        // Add your production domains here
      ],
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
      credentials: true,
    }
  },
  'strapi::poweredBy',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
