{"name": "client", "private": true, "version": "v4.1.0", "type": "module", "scripts": {"start": "echo n | cp -vipr ../.env .env | true && npm run prepareSettings && vite", "build": "npm run prepareSettings && vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "tailwind": "npx tailwindcss -i ./src/input.css -o ./dist/output.css --watch", "prepareSettings": "echo n | cp -vipr src/config/settings.template.js src/config/settings.js | true"}, "dependencies": {"@heroui/react": "^2.7.6", "@internationalized/date": "^3.5.3", "@reduxjs/toolkit": "^2.2.1", "ace-builds": "^1.4.12", "allotment": "^1.19.3", "chart.js": "^4.4.2", "chartjs-plugin-datalabels": "^2.0.0", "date-fns": "^2.30.0", "dotenv": "^16.4.5", "dotenv-expand": "^11.0.6", "framer-motion": "^12.23.0", "immutability-helper": "^3.1.1", "lodash": "^4.17.20", "moment": "^2.29.1", "nanoid": "^5.0.6", "node-sql-parser": "^5.2.0", "prop-types": "^15.8.1", "raf": "^3.4.1", "react": "^18.2.0", "react-ace": "^13.0.0", "react-chartjs-2": "^5.2.0", "react-color": "^2.19.3", "react-cookies": "^0.1.1", "react-date-range": "^1.1.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-dropzone": "^11.4.2", "react-grid-layout": "^1.4.4", "react-helmet": "^6.1.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-joyride": "^2.7.2", "react-markdown": "^10.1.0", "react-redux": "^9.1.0", "react-refresh": "^0.14.0", "react-router": "^6.16.0", "react-router-dom": "^6.16.0", "react-table": "^7.6.3", "react-use": "^17.4.0", "redux": "^5.0.1", "redux-first-history": "^5.1.1", "redux-logger": "^3.0.6", "redux-thunk": "^3.1.0", "remark-gfm": "^4.0.0", "styled-components": "^4.4.1", "tailwind-variants": "^0.2.1", "uuid": "^9.0.1"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react-swc": "^3.3.2", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.28", "tailwindcss": "^3.3.3", "vite": "^7.0.2"}}