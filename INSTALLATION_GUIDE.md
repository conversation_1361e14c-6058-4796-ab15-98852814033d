# Chartbrew Installation Guide

This comprehensive guide covers both manual installation and Docker setup methods for Chartbrew, a powerful data visualization platform.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Manual Installation](#manual-installation)
3. [Docker Installation](#docker-installation)
4. [Configuration Details](#configuration-details)
5. [Troubleshooting](#troubleshooting)
6. [Next Steps](#next-steps)


FROM node:22-slim

WORKDIR /code
COPY . .

RUN apt-get update \
  && apt-get install -y gnupg dos2unix \
  && apt-key adv … || true \
  && apt-get update

# Convert line endings, install deps, build UI
RUN dos2unix entrypoint.sh

RUN cd client && npm install \
 && cd ../server && npm install \
 && npx playwright install-deps && npx playwright install

RUN npm run prepareSettings
RUN cd client && npm run build

RUN chmod +x entrypoint.sh

EXPOSE 4018 4019
ENTRYPOINT ["./entrypoint.sh"]


## Prerequisites

### For Manual Installation
- **Node.js** (v16 or higher)
- **MySQL** (v8.0 or higher) or **PostgreSQL**
- **Redis** (v6 or higher)
- **Git**

### For Docker Installation
- **Docker** (v20.10 or higher)
- **Docker Compose** (v2.0 or higher)

## Manual Installation

### 1. Clone the Repository

```bash
git clone https://github.com/chartbrew/chartbrew.git
cd chartbrew
```

### 2. Install Dependencies

Install server dependencies:
```bash
cd server
npm install
cd ..
```

Install client dependencies:
```bash
cd client
npm install
cd ..
```

### 3. Database Setup

#### Option A: Using Docker for Database Only
If you prefer to use Docker just for MySQL and Redis while running Chartbrew manually:

```bash
# Start MySQL and Redis containers
docker-compose up -d db redis

# Verify containers are running
docker-compose ps
```

#### Option B: Local MySQL Installation
Install MySQL locally and create a database:
```sql
CREATE DATABASE chartbrew;
CREATE USER 'chartbrew_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON chartbrew.* TO 'chartbrew_user'@'localhost';
FLUSH PRIVILEGES;
```

### 4. Environment Configuration

Copy the environment template:
```bash
cp .env-template .env
```

Edit the `.env` file with your configuration:

```env
#### PRODUCTION VARS ####
### Database connection parameters
CB_DB_NAME=chartbrew
CB_DB_USERNAME=chartbrew_user
CB_DB_PASSWORD=chartbrew_password_123
CB_DB_HOST=localhost
CB_DB_PORT=3306

# Redis connection parameters
CB_REDIS_HOST=localhost
CB_REDIS_PORT=6379
CB_REDIS_PASSWORD=redis_password_123

### Encryption keys (will be auto-generated)
CB_SECRET=your_secret_key_here
CB_ENCRYPTION_KEY=

### API Configuration
CB_API_HOST=localhost
CB_API_PORT=4019

### Client Configuration
VITE_APP_CLIENT_HOST=http://localhost:4018
VITE_APP_CLIENT_PORT=4018
VITE_APP_API_HOST=http://localhost:4019

#### DEVELOPMENT VARS ####
# Database connection parameters
CB_DB_NAME_DEV=chartbrew
CB_DB_USERNAME_DEV=chartbrew_user
CB_DB_PASSWORD_DEV=chartbrew_password_123
CB_DB_HOST_DEV=localhost
CB_DB_PORT_DEV=3306

# Redis connection parameters
CB_REDIS_HOST_DEV=localhost
CB_REDIS_PORT_DEV=6379
CB_REDIS_PASSWORD_DEV=redis_password_123

# Encryption keys
CB_SECRET_DEV=your_dev_secret_key_here
CB_ENCRYPTION_KEY_DEV=

# API Configuration
CB_API_HOST_DEV=localhost
CB_API_PORT_DEV=4019

# Client Configuration
VITE_APP_CLIENT_HOST_DEV=http://localhost:4018
VITE_APP_CLIENT_PORT_DEV=4018
VITE_APP_API_HOST_DEV=http://localhost:4019
```

### 5. Generate Encryption Keys

Generate secure encryption keys:
```bash
# Generate a 64-character hex key
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

Add the generated key to both `CB_ENCRYPTION_KEY` and `CB_ENCRYPTION_KEY_DEV` in your `.env` file.

### 6. Copy Environment to Server

```bash
cp .env server/.env
```

### 7. Run Database Migrations

```bash
cd server
npm run db:migrate
```

### 8. Start the Applications

Start the server (in development mode):
```bash
cd server
npm run start-dev
```

In a new terminal, start the client:
```bash
cd client
npm run start
```

## Docker Installation

### 1. Clone the Repository

```bash
git clone https://github.com/chartbrew/chartbrew.git
cd chartbrew
```

### 2. Environment Configuration

Copy and configure the environment file:
```bash
cp .env-template .env
```

For Docker installation, ensure these settings in your `.env` file:
```env
# Database (Docker containers)
CB_DB_HOST=db
CB_DB_PORT=3306
CB_REDIS_HOST=redis
CB_REDIS_PORT=6379

# API Configuration for Docker
CB_API_HOST=0.0.0.0
CB_API_PORT=4019
```

### 3. Start with Docker Compose

```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f chartbrew
```

### 4. Access the Application

Once running, access Chartbrew at:
- **Frontend**: http://localhost:4018
- **API**: http://localhost:4019

## Configuration Details

### Required Environment Variables

#### Database Configuration
- `CB_DB_NAME` / `CB_DB_NAME_DEV`: Database name
- `CB_DB_USERNAME` / `CB_DB_USERNAME_DEV`: Database username
- `CB_DB_PASSWORD` / `CB_DB_PASSWORD_DEV`: Database password
- `CB_DB_HOST` / `CB_DB_HOST_DEV`: Database host
- `CB_DB_PORT` / `CB_DB_PORT_DEV`: Database port

#### Redis Configuration
- `CB_REDIS_HOST` / `CB_REDIS_HOST_DEV`: Redis host
- `CB_REDIS_PORT` / `CB_REDIS_PORT_DEV`: Redis port
- `CB_REDIS_PASSWORD` / `CB_REDIS_PASSWORD_DEV`: Redis password

#### Security Settings
- `CB_SECRET` / `CB_SECRET_DEV`: Legacy secret key
- `CB_ENCRYPTION_KEY` / `CB_ENCRYPTION_KEY_DEV`: AES-256 encryption key (64 hex chars)

#### Application Settings
- `CB_API_HOST` / `CB_API_HOST_DEV`: API server host
- `CB_API_PORT` / `CB_API_PORT_DEV`: API server port
- `VITE_APP_CLIENT_HOST` / `VITE_APP_CLIENT_HOST_DEV`: Frontend URL
- `VITE_APP_API_HOST` / `VITE_APP_API_HOST_DEV`: API URL for frontend

### Port Configuration

Default ports:
- **Frontend**: 4018
- **API**: 4019
- **MySQL**: 3306
- **Redis**: 6379

## Troubleshooting

### Common Issues

#### 1. Encryption Key Validation Error
```
Invalid AES-256 encryption key. It must be a 64-character hexadecimal string.
```

**Solution**: Generate a proper encryption key:
```bash
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

#### 2. Database Connection Refused
```
ConnectionRefusedError [SequelizeConnectionRefusedError]
```

**Solutions**:
- Ensure MySQL is running and accessible
- Check database credentials in `.env` file
- For Docker: Ensure database container is running and healthy
- Verify port 3306 is not blocked by firewall

#### 3. Redis Connection Issues
```
[WARN] This Redis server's default user does not require a password
```

**Solution**: This is just a warning. If Redis doesn't require a password, set:
```env
CB_REDIS_PASSWORD=
CB_REDIS_PASSWORD_DEV=
```

#### 4. Environment Variables Not Loading
- Ensure `.env` file is in the correct location (project root)
- Copy `.env` to `server/.env` for server-side access
- Check for typos in variable names

#### 5. Port Already in Use
```
Error: listen EADDRINUSE: address already in use :::4019
```

**Solution**: 
- Kill processes using the ports: `lsof -ti:4019 | xargs kill -9`
- Or change ports in `.env` file

### Docker-Specific Issues

#### 1. Container Health Checks
Check container health:
```bash
docker-compose ps
docker-compose logs db
docker-compose logs redis
```

#### 2. Database Initialization
If database fails to initialize:
```bash
docker-compose down -v  # Remove volumes
docker-compose up -d
```

## Next Steps

### 1. Access the Application
- Open http://localhost:4018 in your browser
- You should see the Chartbrew login/signup page

### 2. Create Your First Account
- Click "Sign Up" to create an admin account
- Fill in your details and create your account

### 3. Initial Setup
- Create your first team/organization
- Set up your first data connection
- Create your first chart or dashboard

### 4. Additional Configuration

#### Email Settings (Optional)
Configure email for invitations and password resets:
```env
CB_MAIL_HOST=smtp.gmail.com
CB_MAIL_USER=<EMAIL>
CB_MAIL_PASS=your-app-password
CB_MAIL_PORT=465
CB_MAIL_SECURE=true
CB_ADMIN_MAIL=<EMAIL>
```

#### Google OAuth (Optional)
For Google integrations:
```env
CB_GOOGLE_CLIENT_ID=your-google-client-id
CB_GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### 5. Production Deployment

For production deployment:
- Use production environment variables (without `_DEV` suffix)
- Set up proper SSL certificates
- Configure reverse proxy (nginx/Apache)
- Set up monitoring and backups
- Use managed database services for better reliability

## Support

- **Documentation**: https://docs.chartbrew.com
- **GitHub Issues**: https://github.com/chartbrew/chartbrew/issues
- **Community**: https://discord.gg/KwGEbFk

---

**Note**: This guide covers the basic installation process. For advanced configurations, scaling, and production deployments, refer to the official Chartbrew documentation.
