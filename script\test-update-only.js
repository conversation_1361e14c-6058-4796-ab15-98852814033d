const fetch = require('node-fetch');

const STRAPI_URL = 'http://localhost:1337';

async function testUpdate() {
  console.log('🔐 Logging in...');
  const loginResponse = await fetch(`${STRAPI_URL}/api/auth/local`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      identifier: '<EMAIL>',
      password: 'Mb123321'
    })
  });

  const loginData = await loginResponse.json();
  const jwt = loginData.jwt;
  console.log('✅ Login successful');

  // Test update with documentId
  console.log('✏️ Testing update with documentId...');
  const updateData = {
    data: {
      title: 'Test Property - Updated ' + new Date().toISOString(),
      description: 'Updated description with timestamp'
    }
  };

  const updateResponse = await fetch(`${STRAPI_URL}/api/properties/udxctx875q45lwj2q2s6brwn`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${jwt}`
    },
    body: JSON.stringify(updateData)
  });

  console.log('Update response status:', updateResponse.status);
  
  if (updateResponse.ok) {
    const updateResult = await updateResponse.json();
    console.log('✅ Update successful');
    console.log('Response:', JSON.stringify(updateResult, null, 2));
  } else {
    const errorData = await updateResponse.json();
    console.log('❌ Update failed');
    console.log('Error:', JSON.stringify(errorData, null, 2));
  }
}

testUpdate();
