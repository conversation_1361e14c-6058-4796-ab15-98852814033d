flowchart TD
    %% User Layer
    subgraph Users["👥 Users"]
        Guest["🔍 Guest<br/>Browse Properties"]
        User["👤 Registered User<br/>Manage Properties"]
        Agent["🏢 Agent<br/>Professional Tools"]
        Admin["⚙️ Administrator<br/>System Management"]
    end

    %% Frontend Applications
    subgraph Frontend["🖥️ Frontend Applications"]
        subgraph Public["🌐 Public Website"]
            Home["🏠 Homepage<br/>Featured Properties"]
            Browse["📋 Property Listings<br/>Advanced Search"]
            Details["📄 Property Details<br/>Contact & Info"]
        end
        
        subgraph Dashboard["📊 User Dashboard"]
            MyProps["🏘️ My Properties<br/>Manage Listings"]
            Submit["➕ Add Property<br/>Upload & Edit"]
            Messages["💬 Messages<br/>Inquiries & Chat"]
            Profile["👤 Profile<br/>Account Settings"]
        end
    end

    %% Core Features
    subgraph Features["⭐ Core Features"]
        Search["🔍 Smart Search<br/>Filters & Location"]
        Maps["🗺️ Interactive Maps<br/>Nearby Places"]
        Media["📸 Media Management<br/>Photos & Documents"]
        Notifications["🔔 Notifications<br/>Real-time Updates"]
    end

    %% Backend System
    subgraph Backend["⚡ Backend System"]
        API["🔌 REST API<br/>Strapi CMS v5"]
        Auth["🔐 Authentication<br/>JWT Security"]
        Database["💾 Database<br/>Properties & Users"]
        Services["🛠️ Services<br/>Business Logic"]
    end

    %% External Services
    subgraph External["🌍 External Services"]
        GoogleMaps["🗺️ Google Maps<br/>Location Data"]
        GooglePlaces["📍 Google Places<br/>Nearby Services"]
        Email["📧 Email Service<br/>Notifications"]
    end

    %% User Flow Connections
    Guest --> Home
    Guest --> Browse
    Guest --> Details
    
    User --> Dashboard
    User --> MyProps
    User --> Submit
    User --> Messages
    
    Agent --> Dashboard
    Admin --> Backend

    %% Feature Connections
    Browse --> Search
    Details --> Maps
    Submit --> Media
    Messages --> Notifications

    %% Backend Connections
    Frontend --> API
    API --> Auth
    API --> Database
    API --> Services

    %% External Connections
    Maps --> GoogleMaps
    Search --> GooglePlaces
    Notifications --> Email

    %% Styling for better visual appeal
    classDef userStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    classDef frontendStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#000
    classDef featureStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#000
    classDef backendStyle fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#000
    classDef externalStyle fill:#fce4ec,stroke:#c2185b,stroke-width:3px,color:#000

    class Guest,User,Agent,Admin userStyle
    class Home,Browse,Details,MyProps,Submit,Messages,Profile frontendStyle
    class Search,Maps,Media,Notifications featureStyle
    class API,Auth,Database,Services backendStyle
    class GoogleMaps,GooglePlaces,Email externalStyle