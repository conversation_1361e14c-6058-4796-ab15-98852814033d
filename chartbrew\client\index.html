<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#ECEFF1">
    <meta name="description"
      content="Chartbrew allows you to connect all your databases and APIs to create beautiful live charts and visualize your data" />

    <meta property="og:type" content="product" />
    <meta property="og:title" content="Chartbrew - Visualize your data in one place" />
    <meta property="og:url" content="https://chartbrew.com" />
    <meta property="og:site_name" content="Chartbrew" />
    <meta property="og:description"
      content="Chartbrew allows you to connect all your databases and APIs to create beautiful live charts and visualize your data" />
    <meta property="og:image" content="https://cdn2.chartbrew.com/banners/MainOG.png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="twitter:creator" content="@chartbrew">
    <meta property="twitter:site" content="@chartbrew">
    <meta property="twitter:card" content="summary_large_image">
    <meta property="keywords"
      content="charts, visualize, visualisation, visualization, mongodb, data, mysql, postgres, postgresql, api, firebase, sql, dashboard, analytics, public, open, webapp" />

    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Titillium+Web:wght@400;600;700&display=swap" rel="stylesheet">
    
    <script>
      // @see https://docs.headwayapp.co/widget for more configuration options.
      var HW_config = {
        selector: ".changelog-badge", // CSS selector where to inject the badge
        trigger: ".changelog-trigger",
        account: "JVODWy"
      }
    </script>
    <script async src="https://cdn.headwayapp.co/widget.js"></script>

    <script>
      if (global === undefined) {
        var global = window;
      }
    </script>

    <title>Chartbrew - Visualize your data in one place</title>
  </head>
  <body class="text-foreground bg-background" style="min-height: 100vh;">
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
