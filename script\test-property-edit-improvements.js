const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const API_URL = 'http://localhost:1337/api';

class PropertyEditTester {
  constructor() {
    this.token = null;
    this.testPropertyId = null;
  }

  async login() {
    try {
      const response = await axios.post(`${API_URL}/auth/local`, {
        identifier: '<EMAIL>',
        password: 'password123'
      });
      this.token = response.data.jwt;
      console.log('✅ Login successful');
      return true;
    } catch (error) {
      console.log('❌ Login failed:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async createTestProperty() {
    try {
      console.log('🏠 Creating test property...');
      const response = await axios.post(`${API_URL}/properties`, {
        data: {
          title: 'Test Property for Edit Testing',
          description: 'This is a test property created for testing edit functionality.',
          price: 250000,
          currency: 'USD',
          propertyType: 'apartment',
          offer: 'for-sale',
          bedrooms: 2,
          bathrooms: 2,
          area: 100,
          areaUnit: 'sqm',
          address: '123 Test Street',
          city: 'Test City',
          country: 'Test Country'
        }
      }, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      const property = response.data.data;
      this.testPropertyId = property.documentId || property.id;
      console.log(`✅ Test property created with ID: ${this.testPropertyId}`);
      return property;
    } catch (error) {
      console.log('❌ Failed to create test property:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  async getMyProperties() {
    try {
      const response = await axios.get(`${API_URL}/properties/my-properties`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });
      const properties = response.data.data || response.data;
      console.log(`✅ Found ${properties.length} properties`);

      if (properties.length > 0) {
        this.testPropertyId = properties[0].documentId || properties[0].id;
        console.log(`📝 Using property ID: ${this.testPropertyId}`);
        return properties[0];
      }

      // If no properties exist, create one
      return await this.createTestProperty();
    } catch (error) {
      console.log('❌ Failed to get properties:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  async testPropertyEdit() {
    if (!this.testPropertyId) {
      console.log('❌ No test property available');
      return false;
    }

    try {
      console.log('\n🧪 Testing property edit functionality...');
      
      // Test 1: Get property for editing
      console.log('1. Testing property fetch for editing...');
      const editResponse = await axios.get(`${API_URL}/properties/${this.testPropertyId}/edit`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });
      console.log('✅ Property edit data fetched successfully');
      console.log(`   - Title: ${editResponse.data.title}`);
      console.log(`   - Images: ${editResponse.data.images?.length || 0}`);
      console.log(`   - Published: ${editResponse.data.publishedAt ? 'Yes' : 'No'}`);

      // Test 2: Test publish/unpublish functionality
      console.log('\n2. Testing publish/unpublish functionality...');
      const currentStatus = editResponse.data.publishedAt;
      
      if (currentStatus) {
        // Test unpublish
        await axios.put(`${API_URL}/properties/${this.testPropertyId}`, {
          data: { publishedAt: null }
        }, {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        console.log('✅ Property unpublished successfully');
        
        // Test publish again
        await axios.put(`${API_URL}/properties/${this.testPropertyId}`, {
          data: { publishedAt: new Date().toISOString() }
        }, {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        console.log('✅ Property published successfully');
      } else {
        // Test publish
        await axios.put(`${API_URL}/properties/${this.testPropertyId}`, {
          data: { publishedAt: new Date().toISOString() }
        }, {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        console.log('✅ Property published successfully');
        
        // Test unpublish
        await axios.put(`${API_URL}/properties/${this.testPropertyId}`, {
          data: { publishedAt: null }
        }, {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        console.log('✅ Property unpublished successfully');
      }

      // Test 3: Test property update with basic data
      console.log('\n3. Testing property update...');
      const updateData = {
        title: editResponse.data.title + ' (Updated)',
        description: (editResponse.data.description || '') + ' - Updated for testing.'
      };
      
      const updateResponse = await axios.put(`${API_URL}/properties/${this.testPropertyId}`, {
        data: updateData
      }, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });
      console.log('✅ Property updated successfully');
      
      // Revert the update
      await axios.put(`${API_URL}/properties/${this.testPropertyId}`, {
        data: {
          title: editResponse.data.title,
          description: editResponse.data.description
        }
      }, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });
      console.log('✅ Property update reverted');

      return true;
    } catch (error) {
      console.log('❌ Property edit test failed:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async testImageUpload() {
    if (!this.testPropertyId) {
      console.log('❌ No test property available for image upload test');
      return false;
    }

    try {
      console.log('\n🖼️  Testing image upload functionality...');
      
      // Create a simple test image file
      const testImagePath = path.join(__dirname, 'test-image.jpg');
      if (!fs.existsSync(testImagePath)) {
        console.log('⚠️  Test image not found, skipping image upload test');
        return true;
      }

      const formData = new FormData();
      formData.append('data', JSON.stringify({
        title: 'Test Property with Image Update'
      }));
      formData.append('files.images', fs.createReadStream(testImagePath));

      const response = await axios.put(`${API_URL}/properties/${this.testPropertyId}`, formData, {
        headers: {
          'Authorization': `Bearer ${this.token}`,
          ...formData.getHeaders()
        }
      });

      console.log('✅ Image upload test completed');
      console.log(`   - Property updated with image: ${response.data.data?.images?.length || 0} images`);
      
      return true;
    } catch (error) {
      console.log('❌ Image upload test failed:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Property Edit Improvements Test Suite\n');
    
    // Login
    const loginSuccess = await this.login();
    if (!loginSuccess) return;

    // Get test property
    const property = await this.getMyProperties();
    if (!property) {
      console.log('❌ No properties found for testing');
      return;
    }

    // Run tests
    const editTestSuccess = await this.testPropertyEdit();
    const imageTestSuccess = await this.testImageUpload();

    // Summary
    console.log('\n📊 Test Results Summary:');
    console.log(`   - Property Edit: ${editTestSuccess ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   - Image Upload: ${imageTestSuccess ? '✅ PASS' : '❌ FAIL'}`);
    
    if (editTestSuccess && imageTestSuccess) {
      console.log('\n🎉 All tests passed! Property edit improvements are working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check the implementation.');
    }
  }
}

// Run the tests
const tester = new PropertyEditTester();
tester.runAllTests().catch(console.error);
