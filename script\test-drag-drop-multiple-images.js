const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

const API_BASE_URL = 'http://localhost:1337/api';

// Test user credentials
const testUser = {
  identifier: '<EMAIL>',
  password: 'TestPassword123!'
};

class DragDropMultipleImagesTester {
  constructor() {
    this.token = null;
    this.user = null;
    this.createdPropertyId = null;
  }

  async login() {
    console.log('🔐 Logging in...');
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/local`, testUser);
      
      this.token = response.data.jwt;
      this.user = response.data.user;
      console.log('✅ Login successful');
      return true;
    } catch (error) {
      console.log('❌ Login failed:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async createMultipleTestImages() {
    console.log('\n📸 Creating multiple test images...');
    
    const testImages = [];
    const baseImagePath = path.join(__dirname, 'image.jpg');
    
    if (!fs.existsSync(baseImagePath)) {
      console.log('❌ Base image file not found at:', baseImagePath);
      return [];
    }

    // Create multiple copies of the test image with different names
    for (let i = 1; i <= 3; i++) {
      const newImagePath = path.join(__dirname, `test-image-${i}.jpg`);
      try {
        fs.copyFileSync(baseImagePath, newImagePath);
        testImages.push(newImagePath);
        console.log(`✅ Created test image ${i}: test-image-${i}.jpg`);
      } catch (error) {
        console.log(`❌ Failed to create test image ${i}:`, error.message);
      }
    }

    return testImages;
  }

  async createFloorPlanImage() {
    console.log('\n🏗️  Creating floor plan test image...');
    
    const baseImagePath = path.join(__dirname, 'image.jpg');
    const floorPlanPath = path.join(__dirname, 'floor-plan.jpg');
    
    if (!fs.existsSync(baseImagePath)) {
      console.log('❌ Base image file not found for floor plan');
      return null;
    }

    try {
      fs.copyFileSync(baseImagePath, floorPlanPath);
      console.log('✅ Created floor plan test image: floor-plan.jpg');
      return floorPlanPath;
    } catch (error) {
      console.log('❌ Failed to create floor plan image:', error.message);
      return null;
    }
  }

  async testPropertyCreationWithMultipleImages() {
    console.log('\n🏠 Testing property creation with multiple images...');
    
    const testImages = await this.createMultipleTestImages();
    const floorPlanPath = await this.createFloorPlanImage();
    
    if (testImages.length === 0) {
      console.log('❌ No test images available');
      return null;
    }

    try {
      const formData = new FormData();
      
      // Property data
      const propertyData = {
        title: 'Multi-Image Drag & Drop Test Property',
        description: 'Testing multiple image upload and floor plan functionality',
        price: 850000,
        currency: 'USD',
        propertyType: 'villa',
        offer: 'for-sale',
        bedrooms: 3,
        bathrooms: 3,
        area: 200,
        areaUnit: 'sqm',
        address: '456 Multi Image Street',
        city: 'Test City',
        country: 'Test Country',
        neighborhood: ['Premium Area'],
        coordinates: { lat: 25.2048, lng: 55.2708 },
        features: ['pool', 'garden', 'garage'],
        isLuxury: true,
        furnished: false,
        petFriendly: true
      };

      formData.append('data', JSON.stringify(propertyData));
      
      // Add multiple images
      testImages.forEach((imagePath, index) => {
        formData.append('files.images', fs.createReadStream(imagePath));
        console.log(`📎 Added image ${index + 1}: ${path.basename(imagePath)}`);
      });

      // Add floor plan
      if (floorPlanPath) {
        formData.append('files.floorPlan', fs.createReadStream(floorPlanPath));
        console.log('📎 Added floor plan: floor-plan.jpg');
      }

      const response = await axios.post(`${API_BASE_URL}/properties`, formData, {
        headers: {
          'Authorization': `Bearer ${this.token}`,
          ...formData.getHeaders()
        }
      });

      this.createdPropertyId = response.data.data.id;
      console.log('✅ Property created successfully with ID:', this.createdPropertyId);
      console.log(`📸 Expected images: ${testImages.length}`);
      console.log(`🏗️  Expected floor plan: ${floorPlanPath ? 'Yes' : 'No'}`);
      
      return response.data.data;
    } catch (error) {
      console.log('❌ Property creation failed:', error.response?.data?.error?.message || error.message);
      if (error.response?.data?.error?.details) {
        console.log('Error details:', JSON.stringify(error.response.data.error.details, null, 2));
      }
      return null;
    }
  }

  async verifyMultipleImages() {
    if (!this.createdPropertyId) {
      console.log('❌ No property ID to verify');
      return false;
    }

    console.log('\n🔍 Verifying multiple images and floor plan...');
    
    try {
      const response = await axios.get(`${API_BASE_URL}/properties/${this.createdPropertyId}`, {
        headers: { 'Authorization': `Bearer ${this.token}` },
        params: { populate: ['images', 'floorPlan'] }
      });

      const property = response.data.data;
      console.log(`✅ Property retrieved: ${property.title}`);
      console.log(`📸 Images found: ${property.images?.length || 0}`);
      console.log(`🏗️  Floor plan found: ${property.floorPlan ? 'Yes' : 'No'}`);
      
      if (property.images && property.images.length > 0) {
        console.log('\n📸 Image Details:');
        property.images.forEach((image, index) => {
          console.log(`   Image ${index + 1}:`);
          console.log(`     ID: ${image.id}`);
          console.log(`     Name: ${image.name}`);
          console.log(`     URL: ${image.url}`);
          console.log(`     Size: ${(image.size / 1024).toFixed(2)} KB`);
        });
      }
      
      if (property.floorPlan) {
        console.log('\n🏗️  Floor Plan Details:');
        console.log(`     ID: ${property.floorPlan.id}`);
        console.log(`     Name: ${property.floorPlan.name}`);
        console.log(`     URL: ${property.floorPlan.url}`);
        console.log(`     Size: ${(property.floorPlan.size / 1024).toFixed(2)} KB`);
      }
      
      return {
        imagesCount: property.images?.length || 0,
        hasFloorPlan: !!property.floorPlan,
        property: property
      };
    } catch (error) {
      console.log('❌ Failed to verify images:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async testEditWithAdditionalImages() {
    if (!this.createdPropertyId) {
      console.log('❌ No property ID to test edit');
      return false;
    }

    console.log('\n✏️  Testing edit with additional images...');
    
    try {
      // Create additional test images
      const additionalImages = [];
      const baseImagePath = path.join(__dirname, 'image.jpg');
      
      for (let i = 6; i <= 8; i++) {
        const newImagePath = path.join(__dirname, `additional-image-${i}.jpg`);
        fs.copyFileSync(baseImagePath, newImagePath);
        additionalImages.push(newImagePath);
      }

      const formData = new FormData();
      
      // Updated property data
      const propertyData = {
        title: 'Multi-Image Test Property - Updated with More Images',
        description: 'Updated with additional images via edit form'
      };

      formData.append('data', JSON.stringify(propertyData));
      
      // Add additional images
      additionalImages.forEach((imagePath, index) => {
        formData.append('files.images', fs.createReadStream(imagePath));
        console.log(`📎 Adding additional image ${index + 1}: ${path.basename(imagePath)}`);
      });

      const response = await axios.put(`${API_BASE_URL}/properties/${this.createdPropertyId}`, formData, {
        headers: {
          'Authorization': `Bearer ${this.token}`,
          ...formData.getHeaders()
        }
      });

      console.log('✅ Property updated with additional images');
      
      // Verify the update
      const verifyResponse = await axios.get(`${API_BASE_URL}/properties/${this.createdPropertyId}`, {
        headers: { 'Authorization': `Bearer ${this.token}` },
        params: { populate: ['images', 'floorPlan'] }
      });

      const updatedProperty = verifyResponse.data.data;
      console.log(`📸 Total images after update: ${updatedProperty.images?.length || 0}`);
      
      return true;
    } catch (error) {
      console.log('❌ Edit with additional images failed:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async testEditPageImageDisplay() {
    if (!this.createdPropertyId) {
      console.log('❌ No property ID to test edit display');
      return false;
    }

    console.log('\n🖼️  Testing edit page image display...');
    
    try {
      const response = await axios.get(`${API_BASE_URL}/properties/${this.createdPropertyId}/edit`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      const property = response.data.data;
      console.log(`✅ Edit page data loaded: ${property.title}`);
      console.log(`📸 Images available for edit display: ${property.images?.length || 0}`);
      console.log(`🏗️  Floor plan available for edit: ${property.floorPlan ? 'Yes' : 'No'}`);
      
      if (property.images && property.images.length > 0) {
        console.log('\n🖼️  Frontend edit form should display these existing images:');
        property.images.forEach((image, index) => {
          console.log(`     ${index + 1}. ${image.name} (${image.url})`);
        });
      }
      
      if (property.floorPlan) {
        console.log(`\n🏗️  Frontend should display floor plan: ${property.floorPlan.name} (${property.floorPlan.url})`);
      }
      
      return true;
    } catch (error) {
      console.log('❌ Failed to load edit page data:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test files...');
    
    const testFiles = [
      'test-image-1.jpg', 'test-image-2.jpg', 'test-image-3.jpg', 'test-image-4.jpg', 'test-image-5.jpg',
      'additional-image-6.jpg', 'additional-image-7.jpg', 'additional-image-8.jpg',
      'floor-plan.jpg'
    ];

    testFiles.forEach(filename => {
      const filePath = path.join(__dirname, filename);
      if (fs.existsSync(filePath)) {
        try {
          fs.unlinkSync(filePath);
          console.log(`✅ Deleted: ${filename}`);
        } catch (error) {
          console.log(`❌ Failed to delete ${filename}:`, error.message);
        }
      }
    });
  }

  async runCompleteTest() {
    console.log('🧪 Starting Drag & Drop Multiple Images Test\n');
    
    try {
      // Test 1: Login
      const loginSuccess = await this.login();
      if (!loginSuccess) return;

      // Test 2: Create property with multiple images and floor plan
      const property = await this.testPropertyCreationWithMultipleImages();
      if (!property) return;

      // Test 3: Verify multiple images and floor plan
      const verification = await this.verifyMultipleImages();
      
      // Test 4: Test edit with additional images
      const editSuccess = await this.testEditWithAdditionalImages();
      
      // Test 5: Test edit page image display
      const editDisplaySuccess = await this.testEditPageImageDisplay();
      
      console.log('\n🎯 Drag & Drop Multiple Images Test Results:');
      console.log(`✅ Property Creation: ${property ? 'SUCCESS' : 'FAILED'}`);
      console.log(`✅ Multiple Images: ${verification && verification.imagesCount > 1 ? `SUCCESS (${verification.imagesCount} images)` : 'FAILED'}`);
      console.log(`✅ Floor Plan Upload: ${verification && verification.hasFloorPlan ? 'SUCCESS' : 'FAILED'}`);
      console.log(`✅ Edit Additional Images: ${editSuccess ? 'SUCCESS' : 'FAILED'}`);
      console.log(`✅ Edit Display: ${editDisplaySuccess ? 'SUCCESS' : 'FAILED'}`);
      
      if (property && verification && editSuccess && editDisplaySuccess) {
        console.log('\n🎉 ALL TESTS PASSED! Drag & drop and multiple image functionality is working!');
        console.log('\n📋 What should work in the frontend:');
        console.log('   ✅ Drag & drop multiple images in create form');
        console.log('   ✅ Drag & drop multiple images in edit form');
        console.log('   ✅ Floor plan upload in both forms');
        console.log('   ✅ Display existing images in edit form');
        console.log('   ✅ Image reordering (drag to reorder)');
        console.log('   ✅ Image removal (X button)');
        console.log('   ✅ Primary image indicator (first image)');
        console.log(`\n🔗 Test the frontend at:`);
        console.log(`   - Create Property: http://localhost:3001/dashboard/properties/create`);
        console.log(`   - Edit Property: http://localhost:3001/dashboard/properties/${this.createdPropertyId}/edit`);
        console.log(`   - View Property: http://localhost:3001/properties/${this.createdPropertyId}`);
      } else {
        console.log('\n❌ Some tests failed. Check the logs above for details.');
      }
    } finally {
      await this.cleanup();
    }
  }
}

// Run the complete test
const tester = new DragDropMultipleImagesTester();
tester.runCompleteTest().catch(console.error);
