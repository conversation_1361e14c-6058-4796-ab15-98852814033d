const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

const API_URL = 'http://localhost:1337/api';

// Property generation configuration
const TOTAL_PROPERTIES = 40;
const BATCH_SIZE = 20; // Smaller batches for better progress tracking
const DELAY_BETWEEN_PROPERTIES = 500; // Reduced to 0.1 seconds (5x faster)
const MAX_RETRIES = 3; // Reduced retries for speed
const CONCURRENT_PROPERTIES = 10; // Process 5 properties simultaneously

// Sharm El Sheikh neighborhoods with coordinates
const neighborhoods = [
  { name: 'Naama Bay', lat: 27.9158, lng: 34.3300 },
  { name: 'Had<PERSON>', lat: 27.8947, lng: 34.3289 },
  { name: 'Sharks Bay', lat: 27.8756, lng: 34.3445 },
  { name: 'Old Market', lat: 27.9067, lng: 34.3200 },
  { name: '<PERSON><PERSON><PERSON>', lat: 27.8834, lng: 34.3156 },
  { name: 'Nab<PERSON>', lat: 27.9789, lng: 34.3567 },
  { name: '<PERSON><PERSON>', lat: 27.7334, lng: 34.2667 },
  { name: 'Soho Square', lat: 27.9123, lng: 34.3278 },
  { name: 'Hay El Nour', lat: 27.8945, lng: 34.3089 },
  { name: 'Sharm Dreams', lat: 27.8567, lng: 34.3445 },
  { name: 'Four Seasons', lat: 27.8634, lng: 34.3512 },
  { name: 'Coral Bay', lat: 27.8445, lng: 34.3623 },
  { name: 'City Center', lat: 27.9089, lng: 34.3178 },
  { name: 'Peace Road', lat: 27.9012, lng: 34.3145 },
  { name: 'El Fanar', lat: 27.8723, lng: 34.3234 },
  { name: 'Terrazzina Beach', lat: 27.8912, lng: 34.3456 },
  { name: 'Ras Nasrani', lat: 27.9834, lng: 34.3678 },
  { name: 'Tower Bay', lat: 27.8567, lng: 34.3234 }
];

// Property types with realistic price ranges (USD)
const propertyTypes = {
  studio: {
    minPrice: 80000, maxPrice: 250000,
    minArea: 25, maxArea: 60,
    bedrooms: [0, 1], bathrooms: [1],
    rentMultiplier: 0.008
  },
  apartment: {
    minPrice: 150000, maxPrice: 800000,
    minArea: 60, maxArea: 200,
    bedrooms: [1, 2, 3, 4], bathrooms: [1, 2, 3],
    rentMultiplier: 0.007
  },
  villa: {
    minPrice: 400000, maxPrice: 3000000,
    minArea: 200, maxArea: 600,
    bedrooms: [3, 4, 5, 6], bathrooms: [2, 3, 4, 5],
    rentMultiplier: 0.006
  },
  penthouse: {
    minPrice: 800000, maxPrice: 5000000,
    minArea: 150, maxArea: 400,
    bedrooms: [2, 3, 4, 5], bathrooms: [2, 3, 4],
    rentMultiplier: 0.005
  },
  townhouse: {
    minPrice: 300000, maxPrice: 1200000,
    minArea: 120, maxArea: 300,
    bedrooms: [2, 3, 4], bathrooms: [2, 3],
    rentMultiplier: 0.007
  },
  duplex: {
    minPrice: 350000, maxPrice: 1500000,
    minArea: 140, maxArea: 350,
    bedrooms: [2, 3, 4, 5], bathrooms: [2, 3, 4],
    rentMultiplier: 0.006
  },
  commercial: {
    minPrice: 200000, maxPrice: 2000000,
    minArea: 50, maxArea: 500,
    bedrooms: [0], bathrooms: [1, 2, 3],
    rentMultiplier: 0.01
  },
  land: {
    minPrice: 100000, maxPrice: 5000000,
    minArea: 200, maxArea: 5000,
    bedrooms: [0], bathrooms: [0],
    rentMultiplier: 0.002
  }
};

// Available features and amenities
const availableFeatures = [
  'pool', 'gym', 'garden', 'balcony', 'terrace', 'garage', 'security',
  'elevator', 'wifi', 'view', 'concierge', 'shopping', 'cafe', 'parking',
  'air-conditioning', 'heating', 'fireplace', 'storage', 'laundry'
];

// Property title templates
const titleTemplates = {
  studio: ['Cozy Studio', 'Modern Studio', 'Compact Studio', 'Stylish Studio', 'Bright Studio'],
  apartment: ['Modern Apartment', 'Luxury Apartment', 'Spacious Apartment', 'Contemporary Apartment', 'Elegant Apartment'],
  villa: ['Luxury Villa', 'Beachfront Villa', 'Private Villa', 'Stunning Villa', 'Executive Villa'],
  penthouse: ['Luxury Penthouse', 'Sky Penthouse', 'Premium Penthouse', 'Executive Penthouse', 'Exclusive Penthouse'],
  townhouse: ['Family Townhouse', 'Spacious Townhouse', 'Modern Townhouse', 'Comfortable Townhouse', 'Elegant Townhouse'],
  duplex: ['Luxury Duplex', 'Spacious Duplex', 'Modern Duplex', 'Family Duplex', 'Executive Duplex'],
  commercial: ['Commercial Space', 'Office Space', 'Retail Space', 'Business Center', 'Commercial Unit'],
  land: ['Land Plot', 'Development Land', 'Investment Land', 'Beachfront Land', 'Prime Land']
};

// Description templates
const descriptionTemplates = {
  studio: [
    'Perfect for young professionals or investors. Modern design with efficient use of space.',
    'Ideal starter home with contemporary finishes and great location.',
    'Compact yet comfortable living space with modern amenities.',
    'Smart design maximizes every square meter for comfortable living.'
  ],
  apartment: [
    'Beautiful apartment featuring modern amenities and excellent location.',
    'Spacious living with contemporary design and premium finishes.',
    'Perfect for families or professionals seeking comfort and convenience.',
    'Well-designed space with natural light and modern features.'
  ],
  villa: [
    'Stunning villa offering luxury living with private outdoor space.',
    'Exceptional property featuring premium finishes and exclusive amenities.',
    'Perfect for those seeking privacy and luxury in a prime location.',
    'Magnificent villa with spacious rooms and beautiful outdoor areas.'
  ],
  penthouse: [
    'Exclusive penthouse with panoramic views and luxury amenities.',
    'The pinnacle of luxury living with premium finishes throughout.',
    'Sophisticated living space with breathtaking views and modern design.',
    'Ultimate luxury with private terraces and world-class amenities.'
  ],
  townhouse: [
    'Family-friendly townhouse with private garden and modern amenities.',
    'Perfect for families seeking space and community living.',
    'Comfortable family home with excellent layout and outdoor space.',
    'Ideal for growing families with spacious rooms and garden.'
  ],
  duplex: [
    'Unique duplex offering spacious living across two levels.',
    'Modern duplex with excellent layout and premium finishes.',
    'Perfect for those seeking extra space and contemporary design.',
    'Spacious two-level living with modern amenities and style.'
  ],
  commercial: [
    'Prime commercial space perfect for business operations.',
    'Excellent location for retail or office use with high visibility.',
    'Professional space ideal for growing businesses.',
    'Strategic location with excellent foot traffic and accessibility.'
  ],
  land: [
    'Prime development opportunity in excellent location.',
    'Perfect for residential or commercial development.',
    'Rare opportunity for custom development in sought-after area.',
    'Excellent investment potential with development possibilities.'
  ]
};

// Utility functions for generating random property data
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getRandomPrice(min, max) {
  // Generate price in increments of 5000 for realism
  const increment = 5000;
  const minIncrement = Math.ceil(min / increment);
  const maxIncrement = Math.floor(max / increment);
  return getRandomNumber(minIncrement, maxIncrement) * increment;
}

function generatePropertyCode(index) {
  return `SHARM-${Date.now()}-${String(index).padStart(4, '0')}`;
}

function generateRandomFeatures() {
  const numFeatures = getRandomNumber(2, 6);
  const shuffled = [...availableFeatures].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, numFeatures);
}

// Generate a single property
function generateProperty(index) {
  const propertyTypeKey = getRandomElement(Object.keys(propertyTypes));
  const propertyConfig = propertyTypes[propertyTypeKey];
  const neighborhood = getRandomElement(neighborhoods);
  const isForRent = Math.random() < 0.3; // 30% chance for rental

  // Generate price
  const basePrice = getRandomPrice(propertyConfig.minPrice, propertyConfig.maxPrice);
  const price = isForRent ? Math.round(basePrice * propertyConfig.rentMultiplier) : basePrice;

  // Generate bedrooms and bathrooms
  const bedrooms = getRandomElement(propertyConfig.bedrooms);
  const bathrooms = getRandomElement(propertyConfig.bathrooms);

  // Generate area
  const area = getRandomNumber(propertyConfig.minArea, propertyConfig.maxArea);

  // Generate title and description
  const titleTemplate = getRandomElement(titleTemplates[propertyTypeKey]);
  const title = `${titleTemplate} in ${neighborhood.name}`;
  const description = getRandomElement(descriptionTemplates[propertyTypeKey]) +
    ` Located in the prestigious ${neighborhood.name} area of Sharm El Sheikh. ` +
    (isForRent ? 'Available for long-term rental.' : 'Excellent investment opportunity.');

  // Generate address
  const streetNumber = getRandomNumber(1, 999);
  const streetNames = ['Street', 'Avenue', 'Road', 'Boulevard', 'Lane', 'Drive'];
  const address = `${streetNumber} ${neighborhood.name} ${getRandomElement(streetNames)}`;

  // Add some coordinate variation
  const lat = neighborhood.lat + (Math.random() - 0.5) * 0.01;
  const lng = neighborhood.lng + (Math.random() - 0.5) * 0.01;

  return {
    title,
    description,
    price,
    propertyType: propertyTypeKey,
    offer: isForRent ? 'for-rent' : 'for-sale',
    bedrooms,
    bathrooms,
    area,
    address,
    neighborhood: neighborhood.name,
    features: generateRandomFeatures(),
    yearBuilt: propertyTypeKey === 'land' ? null : getRandomNumber(2010, 2024),
    parking: propertyTypeKey === 'land' ? 0 : getRandomNumber(0, 4),
    furnished: Math.random() < 0.4, // 40% chance
    petFriendly: Math.random() < 0.6, // 60% chance
    isLuxury: price > (isForRent ? 3000 : 800000), // Luxury threshold
    coordinates: { lat, lng }
  };
}


// Get all available images from the local images folder
function getAvailableImages() {
  const imagesDir = path.join(__dirname, 'images');

  if (!fs.existsSync(imagesDir)) {
    console.log('⚠️  Images folder not found. Creating sample images...');
    // Create the folder and some sample images
    fs.mkdirSync(imagesDir);
    return [];
  }

  const imageFiles = fs.readdirSync(imagesDir)
    .filter(file => /\.(jpg|jpeg|png|webp)$/i.test(file))
    .map(file => path.join(imagesDir, file));

  return imageFiles;
}

// Function to get random images for a property
function getRandomImagesForProperty(propertyType, numImages) {
  const availableImages = getAvailableImages();

  if (availableImages.length === 0) {
    console.log('⚠️  No images found in ./images/ folder');
    return [];
  }

  // Try to get images that match the property type first
  const matchingImages = availableImages.filter(imagePath => {
    const filename = path.basename(imagePath).toLowerCase();
    return filename.includes(propertyType.toLowerCase()) ||
           filename.includes('luxury') ||
           filename.includes('modern') ||
           filename.includes('beachfront');
  });

  // If we have matching images, use them, otherwise use any available images
  const imagesToUse = matchingImages.length > 0 ? matchingImages : availableImages;

  // Randomly select the requested number of images
  const selectedImages = [];
  for (let i = 0; i < numImages && i < imagesToUse.length; i++) {
    const randomIndex = Math.floor(Math.random() * imagesToUse.length);
    const selectedImage = imagesToUse[randomIndex];

    // Avoid duplicates
    if (!selectedImages.includes(selectedImage)) {
      selectedImages.push(selectedImage);
    }
  }

  return selectedImages;
}

// Function to create property (optimized for speed)
async function createProperty(propertyData, token, propertyIndex) {
  try {
    // Reduced console output for speed - only show every 10th property
    if (propertyIndex % 10 === 0 || propertyIndex < 5) {
      console.log(`\n🏠 Creating property ${propertyIndex + 1}: ${propertyData.title}`);
    }

    // Prepare property data
    const formattedPropertyData = {
      title: propertyData.title,
      description: propertyData.description,
      price: propertyData.price,
      currency: 'USD',
      propertyType: propertyData.propertyType,
      offer: propertyData.offer,
      bedrooms: propertyData.bedrooms,
      bathrooms: propertyData.bathrooms,
      area: propertyData.area,
      areaUnit: 'sqm',
      address: propertyData.address,
      city: 'Sharm El Sheikh',
      country: 'Egypt',
      neighborhood: propertyData.neighborhood,
      coordinates: propertyData.coordinates,
      propertyCode: `SHARM-${Date.now()}-${propertyIndex + 1}`,
      isLuxury: propertyData.isLuxury,
      features: propertyData.features,
      ...(propertyData.yearBuilt && { yearBuilt: propertyData.yearBuilt }),
      ...(propertyData.parking && { parking: propertyData.parking }),
      furnished: propertyData.furnished,
      petFriendly: propertyData.petFriendly,
      views: 0,
      // Publish some properties randomly
      ...(Math.random() > 0.3 && { publishedAt: new Date().toISOString() })
    };

    // Create property first without images
    const response = await axios.post(`${API_URL}/properties`, {
      data: formattedPropertyData
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const propertyId = response.data.data.id;
    if (propertyIndex % 10 === 0 || propertyIndex < 5) {
      console.log(`   ✅ Property created! ID: ${propertyId}`);
    }

    // Now add images to the property (optimized for speed)
    const numImages = Math.floor(Math.random() * 2) + 2; // 2-3 images
    const selectedImages = getRandomImagesForProperty(propertyData.propertyType, numImages);

    if (selectedImages.length > 0) {
      // Upload all images concurrently for speed
      const imageUploadPromises = selectedImages.map(async (imagePath, i) => {
        try {
          const imageFormData = new FormData();
          imageFormData.append('files.images', fs.createReadStream(imagePath));

          await axios.put(`${API_URL}/properties/${propertyId}`, imageFormData, {
            headers: {
              'Authorization': `Bearer ${token}`,
              ...imageFormData.getHeaders()
            }
          });

          return path.basename(imagePath);
        } catch (error) {
          console.log(`   ⚠️  Failed to upload image ${i + 1}`);
          return null;
        }
      });

      const uploadedImages = await Promise.all(imageUploadPromises);
      const successfulUploads = uploadedImages.filter(img => img !== null);
      if (propertyIndex % 10 === 0 || propertyIndex < 5) {
        console.log(`   📸 Added ${successfulUploads.length}/${selectedImages.length} images`);
      }
    } else if (propertyIndex % 10 === 0 || propertyIndex < 5) {
      console.log('   ⚠️  No images available');
    }

    if (propertyIndex % 10 === 0 || propertyIndex < 5) {
      console.log(`   📍 ${propertyData.neighborhood} | 💰 $${propertyData.price.toLocaleString()} | 🏠 ${propertyData.propertyType}`);
    }

    return response.data.data;
  } catch (error) {
    console.error(`   ❌ Error creating property: ${error.response?.data?.error?.message || error.message}`);
    if (error.response?.data?.error?.details) {
      console.error('   📋 Error details:', JSON.stringify(error.response.data.error.details, null, 2));
    }
    return null;
  }
}

// Enhanced property creation with retry logic
async function createPropertyWithRetry(propertyData, token, index, maxRetries = MAX_RETRIES) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await createProperty(propertyData, token, index);
    } catch (error) {
      console.log(`   ⚠️  Attempt ${attempt}/${maxRetries} failed: ${error.message}`);
      if (attempt === maxRetries) {
        console.log(`   ❌ Property ${index + 1} failed after ${maxRetries} attempts`);
        return null;
      }
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
}

// Main function to create all sample properties
async function createSharmElSheikhProperties() {
  console.log('🏖️  Creating 1000 Sample Real Estate Properties in Sharm El Sheikh\n');
  console.log('📍 Location: Sharm El Sheikh, Egypt');
  console.log('👤 User: <EMAIL>');
  console.log('🖼️  Images: 2-3 real estate images per property from local folder');
  console.log(`⚡ SPEED OPTIMIZATIONS ENABLED:`);
  console.log(`   • ${CONCURRENT_PROPERTIES} concurrent properties`);
  console.log(`   • ${DELAY_BETWEEN_PROPERTIES}ms delay (5x faster)`);
  console.log(`   • Concurrent image uploads`);
  console.log(`   • Reduced console output`);
  console.log(`   • Estimated time: ~3-4 minutes (vs 8-10 minutes)\n`);

  try {
    // Step 1: Login with testuser
    console.log('🔐 Logging in as testuser...');
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });

    const token = loginResponse.data.jwt;
    const user = loginResponse.data.user;
    console.log('✅ Login successful');
    console.log(`   User: ${user.username} (${user.email})`);
    console.log(`   User ID: ${user.id}\n`);

    // Step 2: Check available images
    const availableImages = getAvailableImages();
    console.log(`🖼️  Found ${availableImages.length} images in ./images/ folder`);
    if (availableImages.length === 0) {
      console.log('⚠️  No images found! Run: node download-real-images.js first');
      return;
    }

    // Step 3: Create properties with concurrent processing for speed
    console.log(`\n🏗️  Starting creation of ${TOTAL_PROPERTIES} properties...\n`);
    console.log(`⚡ Optimizations: ${CONCURRENT_PROPERTIES} concurrent, ${DELAY_BETWEEN_PROPERTIES}ms delay, concurrent image uploads\n`);

    const createdProperties = [];
    const failedProperties = [];
    const propertyTypeStats = {};
    const neighborhoodStats = {};
    const startTime = Date.now();

    // Process properties in concurrent batches
    for (let batchStart = 0; batchStart < TOTAL_PROPERTIES; batchStart += CONCURRENT_PROPERTIES) {
      const batchEnd = Math.min(batchStart + CONCURRENT_PROPERTIES, TOTAL_PROPERTIES);
      const batchPromises = [];

      // Create concurrent promises for this batch
      for (let i = batchStart; i < batchEnd; i++) {
        const propertyData = generateProperty(i);

        // Update stats
        propertyTypeStats[propertyData.propertyType] = (propertyTypeStats[propertyData.propertyType] || 0) + 1;
        neighborhoodStats[propertyData.neighborhood] = (neighborhoodStats[propertyData.neighborhood] || 0) + 1;

        // Add to batch
        batchPromises.push(
          createPropertyWithRetry(propertyData, token, i).then(property => ({
            property,
            index: i,
            data: propertyData
          }))
        );
      }

      // Wait for all properties in this batch to complete
      const batchResults = await Promise.all(batchPromises);

      // Process results
      batchResults.forEach(result => {
        if (result.property) {
          createdProperties.push(result.property);
        } else {
          failedProperties.push({ index: result.index, data: result.data });
        }
      });

      // Progress indicator
      const progress = ((batchEnd) / TOTAL_PROPERTIES * 100).toFixed(1);
      const elapsed = ((Date.now() - startTime) / 1000 / 60).toFixed(1);
      const estimated = (elapsed / batchEnd * TOTAL_PROPERTIES).toFixed(1);
      const rate = (batchEnd / elapsed).toFixed(1);

      console.log(`\n🎯 Batch ${Math.ceil(batchEnd / CONCURRENT_PROPERTIES)}/${Math.ceil(TOTAL_PROPERTIES / CONCURRENT_PROPERTIES)} completed: ${batchEnd}/${TOTAL_PROPERTIES} (${progress}%)`);
      console.log(`   ⚡ Rate: ${rate} properties/min | ⏱️  Elapsed: ${elapsed} min | ETA: ${estimated} min`);
      console.log(`   ✅ Success: ${createdProperties.length} | ❌ Failed: ${failedProperties.length}`);

      // Small delay between batches to avoid overwhelming the server
      if (batchEnd < TOTAL_PROPERTIES) {
        await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_PROPERTIES));
      }
    }

    // Step 4: Final verification
    console.log('\n🔍 Verifying created properties...');
    const myPropertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const totalUserProperties = myPropertiesResponse.data.data?.length || 0;
    const publishedProperties = myPropertiesResponse.data.data?.filter(p => p.publishedAt) || [];
    const draftProperties = myPropertiesResponse.data.data?.filter(p => !p.publishedAt) || [];

    // Step 5: Display comprehensive summary
    const totalTime = ((Date.now() - startTime) / 1000 / 60).toFixed(1);

    console.log('\n🎉 CREATION COMPLETED!\n');
    console.log('� SUMMARY STATISTICS:');
    console.log('═'.repeat(50));
    console.log(`⏱️  Total time: ${totalTime} minutes`);
    console.log(`🎯 Target properties: ${TOTAL_PROPERTIES}`);
    console.log(`✅ Successfully created: ${createdProperties.length}`);
    console.log(`❌ Failed to create: ${failedProperties.length}`);
    console.log(`📈 Success rate: ${(createdProperties.length / TOTAL_PROPERTIES * 100).toFixed(1)}%`);
    console.log(`📊 Total user properties: ${totalUserProperties}`);
    console.log(`📢 Published: ${publishedProperties.length}`);
    console.log(`📝 Drafts: ${draftProperties.length}`);

    // Property type breakdown
    console.log('\n🏠 PROPERTY TYPES:');
    console.log('─'.repeat(30));
    Object.entries(propertyTypeStats).forEach(([type, count]) => {
      const percentage = (count / TOTAL_PROPERTIES * 100).toFixed(1);
      console.log(`   ${type.padEnd(12)}: ${count.toString().padStart(3)} (${percentage}%)`);
    });

    // Neighborhood breakdown
    console.log('\n📍 NEIGHBORHOODS:');
    console.log('─'.repeat(30));
    Object.entries(neighborhoodStats).forEach(([neighborhood, count]) => {
      const percentage = (count / TOTAL_PROPERTIES * 100).toFixed(1);
      console.log(`   ${neighborhood.padEnd(18)}: ${count.toString().padStart(3)} (${percentage}%)`);
    });

    // Error summary
    if (failedProperties.length > 0) {
      console.log('\n❌ FAILED PROPERTIES:');
      console.log('─'.repeat(30));
      failedProperties.slice(0, 10).forEach(failed => {
        console.log(`   Property ${failed.index + 1}: ${failed.data.title}`);
      });
      if (failedProperties.length > 10) {
        console.log(`   ... and ${failedProperties.length - 10} more`);
      }
    }

    console.log('\n📝 NEXT STEPS:');
    console.log('─'.repeat(30));
    console.log('   1. Go to: http://localhost:3000/auth/login');
    console.log('   2. Login with: <EMAIL> / TestPassword123!');
    console.log('   3. Navigate to: http://localhost:3000/dashboard');
    console.log('   4. Browse properties at: http://localhost:3000/properties');
    console.log('   5. Filter by location: Sharm El Sheikh, Egypt');
    console.log('   6. Test search, filters, and pagination with large dataset');

  } catch (error) {
    console.error('❌ Critical error in main process:', error.response?.data?.error?.message || error.message);
    if (error.response?.data?.error?.details) {
      console.error('   Details:', error.response.data.error.details);
    }
  }
}

// Run the script
createSharmElSheikhProperties().catch(console.error);
