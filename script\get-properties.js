const axios = require('axios');

async function getProperties() {
  try {
    const response = await axios.get('http://localhost:1337/api/properties?pagination[limit]=5&populate=*');
    console.log('Properties found:');
    
    if (response.data.data && response.data.data.length > 0) {
      response.data.data.forEach((property, index) => {
        console.log(`${index + 1}. ${property.title}`);
        console.log(`   ID: ${property.id}`);
        console.log(`   DocumentID: ${property.documentId}`);
        console.log(`   Slug: ${property.slug || 'No slug'}`);
        console.log(`   Coordinates: ${property.coordinates ? `${property.coordinates.lat}, ${property.coordinates.lng}` : 'No coordinates'}`);
        console.log(`   URL: http://localhost:3000/properties/${property.slug || property.documentId}`);
        console.log('');
      });
    } else {
      console.log('No properties found');
    }
  } catch (error) {
    console.error('Error:', error.message);
  }
}

getProperties();
