/**
 * message router
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreRouter('api::message.message');

// Custom routes
export const customRoutes = {
  routes: [
    {
      method: 'GET',
      path: '/messages/inbox',
      handler: 'message.inbox',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/messages/sent',
      handler: 'message.sent',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'PUT',
      path: '/messages/:id/mark-as-read',
      handler: 'message.markAsRead',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
