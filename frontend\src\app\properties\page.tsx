'use client';

import React, { useState, useEffect, Suspense, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { propertiesAPI } from '@/lib/api';
import {
  Search, Filter, MapPin, Bed, Bath, Square, Eye, Star, X, Tag,
  SlidersHorizontal, ChevronDown, ChevronUp, Settings, Grid3X3,
  List, Save, Bell, RotateCcw, Home, Car, Calendar, Sparkles
} from 'lucide-react';
import { PropertyTypeFilter } from '@/components/Filters/PropertyTypeFilter';
import { PriceRangeFilter } from '@/components/Filters/PriceRangeFilter';
import { LocationFilter } from '@/components/Filters/LocationFilter';
import Layout from '@/components/Layout/Layout';
import { SortFilter } from '@/components/Filters/SortFilter';

interface Property {
  documentId: string;
  id: number;
  title: string;
  description: string;
  price: number;
  currency: string;
  propertyType: string;
  offer: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  areaUnit: string;
  address: string;
  city: string;
  country: string;
  neighborhood?: string;
  propertyCode?: string;
  isLuxury?: boolean;
  furnished?: boolean;
  petFriendly?: boolean;
  features?: string[];
  views: number;
  images?: Array<{
    url?: string;
    formats?: {
      small?: { url: string };
      medium?: { url: string };
    };
  }>;
  createdAt: string;
  updatedAt: string;
  slug?: string;
}

// Helper function to get proper image URL
const getImageUrl = (image: any) => {
  if (!image) return '';

  // If image has a full URL (starts with http), use it directly
  if (image.url && (image.url.startsWith('http://') || image.url.startsWith('https://'))) {
    return image.url;
  }

  // If image has formats, try to get the best available format
  if (image.formats) {
    const format = image.formats.medium || image.formats.small || image.formats.thumbnail;
    if (format && format.url) {
      if (format.url.startsWith('http://') || format.url.startsWith('https://')) {
        return format.url;
      }
      // Construct full URL with backend base URL
      return `${process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337'}${format.url}`;
    }
  }

  // Fallback to main image URL
  if (image.url) {
    if (image.url.startsWith('/')) {
      return `${process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337'}${image.url}`;
    }
    return image.url;
  }

  return '';
};

const PropertiesPageContent: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [searching, setSearching] = useState(false);
  const [error, setError] = useState('');

  // Filter state
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    propertyType: searchParams.get('propertyType')?.split(',').filter(Boolean) || [],
    offer: searchParams.get('offer') || '',
    city: searchParams.get('city') || '',
    neighborhood: searchParams.get('neighborhood') || '',
    minPrice: searchParams.get('minPrice') || '',
    maxPrice: searchParams.get('maxPrice') || '',
    bedrooms: searchParams.get('bedrooms') || '',
    bathrooms: searchParams.get('bathrooms') || '',
    parking: searchParams.get('parking') || '',
    yearBuilt: searchParams.get('yearBuilt') || '',
    isLuxury: searchParams.get('isLuxury') === 'true',
    furnished: searchParams.get('furnished') === 'true',
    petFriendly: searchParams.get('petFriendly') === 'true',
    features: searchParams.get('features')?.split(',').filter(Boolean) || [],
  });

  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isMobile, setIsMobile] = useState(false);

  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(
    (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
  );

  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 12,
    total: 0,
    pageCount: 0,
  });

  // Fetch properties function
  const fetchProperties = useCallback(async (isSearch = false) => {
    try {
      if (isSearch) {
        setSearching(true);
      } else {
        setLoading(true);
      }
      setError('');

      const searchFilters = {
        ...filters,
        page: pagination.page,
        pageSize: pagination.pageSize,
        sortBy,
        sortOrder,
      };

      console.log('Fetching properties with filters:', searchFilters);
      const response = await propertiesAPI.search(searchFilters);
      console.log('Properties API response:', response);

      setProperties(response.data || []);
      setPagination(prev => ({
        ...prev,
        total: response.meta?.pagination?.total || 0,
        pageCount: response.meta?.pagination?.pageCount || 0,
      }));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch properties';
      setError(errorMessage);
      console.error('Error fetching properties:', err);
      setProperties([]);
      setPagination(prev => ({ ...prev, total: 0, pageCount: 0 }));
    } finally {
      setLoading(false);
      setSearching(false);
    }
  }, [filters, pagination.page, pagination.pageSize, sortBy, sortOrder]);

  // Filter change handler
  const handleFilterChange = (key: string, value: string | string[] | boolean | number) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // Debounced search function
  const debouncedFetchProperties = useCallback(
    debounce(() => {
      if (!loading) {
        fetchProperties(true);
      }
    }, 500),
    [loading, fetchProperties]
  );

  // Debounced search effect for filter changes
  useEffect(() => {
    if (!loading) { // Don't trigger on initial load
      debouncedFetchProperties();
    }
  }, [filters, debouncedFetchProperties]); // Trigger when filters change

  // Debounce utility function
  function debounce(func: () => void, wait: number) {
    let timeout: NodeJS.Timeout;
    return function executedFunction() {
      const later = () => {
        clearTimeout(timeout);
        func();
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // Sort change handler
  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // Get active filters for display
  const getActiveFilters = () => {
    const active = [];

    // Basic filters
    if (filters.search) active.push({ key: 'search', label: `"${filters.search}"`, value: filters.search, category: 'Search' });
    if (filters.propertyType.length > 0) active.push({ key: 'propertyType', label: filters.propertyType.join(', '), value: filters.propertyType, category: 'Type' });
    if (filters.offer) active.push({ key: 'offer', label: filters.offer.replace('-', ' '), value: filters.offer, category: 'Offer' });

    // Location filters
    if (filters.city) active.push({ key: 'city', label: filters.city, value: filters.city, category: 'City' });
    if (filters.neighborhood) active.push({ key: 'neighborhood', label: filters.neighborhood, value: filters.neighborhood, category: 'Area' });

    // Price filters
    if (filters.minPrice) active.push({ key: 'minPrice', label: `$${parseInt(filters.minPrice).toLocaleString()}+`, value: filters.minPrice, category: 'Min Price' });
    if (filters.maxPrice) active.push({ key: 'maxPrice', label: `$${parseInt(filters.maxPrice).toLocaleString()}-`, value: filters.maxPrice, category: 'Max Price' });

    // Room requirements
    if (filters.bedrooms) active.push({ key: 'bedrooms', label: `${filters.bedrooms}+ Bed`, value: filters.bedrooms, category: 'Bedrooms' });
    if (filters.bathrooms) active.push({ key: 'bathrooms', label: `${filters.bathrooms}+ Bath`, value: filters.bathrooms, category: 'Bathrooms' });

    // Special features
    if (filters.isLuxury) active.push({ key: 'isLuxury', label: 'Luxury', value: true, category: 'Special' });
    if (filters.furnished) active.push({ key: 'furnished', label: 'Furnished', value: true, category: 'Special' });
    if (filters.petFriendly) active.push({ key: 'petFriendly', label: 'Pet Friendly', value: true, category: 'Special' });

    // Advanced filters
    if (filters.parking) active.push({ key: 'parking', label: `${filters.parking}+ Parking`, value: filters.parking, category: 'Parking' });
    if (filters.yearBuilt) active.push({ key: 'yearBuilt', label: `Built ${filters.yearBuilt}+`, value: filters.yearBuilt, category: 'Year' });
    if (filters.features.length > 0) active.push({ key: 'features', label: `${filters.features.length} Amenities`, value: filters.features, category: 'Features' });

    return active;
  };

  // Remove individual filter
  const removeFilter = (key: string) => {
    if (key === 'propertyType' || key === 'features') {
      handleFilterChange(key, []);
    } else if (key === 'isLuxury' || key === 'furnished' || key === 'petFriendly') {
      handleFilterChange(key, false);
    } else {
      handleFilterChange(key, '');
    }
  };

  // Clear filters handler
  const clearFilters = () => {
    setFilters({
      search: '',
      propertyType: [],
      offer: '',
      city: '',
      neighborhood: '',
      minPrice: '',
      maxPrice: '',
      bedrooms: '',
      bathrooms: '',
      parking: '',
      yearBuilt: '',
      isLuxury: false,
      furnished: false,
      petFriendly: false,
      features: [],
    });
    setPagination(prev => ({ ...prev, page: 1 }));
    router.push('/properties', { scroll: false });
  };



  useEffect(() => {
    fetchProperties();
  }, []);

  useEffect(() => {
    fetchProperties(true); // Use searching state for pagination
  }, [pagination.page]);

  useEffect(() => {
    fetchProperties(true); // Use searching state for sorting changes
  }, [sortBy, sortOrder]);

  // Handle responsive behavior and filter toggle state
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 1024; // lg breakpoint
      setIsMobile(mobile);

      // Close mobile filters when switching to desktop
      if (!mobile && showMobileFilters) {
        setShowMobileFilters(false);
      }
    };

    // Set initial state
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [showMobileFilters]);

  if (loading) {
    return (
      <Layout>
        <div className="bg-gray-50">
        {/* Page Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Properties</h1>
                <p className="text-gray-600 mt-2">Loading properties...</p>
              </div>
            </div>
          </div>
        </div>

        {/* Loading Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-8">
            <div className="flex flex-col items-center justify-center py-12">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mb-4"></div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Properties</h3>
              <p className="text-gray-500 text-center max-w-md">
                We're searching through our database to find the best properties for you...
              </p>
            </div>
          </div>
        </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="bg-gray-50 min-h-screen">
        {/* Page Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Properties</h1>
                <div className="flex items-center space-x-4 mt-1 sm:mt-2">
                  <p className="text-gray-600">
                    {pagination.total > 0
                      ? `${pagination.total} properties found`
                      : 'Find your perfect property'
                    }
                  </p>
                  {getActiveFilters().length > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                        {getActiveFilters().length} filter{getActiveFilters().length !== 1 ? 's' : ''} active
                      </span>
                      <button
                        onClick={clearFilters}
                        className="text-xs text-red-600 hover:text-red-700 font-medium underline"
                      >
                        Clear all
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Mobile Filter Toggle */}
              <div className="flex items-center gap-3 lg:hidden">
                <button
                  onClick={() => setShowMobileFilters(true)}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  aria-label={`Open filters${getActiveFilters().length > 0 ? ` (${getActiveFilters().length} active)` : ''}`}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                  {getActiveFilters().length > 0 && (
                    <span className="ml-2 bg-blue-500 text-white text-xs rounded-full px-2 py-0.5">
                      {getActiveFilters().length}
                    </span>
                  )}
                </button>

                <button
                  onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                  className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  aria-label={`Switch to ${viewMode === 'grid' ? 'list' : 'grid'} view`}
                >
                  {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid3X3 className="h-4 w-4" />}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area with Sidebar Layout */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col lg:flex-row gap-6">

            {/* Desktop Sticky Filter Sidebar */}
            <div className="hidden lg:block w-80 flex-shrink-0">
              <div className="sticky top-6 max-h-[calc(100vh-6rem)] overflow-y-auto filter-sidebar-scroll space-y-6 pb-6">
                {/* Quick Search */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Search className="h-5 w-5 mr-2 text-blue-600" />
                    Quick Search
                  </h3>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="City, neighborhood, ZIP..."
                      value={filters.search}
                      onChange={(e) => handleFilterChange('search', e.target.value)}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      aria-label="Search properties by location"
                    />
                  </div>
                </div>

                {/* Primary Filters */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Settings className="h-5 w-5 mr-2 text-blue-600" />
                    Primary Filters
                  </h3>

                  <div className="space-y-4">
                    {/* Location */}
                    <div>
                      <LocationFilter
                        city={filters.city}
                        neighborhood={filters.neighborhood}
                        onCityChange={(value) => handleFilterChange('city', value)}
                        onNeighborhoodChange={(value) => handleFilterChange('neighborhood', value)}
                      />
                    </div>

                    {/* Price Range */}
                    <div>
                      <PriceRangeFilter
                        minPrice={filters.minPrice}
                        maxPrice={filters.maxPrice}
                        onMinPriceChange={(value) => handleFilterChange('minPrice', value)}
                        onMaxPriceChange={(value) => handleFilterChange('maxPrice', value)}
                      />
                    </div>

                    {/* Property Type */}
                    <div>
                      <PropertyTypeFilter
                        selectedTypes={filters.propertyType}
                        onTypesChange={(types) => handleFilterChange('propertyType', types)}
                        multiSelect={true}
                      />
                    </div>

                    {/* Bedrooms & Bathrooms */}
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <Bed className="inline h-4 w-4 mr-1 text-gray-500" />
                          Bedrooms
                        </label>
                        <select
                          value={filters.bedrooms}
                          onChange={(e) => handleFilterChange('bedrooms', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="">Any</option>
                          <option value="1">1+</option>
                          <option value="2">2+</option>
                          <option value="3">3+</option>
                          <option value="4">4+</option>
                          <option value="5">5+</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <Bath className="inline h-4 w-4 mr-1 text-gray-500" />
                          Bathrooms
                        </label>
                        <select
                          value={filters.bathrooms}
                          onChange={(e) => handleFilterChange('bathrooms', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="">Any</option>
                          <option value="1">1+</option>
                          <option value="2">2+</option>
                          <option value="3">3+</option>
                          <option value="4">4+</option>
                        </select>
                      </div>
                    </div>

                    {/* Offer Type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Home className="inline h-4 w-4 mr-1" />
                        Offer Type
                      </label>
                      <select
                        value={filters.offer}
                        onChange={(e) => handleFilterChange('offer', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="">All Types</option>
                        <option value="for-sale">For Sale</option>
                        <option value="for-rent">For Rent</option>
                        <option value="sold">Sold</option>
                        <option value="rented">Rented</option>
                        <option value="off-market">Off Market</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Advanced Filters */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                    className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                    aria-expanded={showAdvancedFilters}
                    aria-controls="advanced-filters-content"
                  >
                    <div className="flex items-center">
                      <SlidersHorizontal className="h-5 w-5 mr-2 text-blue-600" />
                      <span className="text-lg font-semibold text-gray-900">Advanced Filters</span>
                    </div>
                    {showAdvancedFilters ? (
                      <ChevronUp className="h-5 w-5 text-gray-400" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-400" />
                    )}
                  </button>

                  {showAdvancedFilters && (
                    <div id="advanced-filters-content" className="p-4 border-t border-gray-200 space-y-4">
                      {/* Special Features */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">
                          <Sparkles className="inline h-4 w-4 mr-1" />
                          Special Features
                        </label>
                        <div className="space-y-2">
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.isLuxury}
                              onChange={(e) => handleFilterChange('isLuxury', e.target.checked)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-3 text-sm text-gray-700">Luxury Property</span>
                          </label>
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.furnished}
                              onChange={(e) => handleFilterChange('furnished', e.target.checked)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-3 text-sm text-gray-700">Furnished</span>
                          </label>
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.petFriendly}
                              onChange={(e) => handleFilterChange('petFriendly', e.target.checked)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-3 text-sm text-gray-700">Pet Friendly</span>
                          </label>
                        </div>
                      </div>

                      {/* Additional Property Details */}
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            <Car className="inline h-4 w-4 mr-1" />
                            Parking
                          </label>
                          <select
                            value={filters.parking}
                            onChange={(e) => handleFilterChange('parking', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="">Any</option>
                            <option value="1">1+ Space</option>
                            <option value="2">2+ Spaces</option>
                            <option value="3">3+ Spaces</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            <Calendar className="inline h-4 w-4 mr-1" />
                            Year Built
                          </label>
                          <select
                            value={filters.yearBuilt}
                            onChange={(e) => handleFilterChange('yearBuilt', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="">Any Year</option>
                            <option value="2020">2020 or newer</option>
                            <option value="2010">2010 or newer</option>
                            <option value="2000">2000 or newer</option>
                            <option value="1990">1990 or newer</option>
                          </select>
                        </div>
                      </div>

                      {/* Amenities & Features */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">
                          Amenities & Features
                        </label>
                        <div className="grid grid-cols-2 gap-2">
                          {[
                            'pool', 'gym', 'security', 'garden', 'view', 'wifi',
                            'concierge', 'shopping', 'cafe', 'elevator', 'balcony', 'terrace'
                          ].map((feature) => (
                            <label key={feature} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={filters.features.includes(feature)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    handleFilterChange('features', [...filters.features, feature]);
                                  } else {
                                    handleFilterChange('features', filters.features.filter(f => f !== feature));
                                  }
                                }}
                                className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-xs text-gray-700 capitalize">
                                {feature === 'wifi' ? 'WiFi' : feature.replace('-', ' ')}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Sort & View Options */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Sort & View</h3>
                  <div className="space-y-4">
                    <SortFilter
                      sortBy={sortBy}
                      sortOrder={sortOrder}
                      onSortChange={handleSortChange}
                    />

                    <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                      <span className="text-sm font-medium text-gray-700">View Mode</span>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setViewMode('grid')}
                          className={`p-2 rounded-lg transition-colors ${
                            viewMode === 'grid'
                              ? 'bg-blue-100 text-blue-600'
                              : 'text-gray-400 hover:text-gray-600'
                          }`}
                        >
                          <Grid3X3 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => setViewMode('list')}
                          className={`p-2 rounded-lg transition-colors ${
                            viewMode === 'list'
                              ? 'bg-blue-100 text-blue-600'
                              : 'text-gray-400 hover:text-gray-600'
                          }`}
                        >
                          <List className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Save Search & Clear Filters */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 space-y-3">
                  <button
                    onClick={() => {
                      // Save search functionality - could integrate with user preferences
                      const searchData = {
                        filters,
                        sortBy,
                        sortOrder,
                        timestamp: new Date().toISOString()
                      };
                      localStorage.setItem('savedPropertySearch', JSON.stringify(searchData));
                      alert('Search saved! You can access it from your saved searches.');
                    }}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Search
                  </button>

                  <button
                    onClick={() => {
                      // Get alerts functionality
                      alert('Alert notifications will be sent when new properties match your criteria!');
                    }}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                  >
                    <Bell className="h-4 w-4 mr-2" />
                    Get Alerts
                  </button>

                  <button
                    onClick={clearFilters}
                    className="w-full px-4 py-2 text-red-600 border border-red-300 rounded-lg hover:bg-red-50 transition-colors flex items-center justify-center"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Clear All Filters
                  </button>
                </div>
              </div>
            </div>

            {/* Main Content Area */}
            <div className="flex-1 min-w-0">
              {/* Applied Filters Chips */}
              {getActiveFilters().length > 0 && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-900 flex items-center">
                      <Tag className="h-4 w-4 mr-2 text-blue-600" />
                      Active Filters ({getActiveFilters().length})
                    </h3>
                    <button
                      onClick={clearFilters}
                      className="text-xs text-red-600 hover:text-red-700 font-medium"
                    >
                      Clear All
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {getActiveFilters().map((filter, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-xs bg-blue-100 text-blue-800 border border-blue-200 hover:bg-blue-200 transition-colors"
                      >
                        <span className="font-medium text-blue-600">{filter.category}:</span>
                        <span className="ml-1">{filter.label}</span>
                        <button
                          onClick={() => removeFilter(filter.key)}
                          className="ml-2 hover:text-blue-600 transition-colors"
                          title={`Remove ${filter.category} filter`}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Results Summary */}
              {!loading && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {pagination.total > 0 ? (
                          <>
                            {pagination.total} Properties Found
                            {getActiveFilters().length > 0 && (
                              <span className="text-sm font-normal text-gray-600 ml-2">
                                (filtered results)
                              </span>
                            )}
                          </>
                        ) : (
                          'No Properties Found'
                        )}
                      </h3>
                      {pagination.total > 0 && (
                        <span className="text-sm text-gray-500">
                          Showing {((pagination.page - 1) * pagination.pageSize) + 1}-{Math.min(pagination.page * pagination.pageSize, pagination.total)} of {pagination.total}
                        </span>
                      )}
                    </div>
                    {pagination.total > 0 && (
                      <div className="hidden sm:flex items-center space-x-2 text-sm text-gray-600">
                        <span>Sorted by:</span>
                        <span className="font-medium capitalize">
                          {sortBy === 'createdAt' ? 'Date Added' :
                           sortBy === 'price' ? 'Price' :
                           sortBy === 'area' ? 'Area' :
                           sortBy === 'views' ? 'Popularity' :
                           'Relevance'}
                        </span>
                        <span className="text-gray-400">
                          ({sortOrder === 'asc' ? 'Low to High' : 'High to Low'})
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg mb-6">
                  {error}
                </div>
              )}

              {/* Properties Display */}
              <div className="relative">
                {/* Search Loading Overlay */}
                {searching && (
                  <div className="absolute inset-0 bg-white/80 backdrop-blur-sm z-10 flex items-center justify-center rounded-lg">
                    <div className="flex flex-col items-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-200 border-t-blue-600 mb-2"></div>
                      <p className="text-sm text-gray-600">Searching properties...</p>
                    </div>
                  </div>
                )}

                {!loading && properties.length === 0 ? (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                    <div className="max-w-md mx-auto">
                      <div className="mb-6">
                        <Search className="h-16 w-16 text-gray-300 mx-auto" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">No Properties Found</h3>
                      <p className="text-gray-500 mb-6">
                        {getActiveFilters().length > 0
                          ? "No properties match your current filters. Try adjusting your search criteria."
                          : "No properties are currently available. Please check back later."
                        }
                      </p>
                      {getActiveFilters().length > 0 && (
                        <button
                          onClick={clearFilters}
                          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          Clear All Filters
                        </button>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className={viewMode === 'grid'
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6"
                    : "space-y-6"
                  }>
                    {properties.map((property) => (
                      <div
                        key={property.documentId}
                        className={viewMode === 'grid'
                          ? "bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col h-full"
                          : "bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
                        }
                      >
                        {viewMode === 'grid' ? (
                          // Grid View
                          <>
                            <div className="relative aspect-[4/3] bg-gray-200">
                              {property.images && property.images.length > 0 ? (
                                <img
                                  src={getImageUrl(property.images[0])}
                                  alt={property.title}
                                  className="absolute inset-0 w-full h-full object-cover"
                                  onError={(e) => {
                                    e.currentTarget.style.display = 'none';
                                    const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                                    if (nextElement) {
                                      nextElement.style.display = 'flex';
                                    }
                                  }}
                                />
                              ) : null}
                              <div
                                className={`absolute inset-0 bg-gray-100 flex flex-col items-center justify-center ${property.images && property.images.length > 0 ? 'hidden' : ''}`}
                                style={{ display: property.images && property.images.length > 0 ? 'none' : 'flex' }}
                              >
                                <Home className="h-12 w-12 text-gray-400 mb-2" />
                                <span className="text-gray-500 text-sm font-medium">No Image Available</span>
                              </div>
                              <div className="absolute top-4 left-4 flex gap-2">
                                <span className={`text-white px-3 py-1 rounded-full text-sm font-semibold capitalize ${
                                  property.offer === 'for-sale' ? 'bg-green-600' :
                                  property.offer === 'for-rent' ? 'bg-blue-600' :
                                  property.offer === 'sold' ? 'bg-gray-600' :
                                  property.offer === 'rented' ? 'bg-purple-600' :
                                  'bg-orange-600'
                                }`}>
                                  {property.offer.replace('-', ' ')}
                                </span>
                                {property.isLuxury && (
                                  <span className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                    <Star className="h-3 w-3 inline mr-1" />
                                    Luxury
                                  </span>
                                )}
                              </div>
                            </div>

                            <div className="p-6 flex flex-col flex-grow">
                              <div className="flex items-start justify-between mb-3">
                                <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 flex-1 pr-2">
                                  {property.title}
                                </h3>
                                <div className="flex items-center text-gray-500 flex-shrink-0">
                                  <Eye className="h-4 w-4 mr-1" />
                                  <span className="text-sm">{property.views}</span>
                                </div>
                              </div>

                              <p className="text-gray-600 mb-3 flex items-center">
                                <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                                <span className="line-clamp-1">{property.address}, {property.city}</span>
                              </p>

                              <div className="mb-4">
                                <div className="text-2xl font-bold text-blue-600">
                                  ${property.price?.toLocaleString()}
                                  {property.offer === 'for-rent' && <span className="text-sm text-gray-500">/month</span>}
                                </div>
                              </div>

                              <div className="flex items-center flex-wrap gap-x-4 gap-y-2 text-sm text-gray-600 mb-4">
                                {property.bedrooms && (
                                  <div className="flex items-center">
                                    <Bed className="h-4 w-4 mr-1" />
                                    <span>{property.bedrooms} bed{property.bedrooms !== 1 ? 's' : ''}</span>
                                  </div>
                                )}
                                {property.bathrooms && (
                                  <div className="flex items-center">
                                    <Bath className="h-4 w-4 mr-1" />
                                    <span>{property.bathrooms} bath{property.bathrooms !== 1 ? 's' : ''}</span>
                                  </div>
                                )}
                                {property.area && (
                                  <div className="flex items-center">
                                    <Square className="h-4 w-4 mr-1" />
                                    <span>{property.area} sqft</span>
                                  </div>
                                )}
                              </div>

                              <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center flex-wrap gap-2">
                                  {property.furnished && (
                                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full whitespace-nowrap">
                                      Furnished
                                    </span>
                                  )}
                                  {property.petFriendly && (
                                    <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full whitespace-nowrap">
                                      Pet Friendly
                                    </span>
                                  )}
                                </div>
                              </div>

                              <div className="mt-auto">
                                <button
                                  onClick={() => router.push(`/properties/${property.slug || property.documentId}`)}
                                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                                >
                                  View Details
                                </button>
                              </div>
                            </div>
                          </>
                        ) : (
                          // List View
                          <div className="flex items-center space-x-4">
                            <div className="flex-shrink-0 w-24 h-24 bg-gray-200 rounded-lg overflow-hidden">
                              {property.images && property.images.length > 0 ? (
                                <img
                                  src={getImageUrl(property.images[0])}
                                  alt={property.title}
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    e.currentTarget.style.display = 'none';
                                    const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                                    if (nextElement) {
                                      nextElement.style.display = 'flex';
                                    }
                                  }}
                                />
                              ) : (
                                <div className="w-full h-full bg-gray-100 flex flex-col items-center justify-center">
                                  <Home className="h-6 w-6 text-gray-400 mb-1" />
                                  <span className="text-gray-500 text-xs font-medium">No Image</span>
                                </div>
                              )}
                            </div>

                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div>
                                  <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                                    {property.title}
                                  </h3>
                                  <p className="text-gray-600 text-sm flex items-center mt-1">
                                    <MapPin className="h-3 w-3 mr-1" />
                                    {property.address}, {property.city}
                                  </p>
                                  <div className="flex items-center space-x-3 text-sm text-gray-600 mt-2">
                                    {property.bedrooms && (
                                      <span className="flex items-center">
                                        <Bed className="h-3 w-3 mr-1" />
                                        {property.bedrooms}
                                      </span>
                                    )}
                                    {property.bathrooms && (
                                      <span className="flex items-center">
                                        <Bath className="h-3 w-3 mr-1" />
                                        {property.bathrooms}
                                      </span>
                                    )}
                                    {property.area && (
                                      <span className="flex items-center">
                                        <Square className="h-3 w-3 mr-1" />
                                        {property.area} sqft
                                      </span>
                                    )}
                                  </div>
                                </div>

                                <div className="text-right">
                                  <div className="text-xl font-bold text-blue-600">
                                    ${property.price?.toLocaleString()}
                                    {property.offer === 'for-rent' && <span className="text-sm text-gray-500">/mo</span>}
                                  </div>
                                  <div className="flex items-center justify-end space-x-2 mt-2">
                                    <span className={`text-white px-2 py-1 rounded text-xs font-medium ${
                                      property.offer === 'for-sale' ? 'bg-green-600' :
                                      property.offer === 'for-rent' ? 'bg-blue-600' :
                                      property.offer === 'sold' ? 'bg-gray-600' :
                                      property.offer === 'rented' ? 'bg-purple-600' :
                                      'bg-orange-600'
                                    }`}>
                                      {property.offer.replace('-', ' ')}
                                    </span>
                                    {property.isLuxury && (
                                      <span className="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-medium">
                                        Luxury
                                      </span>
                                    )}
                                  </div>
                                  <button
                                    onClick={() => router.push(`/properties/${property.slug || property.documentId}`)}
                                    className="mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors"
                                  >
                                    View Details
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Pagination */}
              {pagination.pageCount > 1 && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-8">
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                      disabled={pagination.page === 1}
                      className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                    >
                      Previous
                    </button>

                    <div className="flex items-center space-x-2">
                      {Array.from({ length: Math.min(5, pagination.pageCount) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <button
                            key={page}
                            onClick={() => setPagination(prev => ({ ...prev, page }))}
                            className={`px-3 py-2 rounded-lg transition-colors ${
                              pagination.page === page
                                ? 'bg-blue-600 text-white'
                                : 'border border-gray-300 hover:bg-gray-50'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      })}
                    </div>

                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pageCount, prev.page + 1) }))}
                      disabled={pagination.page === pagination.pageCount}
                      className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Filter Drawer */}
        {showMobileFilters && (
          <div className="fixed inset-0 z-50 lg:hidden">
            <div className="fixed inset-0 bg-black/20 backdrop-blur-sm" onClick={() => setShowMobileFilters(false)} />
            <div className="fixed inset-y-0 right-0 w-full max-w-sm bg-white shadow-xl overflow-y-auto">
              <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
                    <p className="text-sm text-gray-600">
                      {pagination.total > 0 ? `${pagination.total} properties` : 'No properties found'}
                    </p>
                  </div>
                  <button
                    onClick={() => setShowMobileFilters(false)}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>

                {/* Active filters count */}
                {getActiveFilters().length > 0 && (
                  <div className="mt-3 flex items-center justify-between">
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                      {getActiveFilters().length} filter{getActiveFilters().length !== 1 ? 's' : ''} active
                    </span>
                    <button
                      onClick={clearFilters}
                      className="text-xs text-red-600 hover:text-red-700 font-medium"
                    >
                      Clear all
                    </button>
                  </div>
                )}
              </div>

              <div className="p-4 space-y-6">
                {/* Mobile filter content with enhanced UX */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Search className="h-4 w-4 text-blue-600" />
                    <h3 className="font-semibold text-gray-900">Quick Search</h3>
                  </div>
                  <input
                    type="text"
                    placeholder="City, neighborhood, ZIP..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base"
                  />
                </div>

                <div className="space-y-4">
                  <LocationFilter
                    city={filters.city}
                    neighborhood={filters.neighborhood}
                    onCityChange={(value) => handleFilterChange('city', value)}
                    onNeighborhoodChange={(value) => handleFilterChange('neighborhood', value)}
                  />
                </div>

                <div className="space-y-4">
                  <PriceRangeFilter
                    minPrice={filters.minPrice}
                    maxPrice={filters.maxPrice}
                    onMinPriceChange={(value) => handleFilterChange('minPrice', value)}
                    onMaxPriceChange={(value) => handleFilterChange('maxPrice', value)}
                  />
                </div>

                <div className="space-y-4">
                  <PropertyTypeFilter
                    selectedTypes={filters.propertyType}
                    onTypesChange={(types) => handleFilterChange('propertyType', types)}
                    multiSelect={true}
                  />
                </div>

                {/* Room Requirements */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Bed className="h-4 w-4 text-blue-600" />
                    <h3 className="font-semibold text-gray-900">Rooms</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Bedrooms</label>
                      <select
                        value={filters.bedrooms}
                        onChange={(e) => handleFilterChange('bedrooms', e.target.value)}
                        className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base"
                      >
                        <option value="">Any</option>
                        <option value="1">1+</option>
                        <option value="2">2+</option>
                        <option value="3">3+</option>
                        <option value="4">4+</option>
                        <option value="5">5+</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Bathrooms</label>
                      <select
                        value={filters.bathrooms}
                        onChange={(e) => handleFilterChange('bathrooms', e.target.value)}
                        className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base"
                      >
                        <option value="">Any</option>
                        <option value="1">1+</option>
                        <option value="2">2+</option>
                        <option value="3">3+</option>
                        <option value="4">4+</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Offer Type */}
                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">Offer Type</h3>
                  <select
                    value={filters.offer}
                    onChange={(e) => handleFilterChange('offer', e.target.value)}
                    className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base"
                  >
                    <option value="">All Types</option>
                    <option value="for-sale">For Sale</option>
                    <option value="for-rent">For Rent</option>
                    <option value="sold">Sold</option>
                    <option value="rented">Rented</option>
                    <option value="off-market">Off Market</option>
                  </select>
                </div>

                {/* Advanced Filters for Mobile */}
                <div className="space-y-4 border-t border-gray-200 pt-6">
                  <div className="flex items-center space-x-2">
                    <Sparkles className="h-4 w-4 text-blue-600" />
                    <h3 className="font-semibold text-gray-900">Special Features</h3>
                  </div>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.isLuxury}
                        onChange={(e) => handleFilterChange('isLuxury', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-3 text-sm text-gray-700">Luxury Property</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.furnished}
                        onChange={(e) => handleFilterChange('furnished', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-3 text-sm text-gray-700">Furnished</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.petFriendly}
                        onChange={(e) => handleFilterChange('petFriendly', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-3 text-sm text-gray-700">Pet Friendly</span>
                    </label>
                  </div>
                </div>

                {/* Property Details for Mobile */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Car className="h-4 w-4 text-blue-600" />
                    <h3 className="font-semibold text-gray-900">Property Details</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Parking</label>
                      <select
                        value={filters.parking}
                        onChange={(e) => handleFilterChange('parking', e.target.value)}
                        className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base"
                      >
                        <option value="">Any</option>
                        <option value="1">1+ Space</option>
                        <option value="2">2+ Spaces</option>
                        <option value="3">3+ Spaces</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Year Built</label>
                      <select
                        value={filters.yearBuilt}
                        onChange={(e) => handleFilterChange('yearBuilt', e.target.value)}
                        className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base"
                      >
                        <option value="">Any Year</option>
                        <option value="2020">2020 or newer</option>
                        <option value="2010">2010 or newer</option>
                        <option value="2000">2000 or newer</option>
                        <option value="1990">1990 or newer</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Amenities & Features for Mobile */}
                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">Amenities & Features</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      'pool', 'gym', 'security', 'garden', 'view', 'wifi',
                      'concierge', 'shopping', 'cafe', 'elevator', 'balcony', 'terrace'
                    ].map((feature) => (
                      <label key={feature} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.features.includes(feature)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              handleFilterChange('features', [...filters.features, feature]);
                            } else {
                              handleFilterChange('features', filters.features.filter(f => f !== feature));
                            }
                          }}
                          className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-xs text-gray-700 capitalize">
                          {feature === 'wifi' ? 'WiFi' : feature.replace('-', ' ')}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Sort & View Options for Mobile */}
                <div className="space-y-4 border-t border-gray-200 pt-6">
                  <div className="flex items-center space-x-2">
                    <SlidersHorizontal className="h-4 w-4 text-blue-600" />
                    <h3 className="font-semibold text-gray-900">Sort & View</h3>
                  </div>
                  <div className="space-y-4">
                    <SortFilter
                      sortBy={sortBy}
                      sortOrder={sortOrder}
                      onSortChange={handleSortChange}
                    />

                    <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                      <span className="text-sm font-medium text-gray-700">View Mode</span>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setViewMode('grid')}
                          className={`p-2 rounded-lg transition-colors ${
                            viewMode === 'grid'
                              ? 'bg-blue-100 text-blue-600'
                              : 'text-gray-400 hover:text-gray-600'
                          }`}
                        >
                          <Grid3X3 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => setViewMode('list')}
                          className={`p-2 rounded-lg transition-colors ${
                            viewMode === 'list'
                              ? 'bg-blue-100 text-blue-600'
                              : 'text-gray-400 hover:text-gray-600'
                          }`}
                        >
                          <List className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3 pt-6 border-t border-gray-200">
                  <button
                    onClick={clearFilters}
                    className="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors font-medium"
                  >
                    Clear All
                  </button>
                  <button
                    onClick={() => setShowMobileFilters(false)}
                    className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                  >
                    Show {pagination.total} Properties
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

const PropertiesPage: React.FC = () => {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    }>
      <PropertiesPageContent />
    </Suspense>
  );
};

export default PropertiesPage;
