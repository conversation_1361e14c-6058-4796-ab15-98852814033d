const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

const API_BASE_URL = 'http://localhost:1337/api';

// Test user credentials
const testUser = {
  identifier: '<EMAIL>',
  password: 'TestPassword123!'
};

class CompleteImageFlowTester {
  constructor() {
    this.token = null;
    this.user = null;
    this.createdPropertyId = null;
  }

  async login() {
    console.log('🔐 Logging in...');
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/local`, testUser);
      
      this.token = response.data.jwt;
      this.user = response.data.user;
      console.log('✅ Login successful');
      return true;
    } catch (error) {
      console.log('❌ Login failed:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async createPropertyWithImage() {
    console.log('\n🏠 Creating property with image...');
    
    try {
      // Check if image file exists
      const imagePath = path.join(__dirname, 'image.jpg');
      if (!fs.existsSync(imagePath)) {
        throw new Error('Image file not found at: ' + imagePath);
      }

      const formData = new FormData();
      
      // Property data
      const propertyData = {
        title: 'Complete Flow Test Property',
        description: 'Testing complete image upload and display flow',
        price: 750000,
        currency: 'USD',
        propertyType: 'apartment',
        offer: 'for-sale',
        bedrooms: 2,
        bathrooms: 2,
        area: 120,
        areaUnit: 'sqm',
        address: '123 Test Street',
        city: 'Test City',
        country: 'Test Country',
        neighborhood: ['Test Neighborhood'],
        coordinates: { lat: 25.2048, lng: 55.2708 },
        features: ['balcony', 'parking'],
        isLuxury: false,
        furnished: true,
        petFriendly: false
      };

      formData.append('data', JSON.stringify(propertyData));
      formData.append('files.images', fs.createReadStream(imagePath));

      const response = await axios.post(`${API_BASE_URL}/properties`, formData, {
        headers: {
          'Authorization': `Bearer ${this.token}`,
          ...formData.getHeaders()
        }
      });

      this.createdPropertyId = response.data.data.id;
      console.log('✅ Property created successfully with ID:', this.createdPropertyId);
      return response.data.data;
    } catch (error) {
      console.log('❌ Property creation failed:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  async verifyPropertyImages() {
    if (!this.createdPropertyId) {
      console.log('❌ No property ID to verify');
      return false;
    }

    console.log('\n📸 Verifying property images...');
    
    try {
      const response = await axios.get(`${API_BASE_URL}/properties/${this.createdPropertyId}`, {
        headers: { 'Authorization': `Bearer ${this.token}` },
        params: { populate: ['images'] }
      });

      const property = response.data.data;
      console.log(`✅ Property retrieved: ${property.title}`);
      console.log(`📸 Images found: ${property.images?.length || 0}`);
      
      if (property.images && property.images.length > 0) {
        property.images.forEach((image, index) => {
          console.log(`   Image ${index + 1}:`);
          console.log(`     ID: ${image.id}`);
          console.log(`     URL: ${image.url}`);
          console.log(`     Full URL: http://localhost:1337${image.url}`);
          console.log(`     Name: ${image.name}`);
        });
        return true;
      } else {
        console.log('❌ No images found on property');
        return false;
      }
    } catch (error) {
      console.log('❌ Failed to verify property images:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async testEditPageData() {
    if (!this.createdPropertyId) {
      console.log('❌ No property ID to test edit');
      return false;
    }

    console.log('\n✏️  Testing edit page data...');
    
    try {
      const response = await axios.get(`${API_BASE_URL}/properties/${this.createdPropertyId}/edit`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      const property = response.data.data;
      console.log(`✅ Edit data loaded: ${property.title}`);
      console.log(`📸 Images for edit: ${property.images?.length || 0}`);
      
      if (property.images && property.images.length > 0) {
        console.log('   Frontend edit form should display:');
        property.images.forEach((image, index) => {
          console.log(`     ${index + 1}. ${image.name} (${image.url})`);
        });
        return true;
      }
      return false;
    } catch (error) {
      console.log('❌ Failed to load edit data:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async testImageUpdate() {
    if (!this.createdPropertyId) {
      console.log('❌ No property ID to update');
      return false;
    }

    console.log('\n🔄 Testing image update...');
    
    try {
      // Check if image file exists
      const imagePath = path.join(__dirname, 'image.jpg');
      if (!fs.existsSync(imagePath)) {
        throw new Error('Image file not found for update test');
      }

      const formData = new FormData();
      
      // Updated property data
      const propertyData = {
        title: 'Complete Flow Test Property - Updated',
        description: 'Updated description with new image'
      };

      formData.append('data', JSON.stringify(propertyData));
      formData.append('files.images', fs.createReadStream(imagePath));

      const response = await axios.put(`${API_BASE_URL}/properties/${this.createdPropertyId}`, formData, {
        headers: {
          'Authorization': `Bearer ${this.token}`,
          ...formData.getHeaders()
        }
      });

      console.log('✅ Property updated successfully');
      
      // Verify the update
      const verifyResponse = await axios.get(`${API_BASE_URL}/properties/${this.createdPropertyId}`, {
        headers: { 'Authorization': `Bearer ${this.token}` },
        params: { populate: ['images'] }
      });

      const updatedProperty = verifyResponse.data.data;
      console.log(`📸 Images after update: ${updatedProperty.images?.length || 0}`);
      
      return true;
    } catch (error) {
      console.log('❌ Update failed:', error.response?.data?.error?.message || error.message);
      return false;
    }
  }

  async runCompleteTest() {
    console.log('🧪 Starting Complete Image Flow Test\n');
    
    // Test 1: Login
    const loginSuccess = await this.login();
    if (!loginSuccess) return;

    // Test 2: Create property with image
    const property = await this.createPropertyWithImage();
    if (!property) return;

    // Test 3: Verify images are attached
    const imagesVerified = await this.verifyPropertyImages();
    
    // Test 4: Test edit page data
    const editDataLoaded = await this.testEditPageData();
    
    // Test 5: Test image update
    const updateSuccess = await this.testImageUpdate();
    
    console.log('\n🎯 Complete Image Flow Test Results:');
    console.log(`✅ Property Creation: ${property ? 'SUCCESS' : 'FAILED'}`);
    console.log(`✅ Image Attachment: ${imagesVerified ? 'SUCCESS' : 'FAILED'}`);
    console.log(`✅ Edit Data Loading: ${editDataLoaded ? 'SUCCESS' : 'FAILED'}`);
    console.log(`✅ Image Update: ${updateSuccess ? 'SUCCESS' : 'FAILED'}`);
    
    if (property && imagesVerified && editDataLoaded && updateSuccess) {
      console.log('\n🎉 ALL TESTS PASSED! Image upload and display functionality is working correctly.');
      console.log('\n📋 What should work in the frontend:');
      console.log('   ✅ Property creation with image upload');
      console.log('   ✅ Property listing with image display');
      console.log('   ✅ Property detail page with image gallery');
      console.log('   ✅ Property edit form with existing images shown');
      console.log('   ✅ Property update with new image upload');
      console.log(`\n🔗 Test the frontend at:`);
      console.log(`   - Property List: http://localhost:3001/dashboard/properties`);
      console.log(`   - Property Edit: http://localhost:3001/dashboard/properties/${this.createdPropertyId}/edit`);
      console.log(`   - Property View: http://localhost:3001/properties/${this.createdPropertyId}`);
    } else {
      console.log('\n❌ Some tests failed. Check the logs above for details.');
    }
  }
}

// Run the complete test
const tester = new CompleteImageFlowTester();
tester.runCompleteTest().catch(console.error);
