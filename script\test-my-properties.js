const axios = require('axios');

const FRONTEND_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:1337/api';

async function testMyPropertiesPage() {
  console.log('🏠 Testing My Properties Page...\n');

  try {
    // Step 1: Test API authentication and data
    console.log('1. Testing API authentication and data...');
    
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    console.log('✅ API login successful');
    const token = loginResponse.data.jwt;
    const user = loginResponse.data.user;
    console.log(`   User: ${user.username} (ID: ${user.id})`);
    
    // Step 2: Test my-properties API endpoint
    console.log('\n2. Testing my-properties API endpoint...');
    
    const myPropertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const properties = myPropertiesResponse.data.data || myPropertiesResponse.data;
    console.log(`✅ My Properties API working`);
    console.log(`   Found ${properties.length} properties`);
    
    if (properties.length > 0) {
      console.log('\n   Property Details:');
      properties.forEach((property, index) => {
        console.log(`   ${index + 1}. ${property.title}`);
        console.log(`      ID: ${property.id} | Document ID: ${property.documentId}`);
        console.log(`      Price: ${property.currency} ${property.price.toLocaleString()}`);
        console.log(`      Type: ${property.propertyType} | Offer: ${property.offer}`);
        console.log(`      Location: ${property.city}, ${property.country}`);
        console.log(`      Published: ${property.publishedAt ? 'Yes' : 'No'}`);
        console.log(`      Created: ${new Date(property.createdAt).toLocaleDateString()}`);
        console.log('');
      });
    }
    
    // Step 3: Test frontend page loading
    console.log('3. Testing frontend page loading...');
    
    const frontendResponse = await axios.get(`${FRONTEND_URL}/dashboard/properties`);
    console.log('✅ My Properties frontend page loading (200)');
    
    // Check if the page contains expected elements
    const pageContent = frontendResponse.data;
    const hasPropertyElements = pageContent.includes('My Properties') && 
                               pageContent.includes('properties') &&
                               pageContent.includes('dashboard');
    
    if (hasPropertyElements) {
      console.log('✅ Page contains expected property elements');
    } else {
      console.log('⚠️  Page might be missing some property elements');
    }
    
    // Step 4: Test property actions (if properties exist)
    if (properties.length > 0) {
      console.log('\n4. Testing property actions...');
      
      const firstProperty = properties[0];
      
      // Test property edit endpoint
      try {
        const editResponse = await axios.get(`${API_URL}/properties/${firstProperty.documentId}/edit`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log('✅ Property edit endpoint working');
        console.log(`   Can edit: ${firstProperty.title}`);
      } catch (editError) {
        console.log('❌ Property edit endpoint failed:', editError.response?.status);
      }
      
      // Test property view (without auth - should work)
      try {
        const viewResponse = await axios.get(`${API_URL}/properties/${firstProperty.documentId}`);
        console.log('✅ Property view endpoint working');
      } catch (viewError) {
        console.log('❌ Property view endpoint failed:', viewError.response?.status);
      }
      
      // Test publish/unpublish
      if (!firstProperty.publishedAt) {
        try {
          const publishResponse = await axios.put(`${API_URL}/properties/${firstProperty.documentId}/publish`, {}, {
            headers: { 'Authorization': `Bearer ${token}` }
          });
          console.log('✅ Property publish endpoint working');
          
          // Unpublish it back
          await axios.put(`${API_URL}/properties/${firstProperty.documentId}/unpublish`, {}, {
            headers: { 'Authorization': `Bearer ${token}` }
          });
          console.log('✅ Property unpublish endpoint working');
        } catch (publishError) {
          console.log('⚠️  Property publish/unpublish failed:', publishError.response?.status);
        }
      }
    }
    
    // Step 5: Test filtering and search functionality
    console.log('\n5. Testing filtering capabilities...');
    
    // Test different offer types
    const forSaleProperties = properties.filter(p => p.offer === 'for-sale');
    const forRentProperties = properties.filter(p => p.offer === 'for-rent');
    const publishedProperties = properties.filter(p => p.publishedAt);
    const draftProperties = properties.filter(p => !p.publishedAt);
    
    console.log(`   For Sale: ${forSaleProperties.length} properties`);
    console.log(`   For Rent: ${forRentProperties.length} properties`);
    console.log(`   Published: ${publishedProperties.length} properties`);
    console.log(`   Drafts: ${draftProperties.length} properties`);
    
    // Test property types
    const propertyTypes = [...new Set(properties.map(p => p.propertyType))];
    console.log(`   Property Types: ${propertyTypes.join(', ')}`);
    
    // Step 6: Summary and recommendations
    console.log('\n🎉 My Properties Page Test Summary:');
    console.log('   ✅ API authentication working');
    console.log('   ✅ My Properties endpoint responding');
    console.log('   ✅ Frontend page loading correctly');
    console.log(`   ✅ Found ${properties.length} user properties`);
    console.log('   ✅ Property actions available');
    console.log('   ✅ Filtering data available');
    
    console.log('\n📝 Manual Testing Instructions:');
    console.log('   1. Open: http://localhost:3000/auth/login');
    console.log('   2. Login with: <EMAIL> / TestPassword123!');
    console.log('   3. Navigate to: http://localhost:3000/dashboard/properties');
    console.log('   4. Verify you can see:');
    console.log('      - List of your properties');
    console.log('      - Search and filter options');
    console.log('      - Edit/View/Delete buttons for each property');
    console.log('      - Grid/List view toggle');
    console.log('      - Property status (Published/Draft)');
    
    if (properties.length === 0) {
      console.log('\n💡 No properties found. To test with data:');
      console.log('   - Go to http://localhost:3000/submit-property');
      console.log('   - Create a few test properties');
      console.log('   - Return to My Properties page');
    }
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Data:`, error.response.data);
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Connection refused. Make sure:');
      console.log('   - Frontend is running on http://localhost:3000');
      console.log('   - Backend is running on http://localhost:1337');
    }
  }
}

testMyPropertiesPage().catch(console.error);
