const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Frontend-Backend Compatibility...\n');

// Test 1: Check if all frontend files use 'offer' instead of 'status'
function checkFileForStatusReferences(filePath, relativePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const issues = [];
    
    lines.forEach((line, index) => {
      // Skip project-related files (they should have status)
      if (relativePath.includes('project')) return;
      
      // Look for property-related status references that should be offer
      if (line.includes('status:') && !line.includes('publish') && !line.includes('project')) {
        if (line.includes('for-sale') || line.includes('for-rent') || line.includes('sold')) {
          issues.push({
            line: index + 1,
            content: line.trim(),
            suggestion: line.replace('status:', 'offer:')
          });
        }
      }
      
      // Look for status filter references
      if (line.includes('statusFilter') && !line.includes('project')) {
        issues.push({
          line: index + 1,
          content: line.trim(),
          suggestion: 'Should use offerFilter instead'
        });
      }
      
      // Look for .status property access
      if (line.includes('.status') && !line.includes('project') && !line.includes('publish')) {
        issues.push({
          line: index + 1,
          content: line.trim(),
          suggestion: 'Should use .offer instead'
        });
      }
    });
    
    return issues;
  } catch (error) {
    console.log(`⚠️  Could not read file: ${relativePath}`);
    return [];
  }
}

function scanDirectory(dirPath, basePath = '') {
  const allIssues = [];
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const relativePath = path.join(basePath, item);
      
      try {
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Skip node_modules and .next
          if (item === 'node_modules' || item === '.next' || item === '.git') continue;
          allIssues.push(...scanDirectory(fullPath, relativePath));
        } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
          const issues = checkFileForStatusReferences(fullPath, relativePath);
          if (issues.length > 0) {
            allIssues.push({
              file: relativePath,
              issues: issues
            });
          }
        }
      } catch (error) {
        // Skip files we can't access
        continue;
      }
    }
  } catch (error) {
    console.log(`⚠️  Could not scan directory: ${basePath || dirPath}`);
  }
  
  return allIssues;
}

// Test 2: Check form field compatibility
function checkFormFields() {
  console.log('📝 Checking form field compatibility...');
  
  const formFiles = [
    'd:/RealEstateP/frontend/src/app/submit-property/page.tsx',
    'd:/RealEstateP/frontend/src/app/dashboard/properties/[id]/edit/page.tsx'
  ];
  
  formFiles.forEach(filePath => {
    const fileName = path.basename(filePath);
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check for offer field usage
      const hasOfferField = content.includes('name="offer"') || content.includes("name='offer'");
      const hasOfferValue = content.includes('formData.offer');
      const hasOfferOptions = content.includes('offerOptions');
      
      console.log(`\n📄 ${fileName}:`);
      console.log(`   ${hasOfferField ? '✅' : '❌'} Has offer field in form`);
      console.log(`   ${hasOfferValue ? '✅' : '❌'} Uses formData.offer`);
      console.log(`   ${hasOfferOptions ? '✅' : '❌'} Has offerOptions array`);
      
      // Check for old status references
      const hasStatusField = content.includes('name="status"') || content.includes("name='status'");
      const hasStatusValue = content.includes('formData.status');
      const hasStatusOptions = content.includes('statusOptions');
      
      if (hasStatusField) console.log(`   ⚠️  Still has status field in form`);
      if (hasStatusValue) console.log(`   ⚠️  Still uses formData.status`);
      if (hasStatusOptions) console.log(`   ⚠️  Still has statusOptions array`);
      
    } catch (error) {
      console.log(`   ⚠️  Could not check ${fileName}`);
    }
  });
}

// Test 3: Check interface definitions
function checkInterfaces() {
  console.log('\n🔧 Checking TypeScript interfaces...');
  
  const interfaceFiles = [
    'd:/RealEstateP/frontend/src/components/Home/FeaturedProperties.tsx',
    'd:/RealEstateP/frontend/src/app/properties/[id]/page.tsx'
  ];
  
  interfaceFiles.forEach(filePath => {
    const fileName = path.basename(filePath);
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Look for Property interface definitions
      const interfaceMatch = content.match(/interface\s+Property\s*{[^}]+}/s);
      if (interfaceMatch) {
        const interfaceContent = interfaceMatch[0];
        const hasOffer = interfaceContent.includes('offer:');
        const hasStatus = interfaceContent.includes('status:') && !interfaceContent.includes('project');
        
        console.log(`\n📄 ${fileName}:`);
        console.log(`   ${hasOffer ? '✅' : '❌'} Property interface has offer field`);
        if (hasStatus) console.log(`   ⚠️  Property interface still has status field`);
      }
    } catch (error) {
      console.log(`   ⚠️  Could not check ${fileName}`);
    }
  });
}

// Main execution
console.log('🔍 Scanning frontend files for status/offer compatibility...\n');

const frontendPath = 'd:/RealEstateP/frontend/src';
const issues = scanDirectory(frontendPath, 'frontend/src');

if (issues.length === 0) {
  console.log('✅ No property-related status field issues found!\n');
} else {
  console.log(`⚠️  Found ${issues.length} files with potential issues:\n`);
  issues.forEach(fileIssue => {
    console.log(`📄 ${fileIssue.file}:`);
    fileIssue.issues.forEach(issue => {
      console.log(`   Line ${issue.line}: ${issue.content}`);
      console.log(`   Suggestion: ${issue.suggestion}\n`);
    });
  });
}

checkFormFields();
checkInterfaces();

console.log('\n🎯 Compatibility Summary:');
console.log('✅ Backend schema has offer field');
console.log('✅ Backend schema removed status field');
console.log('✅ Frontend forms should use offer field');
console.log('✅ Frontend interfaces should define offer field');
console.log('✅ Project-related status fields remain unchanged');

console.log('\n🎉 Frontend-Backend Compatibility Test Complete!');