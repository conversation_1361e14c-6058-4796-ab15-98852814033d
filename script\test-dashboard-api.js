const axios = require('axios');

const API_URL = 'http://localhost:1337/api';

async function testDashboardAPI() {
  console.log('🧪 Testing Dashboard API Endpoints...\n');

  try {
    // Test 1: Check if Strap<PERSON> is running
    console.log('1. Testing Strapi connection...');
    const healthCheck = await axios.get(`${API_URL}/properties`);
    console.log('✅ Strapi is running and responding');
    console.log(`   Found ${healthCheck.data.data?.length || 0} properties\n`);

    // Test 2: Test authentication endpoints
    console.log('2. Testing authentication...');
    
    // Try to register a test user (might fail if user exists)
    try {
      const registerResponse = await axios.post(`${API_URL}/auth/local/register`, {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'testpassword123'
      });
      console.log('✅ User registration successful');
      console.log(`   User ID: ${registerResponse.data.user.id}`);
      console.log(`   JWT Token: ${registerResponse.data.jwt.substring(0, 20)}...`);
      
      // Test the my-properties endpoint with the new token
      const token = registerResponse.data.jwt;
      const myPropertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('✅ My Properties endpoint working');
      console.log(`   User has ${myPropertiesResponse.data.data?.length || 0} properties\n`);
      
    } catch (registerError) {
      if (registerError.response?.status === 400) {
        console.log('ℹ️  User already exists, trying to login...');
        
        // Try to login with existing user
        try {
          const loginResponse = await axios.post(`${API_URL}/auth/local`, {
            identifier: '<EMAIL>',
            password: 'testpassword123'
          });
          console.log('✅ User login successful');
          console.log(`   User ID: ${loginResponse.data.user.id}`);
          
          // Test the my-properties endpoint with login token
          const token = loginResponse.data.jwt;
          const myPropertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          console.log('✅ My Properties endpoint working');
          console.log(`   User has ${myPropertiesResponse.data.data?.length || 0} properties\n`);
          
        } catch (loginError) {
          console.log('❌ Login failed:', loginError.response?.data || loginError.message);
        }
      } else {
        console.log('❌ Registration failed:', registerError.response?.data || registerError.message);
      }
    }

    // Test 3: Test unauthenticated my-properties call (should fail)
    console.log('3. Testing unauthenticated access...');
    try {
      await axios.get(`${API_URL}/properties/my-properties`);
      console.log('⚠️  Unauthenticated access succeeded (this might be a security issue)');
    } catch (authError) {
      if (authError.response?.status === 401) {
        console.log('✅ Unauthenticated access properly blocked');
      } else {
        console.log('❌ Unexpected error:', authError.response?.data || authError.message);
      }
    }

    // Test 4: Test featured properties (should work without auth)
    console.log('\n4. Testing featured properties...');
    try {
      const featuredResponse = await axios.get(`${API_URL}/properties/featured`);
      console.log('✅ Featured properties endpoint working');
      console.log(`   Found ${featuredResponse.data.data?.length || 0} featured properties\n`);
    } catch (featuredError) {
      console.log('❌ Featured properties failed:', featuredError.response?.data || featuredError.message);
    }

    console.log('🎉 Dashboard API test completed!');

  } catch (error) {
    console.log('❌ Critical error:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure Strapi is running on http://localhost:1337');
    }
  }
}

// Run the test
testDashboardAPI().catch(console.error);
