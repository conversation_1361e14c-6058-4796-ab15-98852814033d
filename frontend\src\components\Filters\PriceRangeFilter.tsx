'use client';

import React, { useState, useEffect } from 'react';
import { DollarSign, TrendingUp, TrendingDown } from 'lucide-react';

interface PriceRangeFilterProps {
  minPrice: string;
  maxPrice: string;
  currency?: string;
  onMinPriceChange: (value: string) => void;
  onMaxPriceChange: (value: string) => void;
  onCurrencyChange?: (value: string) => void;
  className?: string;
}

const CURRENCY_OPTIONS = [
  { value: 'USD', label: 'USD ($)', symbol: '$' },
  { value: 'EUR', label: 'EUR (€)', symbol: '€' },
  { value: 'GBP', label: 'GBP (£)', symbol: '£' },
  { value: 'AED', label: 'AED (د.إ)', symbol: 'د.إ' },
  { value: 'EGP', label: 'EGP (ج.م)', symbol: 'ج.م' },
];

const PRICE_PRESETS = [
  { label: 'Under $100K', min: '', max: '100000' },
  { label: '$100K - $300K', min: '100000', max: '300000' },
  { label: '$300K - $500K', min: '300000', max: '500000' },
  { label: '$500K - $1M', min: '500000', max: '1000000' },
  { label: 'Over $1M', min: '1000000', max: '' },
];

export const PriceRangeFilter: React.FC<PriceRangeFilterProps> = ({
  minPrice,
  maxPrice,
  currency = 'USD',
  onMinPriceChange,
  onMaxPriceChange,
  onCurrencyChange,
  className = '',
}) => {
  const [validationError, setValidationError] = useState<string>('');
  const [showPresets, setShowPresets] = useState(false);

  const currentCurrency = CURRENCY_OPTIONS.find(c => c.value === currency) || CURRENCY_OPTIONS[0];

  // Validate price range
  useEffect(() => {
    if (minPrice && maxPrice) {
      const min = parseFloat(minPrice);
      const max = parseFloat(maxPrice);
      
      if (min > max) {
        setValidationError('Minimum price cannot be greater than maximum price');
      } else {
        setValidationError('');
      }
    } else {
      setValidationError('');
    }
  }, [minPrice, maxPrice]);

  const formatNumber = (value: string) => {
    if (!value) return '';
    const num = parseFloat(value);
    if (isNaN(num)) return value;
    return num.toLocaleString();
  };

  const handleMinPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/,/g, '');
    onMinPriceChange(value);
  };

  const handleMaxPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/,/g, '');
    onMaxPriceChange(value);
  };

  const applyPreset = (preset: typeof PRICE_PRESETS[0]) => {
    onMinPriceChange(preset.min);
    onMaxPriceChange(preset.max);
    setShowPresets(false);
  };

  const clearPriceRange = () => {
    onMinPriceChange('');
    onMaxPriceChange('');
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Currency Selector */}
      {onCurrencyChange && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Currency
          </label>
          <select
            value={currency}
            onChange={(e) => onCurrencyChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {CURRENCY_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Price Range Inputs */}
      <div>
        <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
          <DollarSign className="h-4 w-4 text-gray-500 mr-2" />
          Price Range
        </label>
        <div className="grid grid-cols-2 gap-3">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500 text-sm">{currentCurrency.symbol}</span>
            </div>
            <input
              type="text"
              placeholder="Min Price"
              value={formatNumber(minPrice)}
              onChange={handleMinPriceChange}
              className={`w-full pl-8 pr-4 py-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                validationError ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {minPrice && (
              <TrendingDown className="absolute right-3 top-3 h-5 w-5 text-red-500" />
            )}
          </div>

          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500 text-sm">{currentCurrency.symbol}</span>
            </div>
            <input
              type="text"
              placeholder="Max Price"
              value={formatNumber(maxPrice)}
              onChange={handleMaxPriceChange}
              className={`w-full pl-8 pr-4 py-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                validationError ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {maxPrice && (
              <TrendingUp className="absolute right-3 top-3 h-5 w-5 text-green-500" />
            )}
          </div>
        </div>

        {validationError && (
          <p className="mt-2 text-sm text-red-600">{validationError}</p>
        )}
      </div>

      {/* Price Presets */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Quick Ranges</span>
          <button
            type="button"
            onClick={() => setShowPresets(!showPresets)}
            className="text-sm text-blue-600 hover:text-blue-700"
          >
            {showPresets ? 'Hide' : 'Show'} Presets
          </button>
        </div>

        {showPresets && (
          <div className="grid grid-cols-1 gap-2">
            {PRICE_PRESETS.map((preset, index) => (
              <button
                key={index}
                type="button"
                onClick={() => applyPreset(preset)}
                className="text-left px-3 py-2 text-sm bg-gray-50 hover:bg-gray-100 rounded-md transition-colors"
              >
                {preset.label}
              </button>
            ))}
            <button
              type="button"
              onClick={clearPriceRange}
              className="text-left px-3 py-2 text-sm bg-red-50 hover:bg-red-100 text-red-700 rounded-md transition-colors"
            >
              Clear Price Range
            </button>
          </div>
        )}
      </div>

      {/* Current Range Display */}
      {(minPrice || maxPrice) && (
        <div className="p-3 bg-blue-50 rounded-md">
          <div className="flex items-center space-x-2">
            <DollarSign className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">
              Current Range: {currentCurrency.symbol}
              {minPrice ? formatNumber(minPrice) : '0'} - {currentCurrency.symbol}
              {maxPrice ? formatNumber(maxPrice) : '∞'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
