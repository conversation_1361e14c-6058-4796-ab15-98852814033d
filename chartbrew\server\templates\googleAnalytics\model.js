const builder = require("./builder");
const googleConnector = require("../../modules/googleConnector");
const db = require("../../models/models");
const { chartColors } = require("../../charts/colors");

const template = (configuration = {}) => ({
  "Connections": [
    {
      "host": null,
      "dbName": null,
      "port": null,
      "username": null,
      "password": null,
      "options": null,
      "connectionString": "null",
      "authentication": null,
      "firebaseServiceAccount": null,
      "name": "Google Analytics - GA4",
      "type": "googleAnalytics",
      "subType": "googleAnalytics",
      "active": true,
      "srv": false
    }
  ],
  "Datasets": [{
    "td_id": 1,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].date": "date",
      "root[].newUsers": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": {
      "joins": []
    },
    "connection_id": null,
    "query": null,
    "xAxis": "root[].date",
    "xAxisOperation": null,
    "yAxis": "root[].newUsers",
    "yAxisOperation": "none",
    "dateField": "root[].date",
    "datasetColor": chartColors.blue.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "New users",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 0,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "newUsers",
          "startDate": "30daysAgo",
          "endDate": "yesterday",
          "dimensions": "date"
        },
        "connection_id": 678,
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
        "Connection": 678
      }
    ]
  }, {
    "td_id": 2,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].date": "date",
      "root[].activeUsers": "date"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].date",
    "xAxisOperation": null,
    "yAxis": "root[].activeUsers",
    "yAxisOperation": "none",
    "dateField": "root[].date",
    "datasetColor": chartColors.amber.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Active users",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 0,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": "accounts/********",
          "propertyId": "properties/*********",
          "filters": [],
          "metrics": "activeUsers",
          "startDate": "30daysAgo",
          "endDate": "yesterday",
          "dimensions": "date"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
        "Connection": 678
      }
    ]
  }, {
    "td_id": 3,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].date": "date",
      "root[].newUsers": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].date",
    "xAxisOperation": null,
    "yAxis": "root[].newUsers",
    "yAxisOperation": "count",
    "dateField": "root[].date",
    "datasetColor": chartColors.blue.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "New users",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 0,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "newUsers",
          "startDate": "30daysAgo",
          "endDate": "yesterday",
          "dimensions": "date"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 4,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].date": "date",
      "root[].activeUsers": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": {
      "joins": []
    },
    "connection_id": null,
    "query": null,
    "xAxis": "root[].date",
    "xAxisOperation": null,
    "yAxis": "root[].activeUsers",
    "yAxisOperation": "count",
    "dateField": "root[].date",
    "datasetColor": chartColors.amber.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Active users",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 1,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "activeUsers",
          "startDate": "30daysAgo",
          "endDate": "yesterday",
          "dimensions": "date"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 5,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].bounceRate": "string"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].bounceRate",
    "xAxisOperation": null,
    "yAxis": "root[].bounceRate",
    "yAxisOperation": "none",
    "dateField": null,
    "datasetColor": chartColors.blue.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Bounce rate",
    "pointRadius": null,
    "formula": "{val * 100}%",
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 0,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "bounceRate",
          "startDate": "30daysAgo",
          "endDate": "yesterday"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 6,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].date": "date",
      "root[].eventCount": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].date",
    "xAxisOperation": null,
    "yAxis": "root[].eventCount",
    "yAxisOperation": "none",
    "dateField": "root[].date",
    "datasetColor": chartColors.blue.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Total events",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 0,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "eventCount",
          "startDate": "30daysAgo",
          "endDate": "yesterday",
          "dimensions": "date"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 7,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].conversions": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].conversions",
    "xAxisOperation": null,
    "yAxis": "root[].conversions",
    "yAxisOperation": "none",
    "dateField": null,
    "datasetColor": chartColors.blue.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Conversions",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 0,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "conversions",
          "startDate": "30daysAgo",
          "endDate": "yesterday"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 8,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].userEngagementDuration": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": {
      "joins": []
    },
    "connection_id": null,
    "query": null,
    "xAxis": "root[].userEngagementDuration",
    "xAxisOperation": null,
    "yAxis": "root[].userEngagementDuration",
    "yAxisOperation": "none",
    "dateField": "",
    "datasetColor": chartColors.blue.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Duration (s)",
    "pointRadius": null,
    "formula": "",
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 0,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "userEngagementDuration",
          "startDate": "30daysAgo",
          "endDate": "yesterday"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 9,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].engagementRate": "string"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].engagementRate",
    "xAxisOperation": null,
    "yAxis": "root[].engagementRate",
    "yAxisOperation": "none",
    "dateField": null,
    "datasetColor": chartColors.amber.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Engagement rate",
    "pointRadius": null,
    "formula": "{val * 100}%",
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 1,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "engagementRate",
          "startDate": "30daysAgo",
          "endDate": "yesterday"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 10,
    "fillColor": [
      chartColors.blue.rgb,
      chartColors.amber.rgb,
      chartColors.teal.rgb,
      chartColors.fuchsia.rgb,
      chartColors.lime.rgb,
      chartColors.deep_fuchsia.rgb,
      chartColors.orange.rgb,
      chartColors.light_purple.rgb,
      chartColors.green.rgb,
      chartColors.rose.rgb,
    ],
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].country": "string",
      "root[].sessions": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].country",
    "xAxisOperation": null,
    "yAxis": "root[].sessions",
    "yAxisOperation": "none",
    "dateField": null,
    "datasetColor": "rgba(255, 255, 255, 1)",
    "fill": false,
    "multiFill": true,
    "dateFormat": null,
    "legend": "Sessions",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": "",
    "averageByTotal": false,
    "order": 0,
    "maxRecords": 10,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "sessions",
          "startDate": "30daysAgo",
          "endDate": "yesterday",
          "dimensions": "country"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 11,
    "fillColor": [
      chartColors.blue.rgb,
      chartColors.amber.rgb,
      chartColors.teal.rgb,
      chartColors.fuchsia.rgb,
      chartColors.lime.rgb,
    ],
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].sessionSource": "string",
      "root[].sessions": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].sessionSource",
    "xAxisOperation": null,
    "yAxis": "root[].sessions",
    "yAxisOperation": "none",
    "dateField": null,
    "datasetColor": "rgba(255, 255, 255, 1)",
    "fill": false,
    "multiFill": true,
    "dateFormat": null,
    "legend": "Sessions",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": "desc",
    "averageByTotal": false,
    "order": 0,
    "maxRecords": 10,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "sessions",
          "startDate": "30daysAgo",
          "endDate": "yesterday",
          "dimensions": "sessionSource"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 12,
    "fillColor": [
      chartColors.blue.rgb,
      chartColors.amber.rgb,
    ],
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].deviceCategory": "string",
      "root[].sessions": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].deviceCategory",
    "xAxisOperation": null,
    "yAxis": "root[].sessions",
    "yAxisOperation": "none",
    "dateField": null,
    "datasetColor": "rgba(255, 255, 255, 1)",
    "fill": false,
    "multiFill": true,
    "dateFormat": null,
    "legend": "Dataset #1",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 0,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "sessions",
          "startDate": "30daysAgo",
          "endDate": "yesterday",
          "dimensions": "deviceCategory"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 13,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].sessionCampaignName": "string",
      "root[].sessions": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].sessionCampaignName",
    "xAxisOperation": null,
    "yAxis": "root[].sessions",
    "yAxisOperation": "count",
    "dateField": null,
    "datasetColor": chartColors.blue.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Campaigns",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 0,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "sessions",
          "startDate": "30daysAgo",
          "endDate": "yesterday",
          "dimensions": "sessionCampaignName"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 14,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].sessionMedium": "string",
      "root[].sessions": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].sessionMedium",
    "xAxisOperation": null,
    "yAxis": "root[].sessions",
    "yAxisOperation": "count",
    "dateField": null,
    "datasetColor": chartColors.amber.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Mediums",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 0,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "sessions",
          "startDate": "30daysAgo",
          "endDate": "yesterday",
          "dimensions": "sessionMedium"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }, {
    "td_id": 15,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root[].sessionDefaultChannelGroup": "string",
      "root[].sessions": "number"
    },
    "excludedFields": null,
    "groups": null,
    "columnsOrder": null,
    "configuration": null,
    "joinSettings": null,
    "connection_id": null,
    "query": null,
    "xAxis": "root[].sessionDefaultChannelGroup",
    "xAxisOperation": null,
    "yAxis": "root[].sessions",
    "yAxisOperation": "count",
    "dateField": null,
    "datasetColor": chartColors.teal.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Channels",
    "pointRadius": null,
    "formula": null,
    "groupBy": null,
    "sort": null,
    "averageByTotal": false,
    "order": 0,
    "maxRecords": null,
    "DataRequests": [
      {
        "headers": null,
        "body": "null",
        "conditions": null,
        "configuration": {
          "accountId": configuration.accountId,
          "propertyId": configuration.propertyId,
          "filters": [],
          "metrics": "sessions",
          "startDate": "30daysAgo",
          "endDate": "yesterday",
          "dimensions": "sessionDefaultChannelGroup"
        },
        "method": null,
        "route": null,
        "useGlobalHeaders": true,
        "query": null,
        "pagination": false,
        "items": "items",
        "itemsLimit": 0,
        "offset": "offset",
        "paginationField": null,
        "template": null,
      }
    ]
  }],
  "Charts": [
    {
      "name": "Users",
      "type": "line",
      "subType": "lcTimeseries",
      "public": false,
      "shareable": false,
      "chartSize": 2,
      "displayLegend": false,
      "pointRadius": null,
      "dataLabels": false,
      "startDate": null,
      "endDate": null,
      "dateVarsFormat": null,
      "includeZeros": true,
      "currentEndDate": false,
      "fixedStartDate": false,
      "timeInterval": "day",
      "autoUpdate": 86400,
      "draft": false,
      "mode": "kpichart",
      "maxValue": null,
      "minValue": null,
      "disabledExport": null,
      "onReport": false,
      "xLabelTicks": "default",
      "stacked": false,
      "horizontal": false,
      "showGrowth": true,
      "layout": {
        "xxs": [0, 0, 2, 2], "xs": [0, 0, 6, 2], "sm": [0, 0, 4, 2], "md": [0, 0, 5, 2], "lg": [0, 0, 6, 2]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 1,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].date": "date",
            "root[].newUsers": "number"
          },
          "excludedFields": null,
          "groups": null,
          "columnsOrder": null,
          "configuration": null,
          "joinSettings": {
            "joins": []
          },
          "connection_id": null,
          "datasetColor": chartColors.blue.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "New users",
          "pointRadius": null,
          "formula": null,
          "groupBy": null,
          "sort": null,
          "averageByTotal": false,
          "order": 0,
          "maxRecords": null,
        },
        {
          "td_id": 2,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].date": "date",
            "root[].activeUsers": "date"
          },
          "excludedFields": null,
          "groups": null,
          "columnsOrder": null,
          "configuration": null,
          "joinSettings": null,
          "connection_id": null,
          "datasetColor": chartColors.amber.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Active users",
          "pointRadius": null,
          "formula": null,
          "groupBy": null,
          "sort": null,
          "averageByTotal": false,
          "order": 0,
          "maxRecords": null,
        }
      ],
      "tid": 0
    },
    {
      "name": "Users KPIs",
      "type": "kpi",
      "subType": "AddTimeseries",
      "public": false,
      "shareable": false,
      "chartSize": 1,
      "displayLegend": false,
      "pointRadius": null,
      "dataLabels": false,
      "startDate": null,
      "endDate": null,
      "dateVarsFormat": null,
      "includeZeros": true,
      "currentEndDate": false,
      "fixedStartDate": false,
      "timeInterval": "day",
      "autoUpdate": 86400,
      "draft": false,
      "maxValue": null,
      "minValue": null,
      "disabledExport": null,
      "onReport": false,
      "xLabelTicks": "default",
      "stacked": false,
      "horizontal": false,
      "showGrowth": false,
      "layout": {
        "xxs": [0, 0, 2, 2], "xs": [0, 2, 6, 2], "sm": [4, 0, 2, 2], "md": [5, 0, 3, 2], "lg": [6, 0, 3, 2]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 3,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].date": "date",
            "root[].newUsers": "number"
          },
          "excludedFields": null,
          "groups": null,
          "columnsOrder": null,
          "configuration": null,
          "joinSettings": null,
          "connection_id": null,
          "datasetColor": chartColors.blue.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "New users",
          "pointRadius": null,
          "formula": null,
          "groupBy": null,
          "sort": null,
          "averageByTotal": false,
          "order": 0,
          "maxRecords": null,
        },
        {
          "td_id": 4,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].date": "date",
            "root[].activeUsers": "number"
          },
          "excludedFields": null,
          "groups": null,
          "columnsOrder": null,
          "configuration": null,
          "joinSettings": {
            "joins": []
          },
          "connection_id": null,
          "datasetColor": chartColors.amber.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Active users",
          "pointRadius": null,
          "formula": null,
          "groupBy": null,
          "sort": null,
          "averageByTotal": false,
          "order": 1,
          "maxRecords": null,
        }
      ],
      "tid": 1
    },
    {
      "name": "Bounce rate",
      "type": "kpi",
      "subType": "lcTimeseries",
      "public": false,
      "shareable": false,
      "chartSize": 1,
      "displayLegend": false,
      "pointRadius": null,
      "dataLabels": false,
      "startDate": null,
      "endDate": null,
      "dateVarsFormat": null,
      "includeZeros": true,
      "currentEndDate": false,
      "fixedStartDate": false,
      "timeInterval": "day",
      "autoUpdate": 86400,
      "draft": false,
      "maxValue": null,
      "minValue": null,
      "disabledExport": null,
      "onReport": false,
      "xLabelTicks": "default",
      "stacked": false,
      "horizontal": false,
      "showGrowth": false,
      "layout": {
        "xxs": [0, 0, 2, 2], "xs": [0, 4, 6, 2], "sm": [6, 0, 2, 2], "md": [8, 0, 2, 2], "lg": [9, 0, 3, 2]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 5,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].bounceRate": "string"
          },
          "datasetColor": chartColors.blue.rgb,
          "fill": false,
          "multiFill": false,
          "legend": "Bounce rate",
          "formula": "{val * 100}%",
          "averageByTotal": false,
          "order": 0,
        }
      ],
      "tid": 2
    },
    {
      "name": "Events",
      "type": "line",
      "subType": "lcTimeseries",
      "public": false,
      "shareable": false,
      "chartSize": 2,
      "displayLegend": false,
      "pointRadius": null,
      "dataLabels": false,
      "startDate": null,
      "endDate": null,
      "dateVarsFormat": null,
      "includeZeros": true,
      "currentEndDate": false,
      "fixedStartDate": false,
      "timeInterval": "day",
      "autoUpdate": 86400,
      "draft": false,
      "mode": "kpichart",
      "maxValue": null,
      "minValue": null,
      "disabledExport": null,
      "onReport": false,
      "xLabelTicks": "default",
      "stacked": false,
      "horizontal": false,
      "showGrowth": true,
      "layout": {
        "xxs": [0, 0, 2, 2], "xs": [0, 6, 6, 2], "sm": [0, 2, 4, 2], "md": [0, 2, 5, 2], "lg": [0, 2, 5, 2]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 6,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "fieldsSchema": {
            "root[].date": "date",
            "root[].eventCount": "number"
          },
          "datasetColor": chartColors.blue.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Total events",
          "averageByTotal": false,
          "order": 0,
        },
        {
          "td_id": 2,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].date": "date",
            "root[].activeUsers": "number"
          },
          "datasetColor": chartColors.amber.rgb,
          "fill": false,
          "multiFill": false,
          "legend": "Conversions",
          "averageByTotal": false,
          "order": 1,
        }
      ],
      "tid": 3
    },
    {
      "name": "Conversions",
      "type": "kpi",
      "subType": "lcTimeseries",
      "public": false,
      "shareable": false,
      "chartSize": 1,
      "displayLegend": false,
      "pointRadius": null,
      "dataLabels": false,
      "startDate": null,
      "endDate": null,
      "dateVarsFormat": null,
      "includeZeros": true,
      "currentEndDate": false,
      "fixedStartDate": false,
      "timeInterval": "day",
      "autoUpdate": 86400,
      "draft": false,
      "maxValue": null,
      "minValue": null,
      "disabledExport": null,
      "onReport": false,
      "xLabelTicks": "default",
      "stacked": false,
      "horizontal": false,
      "showGrowth": false,
      "layout": {
        "xxs": [0, 0, 2, 2], "xs": [0, 10, 6, 2], "sm": [6, 2, 2, 2], "md": [8, 2, 2, 2], "lg": [8, 2, 2, 2]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 7,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].conversions": "number"
          },
          "datasetColor": chartColors.blue.rgb,
          "fill": false,
          "multiFill": false,
          "legend": "Conversions",
          "averageByTotal": false,
          "order": 0,
        }
      ],
      "tid": 4
    },
    {
      "name": "Engagement",
      "type": "kpi",
      "subType": "timeseries",
      "public": false,
      "shareable": false,
      "chartSize": 1,
      "displayLegend": false,
      "pointRadius": null,
      "dataLabels": false,
      "startDate": null,
      "endDate": null,
      "dateVarsFormat": null,
      "includeZeros": true,
      "currentEndDate": false,
      "fixedStartDate": false,
      "timeInterval": "day",
      "autoUpdate": 86400,
      "draft": false,
      "maxValue": null,
      "minValue": null,
      "disabledExport": null,
      "onReport": false,
      "xLabelTicks": "default",
      "stacked": false,
      "horizontal": false,
      "showGrowth": false,
      "layout": {
        "xxs": [0, 0, 2, 2], "xs": [0, 8, 6, 2], "sm": [4, 2, 2, 2], "md": [5, 2, 3, 2], "lg": [5, 2, 3, 2]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 8,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].userEngagementDuration": "number"
          },
          "datasetColor": chartColors.blue.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Duration (s)",
        },
        {
          "td_id": 9,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].engagementRate": "string"
          },
          "xAxis": "root[].engagementRate",
          "xAxisOperation": null,
          "yAxis": "root[].engagementRate",
          "yAxisOperation": "none",
          "dateField": null,
          "datasetColor": chartColors.amber.rgb,
          "fill": false,
          "multiFill": false,
          "legend": "Engagement rate",
          "formula": "{val * 100}%",
          "averageByTotal": false,
          "order": 1,
        }
      ],
      "tid": 5
    },
    {
      "name": "New users by Country",
      "type": "bar",
      "subType": "lcTimeseries",
      "public": false,
      "shareable": false,
      "chartSize": 2,
      "displayLegend": false,
      "pointRadius": null,
      "dataLabels": true,
      "startDate": null,
      "endDate": null,
      "dateVarsFormat": null,
      "includeZeros": true,
      "currentEndDate": false,
      "fixedStartDate": false,
      "timeInterval": "day",
      "autoUpdate": 86400,
      "draft": false,
      "mode": "chart",
      "maxValue": null,
      "minValue": null,
      "disabledExport": null,
      "onReport": false,
      "xLabelTicks": "showAll",
      "stacked": false,
      "horizontal": true,
      "showGrowth": false,
      "layout": {
        "xxs": [0, 0, 2, 2], "xs": [0, 12, 6, 2], "sm": [0, 4, 4, 3], "md": [0, 4, 4, 3], "lg": [0, 4, 3, 3]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 10,
          "fillColor": [
            chartColors.blue.rgb,
            chartColors.amber.rgb,
            chartColors.teal.rgb,
            chartColors.fuchsia.rgb,
            chartColors.lime.rgb,
            chartColors.deep_fuchsia.rgb,
            chartColors.orange.rgb,
            chartColors.light_purple.rgb,
            chartColors.green.rgb,
            chartColors.rose.rgb,
          ],
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].country": "string",
            "root[].sessions": "number"
          },
          "datasetColor": "rgba(255, 255, 255, 1)",
          "fill": false,
          "multiFill": true,
          "dateFormat": null,
          "legend": "Sessions",
          "pointRadius": null,
          "formula": null,
          "groupBy": null,
          "sort": "",
          "averageByTotal": false,
          "order": 0,
          "maxRecords": 10,
        }
      ],
      "tid": 7
    },
    {
      "name": "Top sources",
      "type": "bar",
      "subType": "lcTimeseries",
      "public": false,
      "shareable": false,
      "chartSize": 2,
      "displayLegend": false,
      "pointRadius": null,
      "dataLabels": true,
      "startDate": null,
      "endDate": null,
      "dateVarsFormat": null,
      "includeZeros": true,
      "currentEndDate": false,
      "fixedStartDate": false,
      "timeInterval": "day",
      "autoUpdate": 86400,
      "draft": false,
      "mode": "chart",
      "maxValue": null,
      "minValue": null,
      "disabledExport": null,
      "onReport": false,
      "xLabelTicks": "default",
      "stacked": false,
      "horizontal": true,
      "showGrowth": false,
      "layout": {
        "xxs": [0, 0, 2, 2], "xs": [0, 14, 6, 2], "sm": [4, 4, 4, 3], "md": [4, 4, 4, 3], "lg": [3, 4, 3, 3]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 11,
          "fillColor": [
            chartColors.blue.rgb,
            chartColors.amber.rgb,
            chartColors.teal.rgb,
            chartColors.fuchsia.rgb,
            chartColors.lime.rgb,
          ],
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].sessionSource": "string",
            "root[].sessions": "number"
          },
          "datasetColor": "rgba(255, 255, 255, 1)",
          "fill": false,
          "multiFill": true,
          "legend": "Sessions",
          "sort": "desc",
          "averageByTotal": false,
          "order": 0,
          "maxRecords": 10,
        }
      ],
      "tid": 8
    },
    {
      "name": "Sessions by device",
      "type": "doughnut",
      "subType": "timeseries",
      "public": false,
      "shareable": false,
      "chartSize": 1,
      "displayLegend": true,
      "pointRadius": null,
      "dataLabels": false,
      "startDate": null,
      "endDate": null,
      "dateVarsFormat": null,
      "includeZeros": true,
      "currentEndDate": false,
      "fixedStartDate": false,
      "timeInterval": "day",
      "autoUpdate": 86400,
      "draft": false,
      "mode": "chart",
      "maxValue": null,
      "minValue": null,
      "disabledExport": null,
      "onReport": false,
      "xLabelTicks": "default",
      "stacked": false,
      "horizontal": false,
      "showGrowth": false,
      "layout": {
        "xxs": [0, 0, 2, 2], "xs": [0, 16, 6, 2], "sm": [0, 7, 3, 2], "md": [8, 4, 2, 2], "lg": [10, 2, 2, 2]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 12,
          "fillColor": [
            chartColors.blue.rgb,
            chartColors.amber.rgb,
          ],
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].deviceCategory": "string",
            "root[].sessions": "number"
          },
          "datasetColor": "rgba(255, 255, 255, 1)",
          "fill": false,
          "multiFill": true,
          "dateFormat": null,
          "legend": "Dataset #1",
          "averageByTotal": false,
          "order": 0,
        }
      ],
      "tid": 8
    },
    {
      "name": "Detailed sessions view",
      "type": "table",
      "subType": "timeseries",
      "public": false,
      "shareable": false,
      "chartSize": 3,
      "displayLegend": false,
      "pointRadius": null,
      "dataLabels": false,
      "startDate": null,
      "endDate": null,
      "dateVarsFormat": null,
      "includeZeros": true,
      "currentEndDate": false,
      "fixedStartDate": false,
      "timeInterval": "day",
      "autoUpdate": 86400,
      "draft": false,
      "mode": "chart",
      "maxValue": null,
      "minValue": null,
      "disabledExport": null,
      "onReport": false,
      "xLabelTicks": "default",
      "stacked": false,
      "horizontal": false,
      "showGrowth": false,
      "layout": {
        "xxs": [0, 0, 2, 2], "xs": [0, 18, 6, 2], "sm": [3, 7, 5, 2], "md": [0, 7, 8, 3], "lg": [6, 4, 6, 3]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 13,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].sessionCampaignName": "string",
            "root[].sessions": "number"
          },
          "datasetColor": chartColors.blue.rgb,
          "fill": false,
          "multiFill": false,
          "legend": "Campaigns",
          "averageByTotal": false,
          "order": 0,
        },
        {
          "td_id": 14,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].sessionMedium": "string",
            "root[].sessions": "number"
          },
          "datasetColor": chartColors.amber.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Mediums",
          "pointRadius": null,
          "averageByTotal": false,
          "order": 0,
        },
        {
          "td_id": 15,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].sessionDefaultChannelGroup": "string",
            "root[].sessions": "number"
          },
          "datasetColor": chartColors.teal.rgb,
          "fill": false,
          "multiFill": false,
          "legend": "Channels",
          "averageByTotal": false,
          "order": 0,
        },
        {
          "td_id": 11,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root[].sessionSource": "string",
            "root[].sessions": "number"
          },
          "datasetColor": chartColors.fuchsia.rgb,
          "fill": false,
          "multiFill": false,
          "legend": "Sources",
          "averageByTotal": false,
          "order": 0,
        }
      ],
      "tid": 9
    }
  ]
});

module.exports.template = template;

module.exports.build = async (teamId, projectId, {
  configuration, charts, connection_id,
}) => {
  if (!configuration || !connection_id) return Promise.reject("Missing required parameters");

  let checkErrored = false;
  const connection = await db.Connection.findOne({
    where: { id: connection_id },
    include: [{ model: db.OAuth }]
  });

  const dataRequest = {
    configuration: {
      accountId: configuration.accountId,
      propertyId: configuration.propertyId,
      viewId: configuration.viewId,
      startDate: "30daysAgo",
      endDate: "yesterday",
      metrics: "ga:users",
    },
  };

  try {
    await googleConnector.getAnalytics(connection.OAuth.refreshToken, dataRequest);
  } catch (error) {
    checkErrored = true;
  }

  if (!connection_id && checkErrored) {
    return Promise.reject(new Error("Request cannot be authenticated"));
  }

  return builder(teamId, projectId, configuration, template, charts, connection_id)
    .catch((err) => {
      if (err && err.message) {
        return Promise.reject(err.message);
      }
      return Promise.reject(err);
    });
};
