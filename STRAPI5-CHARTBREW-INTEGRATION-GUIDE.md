# Strapi 5 + Chartbrew v4 Integration Guide

Complete guide to integrate Strapi 5 with Chartbrew v4 for data visualization and analytics.

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Chartbrew Setup](#chartbrew-setup)
3. [Strapi 5 Configuration](#strapi-5-configuration)
4. [Authentication Setup](#authentication-setup)
5. [Troubleshooting](#troubleshooting)
6. [Testing Connection](#testing-connection)
7. [Common Issues](#common-issues)

## 🔧 Prerequisites

### Required Software
- **Node.js** v16 or higher
- **Docker** and **Docker Compose**
- **Strapi 5** project
- **Chartbrew v4**

### Required Ports
- **Strapi**: 1337
- **Chartbrew Frontend**: 4018
- **Chartbrew API**: 4019
- **MySQL**: 3306
- **Redis**: 6379

## 🚀 Chartbrew Setup

### 1. Install Dependencies

```bash
# Navigate to chartbrew client directory
cd chartbrew/client

# Install cross-platform file copy tool
npm install --save-dev cpy-cli
```

### 2. Fix Package.json Scripts

Update `chartbrew/client/package.json`:

```json
{
  "scripts": {
    "start": "cpy ../.env . && npm run prepareSettings && vite",
    "build": "npm run prepareSettings && vite build",
    "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview",
    "tailwind": "npx tailwindcss -i ./src/input.css -o ./dist/output.css --watch",
    "prepareSettings": "cpy src/config/settings.template.js src/config/settings.js"
  }
}
```

### 3. Environment Configuration

Update `chartbrew/.env` and `chartbrew/server/.env`:

```env
#### PRODUCTION VARS ####
### Database connection parameters
CB_DB_NAME=chartbrew
CB_DB_USERNAME=chartbrew_user
CB_DB_PASSWORD=chartbrew_password_123
CB_DB_HOST=localhost
CB_DB_PORT=3306

# Redis connection parameters
CB_REDIS_HOST=localhost
CB_REDIS_PORT=6379
CB_REDIS_PASSWORD=redis_password_123

### Encryption keys
CB_SECRET=chartbrew_secret_key_2024
CB_ENCRYPTION_KEY=e415a95192a7e3461539da8b3abbc4a1300190fd925e69194f02fed9c7198dfd

### API Configuration - IMPORTANT: Use 0.0.0.0 for external access
CB_API_HOST=0.0.0.0
CB_API_PORT=4019

### Client Configuration
VITE_APP_CLIENT_HOST=http://localhost:4018
VITE_APP_CLIENT_PORT=4018
VITE_APP_API_HOST=http://localhost:4019

#### DEVELOPMENT VARS ####
# Database connection parameters
CB_DB_NAME_DEV=chartbrew
CB_DB_USERNAME_DEV=chartbrew_user
CB_DB_PASSWORD_DEV=chartbrew_password_123
CB_DB_HOST_DEV=localhost
CB_DB_PORT_DEV=3306

# Redis connection parameters
CB_REDIS_HOST_DEV=localhost
CB_REDIS_PORT_DEV=6379
CB_REDIS_PASSWORD_DEV=redis_password_123

# Encryption keys
CB_SECRET_DEV=change_to_random_string
CB_ENCRYPTION_KEY_DEV=9a0e928c6e2fa4b8a640f2a26a982b8124fa44724efc8a0fefe58e87019e15f3

# API Configuration - IMPORTANT: Use 0.0.0.0 for external access
CB_API_HOST_DEV=0.0.0.0
CB_API_PORT_DEV=4019

# Client Configuration
VITE_APP_CLIENT_HOST_DEV=http://localhost:4018
VITE_APP_CLIENT_PORT_DEV=4018
VITE_APP_API_HOST_DEV=http://localhost:4019
```

### 4. Vite Configuration

Update `chartbrew/client/vite.config.js`:

```javascript
import { defineConfig, loadEnv } from "vite"
import react from "@vitejs/plugin-react-swc"

export default ({ mode }) => {
  // Load app-level env vars to node-level env vars.
  process.env = { ...process.env, ...loadEnv(mode, `${process.cwd()}/..`) };

  let port = 4018;
  if (process.env.NODE_ENV === "production" && process.env.VITE_APP_CLIENT_PORT) {
    port = process.env.VITE_APP_CLIENT_PORT;
  } else if (process.env.NODE_ENV !== "production" && process.env.VITE_APP_CLIENT_PORT_DEV) {
    port = process.env.VITE_APP_CLIENT_PORT_DEV;
  }

  process.env.VITE_APP_VERSION = process.env.npm_package_version;

  return defineConfig({
    plugins: [react()],
    server: {
      port,
      host: "0.0.0.0", // Allow external connections
      cors: true, // Enable CORS
    },
    preview: {
      port,
      host: "0.0.0.0"
    },
  });
};
```

### 5. Start Chartbrew Services

```bash
# Start database and Redis with Docker
docker-compose up -d db redis

# Start API server
cd chartbrew/server
npm run start-dev

# Start frontend (in new terminal)
cd chartbrew/client
npm run start
```

## ⚙️ Strapi 5 Configuration

### 1. Middleware Configuration

Create/update `backend/config/middlewares.ts`:

```typescript
export default [
  'strapi::logger',
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': [
            "'self'", 
            'http:', 
            'https:', 
            'http://localhost:*',
            'http://127.0.0.1:*',
            'ws://localhost:*',
            'wss://localhost:*'
          ],
          'frame-src': [
            "'self'", 
            'http://localhost:*',
            'http://127.0.0.1:*'
          ],
          'img-src': [
            "'self'", 
            'data:', 
            'blob:', 
            'http:', 
            'https:'
          ],
          'script-src': [
            "'self'",
            "'unsafe-inline'",
            "'unsafe-eval'",
            'http://localhost:*',
            'http://127.0.0.1:*'
          ],
          upgradeInsecureRequests: null,
        },
      },
    },
  },
  {
    name: 'strapi::cors',
    config: {
      enabled: true,
      headers: ['*'],
      origin: [
        'http://localhost:1337',
        'http://127.0.0.1:1337',
        'http://localhost:3000',
        'http://127.0.0.1:3000', 
        'http://localhost:4018', // Chartbrew frontend
        'http://127.0.0.1:4018',
        'http://localhost:4019', // Chartbrew API
        'http://127.0.0.1:4019',
        // Add your production domains here
      ],
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
      credentials: true,
    }
  },
  'strapi::poweredBy',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
```

### 2. Restart Strapi

```bash
# Stop Strapi (Ctrl+C)
# Then restart
npm run develop
# or
yarn develop
```

## 🔐 Authentication Setup

### 1. Generate Chartbrew API Token

1. Open http://localhost:4018 in your browser
2. Create an account or sign in to Chartbrew
3. Navigate to your team settings (click on profile/team name)
4. Go to "API Keys" or "Integrations" section
5. Click "Generate new API key" or "Create API Token"
6. **Copy the generated token immediately** (you won't see it again)

### 2. Configure Connection in Strapi Plugin

Use these exact values in your Strapi Chartbrew plugin:

- **Chartbrew application host (frontend URL)**: `http://localhost:4018`
- **Chartbrew API host (backend URL)**: `http://localhost:4019`
- **Your Strapi backend URL**: `http://localhost:1337`
- **Chartbrew API Token**: `[Your generated token from step 1]`

## 🧪 Testing Connection

### 1. Verify Services Are Running

```bash
# Check if all services respond
curl http://localhost:4019  # Should return "Welcome to chartBrew server API"
curl http://localhost:4018  # Should return HTML
curl http://localhost:1337  # Should return Strapi response
```

### 2. Test Connectivity Script

Create `test-connectivity.js`:

```javascript
const http = require('http');

function testConnection(host, port, path = '/') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: host,
      port: port,
      path: path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      resolve({ status: res.statusCode });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function runTests() {
  const tests = [
    { name: 'Chartbrew API', host: 'localhost', port: 4019 },
    { name: 'Chartbrew Frontend', host: 'localhost', port: 4018 },
    { name: 'Strapi Backend', host: 'localhost', port: 1337 },
  ];

  for (const test of tests) {
    try {
      const result = await testConnection(test.host, test.port);
      console.log(`✅ ${test.name}: Status ${result.status}`);
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
    }
  }
}

runTests();
```

Run with: `node test-connectivity.js`

## 🔧 Troubleshooting

### Critical Authentication Fixes

#### "Authentication failed" Error - Step by Step Fix

**MOST COMMON ISSUE**: API server binding to localhost only

1. **Fix API Host Binding** (CRITICAL):
   ```bash
   # Edit chartbrew/.env and chartbrew/server/.env
   # Change this line:
   CB_API_HOST_DEV=localhost
   # To this:
   CB_API_HOST_DEV=0.0.0.0
   ```

2. **Restart Chartbrew API Server**:
   ```bash
   # Kill current server process (Ctrl+C)
   cd chartbrew/server
   npm run start-dev
   ```

3. **Verify API Accessibility**:
   ```bash
   # Both should work:
   curl http://localhost:4019
   curl http://127.0.0.1:4019
   ```

4. **Generate Fresh API Token**:
   - Go to http://localhost:4018
   - Login to Chartbrew
   - Team Settings → API Keys
   - Delete old token, create new one
   - Copy token immediately

5. **Use Exact URLs in Strapi Plugin**:
   - Frontend: `http://localhost:4018`
   - API: `http://localhost:4019`
   - Strapi: `http://localhost:1337`

### Common Authentication Errors

#### "Something went wrong with authentication" Error

**Root Causes & Solutions:**
1. **Invalid API token** → Regenerate token in Chartbrew
2. **API server not accessible** → Fix host binding to 0.0.0.0
3. **CORS blocking requests** → Update Strapi middleware
4. **Wrong URLs** → Use exact URLs as specified
5. **Services not running** → Verify all services are up

#### "Connection Refused" Error

**Causes:**
1. Service not running
2. Wrong port
3. Firewall blocking connection

**Solutions:**
1. **Check Service Status**: Verify all services are running
2. **Port Verification**: Ensure ports 1337, 4018, 4019 are available
3. **Firewall**: Allow connections on required ports

### Network Issues

#### API Not Accessible from 127.0.0.1

**Solution**: Ensure `CB_API_HOST_DEV=0.0.0.0` in environment files

#### CORS Errors in Browser Console

**Solution**: Update Strapi middleware configuration as shown above

## ✅ Verification Checklist

Before testing authentication:

- [ ] Docker containers running (MySQL, Redis)
- [ ] Chartbrew API server running on port 4019
- [ ] Chartbrew frontend running on port 4018
- [ ] Strapi running on port 1337
- [ ] Environment files updated with `0.0.0.0` for API host
- [ ] Strapi middleware configuration updated
- [ ] Valid API token generated from Chartbrew
- [ ] All URLs exactly as specified in configuration

## 🐛 Debug Authentication Process

### Step-by-Step Authentication Debug

1. **Test API Endpoint Directly**:
   ```bash
   # Test if API is accessible
   curl -v http://localhost:4019
   curl -v http://127.0.0.1:4019

   # Should return: "Welcome to chartBrew server API"
   ```

2. **Test API with Token**:
   ```bash
   # Replace YOUR_TOKEN with actual token
   curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:4019/api/v1/teams
   ```

3. **Check Browser Network Tab**:
   - Open browser dev tools (F12)
   - Go to Network tab
   - Try authentication in Strapi plugin
   - Look for failed requests (red entries)
   - Check request headers and response

4. **Common Network Errors**:
   - `ERR_CONNECTION_REFUSED` → Service not running
   - `CORS error` → Middleware configuration issue
   - `401 Unauthorized` → Invalid token
   - `404 Not Found` → Wrong URL

### Authentication Flow Debug

The authentication process works like this:
1. Strapi plugin sends request to Chartbrew API
2. Chartbrew API validates the token
3. If valid, returns success response
4. If invalid, returns error

**Debug each step:**
```bash
# 1. Check if Strapi can reach Chartbrew API
curl -v http://localhost:4019 -H "Origin: http://localhost:1337"

# 2. Test token validation
curl -v http://localhost:4019/api/v1/teams \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Origin: http://localhost:1337"
```

## 📞 Support

If authentication still fails after following this guide:

1. **Check Browser Console**: Look for CORS or network errors
2. **Check Server Logs**: Review Strapi and Chartbrew logs for errors
3. **Verify Token**: Ensure the API token is valid and not expired
4. **Test Connectivity**: Use the test script to verify all services are accessible
5. **Check API Binding**: Ensure API server binds to 0.0.0.0, not localhost
6. **Verify Middleware**: Ensure Strapi middleware allows connections to Chartbrew

## 🔄 Quick Reset

If you need to start fresh:

```bash
# Stop all services
docker-compose down
# Kill Node processes
pkill -f node

# Restart everything
docker-compose up -d db redis
cd chartbrew/server && npm run start-dev &
cd chartbrew/client && npm run start &
cd backend && npm run develop
```

---

**Last Updated**: December 2024  
**Chartbrew Version**: v4.0.1  
**Strapi Version**: 5.x  
**Node.js Version**: 16+
