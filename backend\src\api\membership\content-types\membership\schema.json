{"kind": "collectionType", "collectionName": "memberships", "info": {"singularName": "membership", "pluralName": "memberships", "displayName": "Membership", "description": "User membership plans and subscriptions"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "richtext"}, "price": {"type": "decimal", "required": true, "default": 0}, "currency": {"type": "enumeration", "enum": ["USD", "EUR", "GBP", "CAD", "AUD"], "default": "USD", "required": true}, "duration": {"type": "enumeration", "enum": ["monthly", "quarterly", "yearly", "lifetime"], "default": "monthly", "required": true}, "features": {"type": "json", "default": []}, "maxProperties": {"type": "integer", "default": 10}, "maxImages": {"type": "integer", "default": 5}, "canCreateProjects": {"type": "boolean", "default": false}, "canAccessAnalytics": {"type": "boolean", "default": false}, "canUsePremiumFilters": {"type": "boolean", "default": false}, "priority": {"type": "integer", "default": 0}, "isActive": {"type": "boolean", "default": true}, "users": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user", "mappedBy": "membership"}}}