'use client';

import React, { useState } from 'react';
import { Building, Home, Building2, Crown, Warehouse, TreePine, ShoppingBag, Check, X } from 'lucide-react';

interface PropertyTypeFilterProps {
  selectedTypes: string[];
  onTypesChange: (types: string[]) => void;
  className?: string;
  multiSelect?: boolean;
}

const PROPERTY_TYPES = [
  { 
    value: 'apartment', 
    label: 'Apartment', 
    icon: Building,
    description: 'Multi-unit residential building'
  },
  { 
    value: 'villa', 
    label: 'Villa', 
    icon: Home,
    description: 'Standalone luxury house'
  },
  { 
    value: 'townhouse', 
    label: 'Townhouse', 
    icon: Building2,
    description: 'Multi-story attached house'
  },
  { 
    value: 'penthouse', 
    label: 'Penthouse', 
    icon: Crown,
    description: 'Top-floor luxury apartment'
  },
  { 
    value: 'studio', 
    label: 'Studio', 
    icon: Home,
    description: 'Single-room living space'
  },
  { 
    value: 'duplex', 
    label: 'Duplex', 
    icon: Building2,
    description: 'Two-level apartment'
  },
  { 
    value: 'land', 
    label: 'Land', 
    icon: TreePine,
    description: 'Vacant land for development'
  },
  { 
    value: 'commercial', 
    label: 'Commercial', 
    icon: ShoppingBag,
    description: 'Business and retail spaces'
  },
];

export const PropertyTypeFilter: React.FC<PropertyTypeFilterProps> = ({
  selectedTypes,
  onTypesChange,
  className = '',
  multiSelect = true,
}) => {
  const [showAll, setShowAll] = useState(false);
  const displayTypes = showAll ? PROPERTY_TYPES : PROPERTY_TYPES.slice(0, 4);

  const handleTypeToggle = (typeValue: string) => {
    if (multiSelect) {
      if (selectedTypes.includes(typeValue)) {
        onTypesChange(selectedTypes.filter(type => type !== typeValue));
      } else {
        onTypesChange([...selectedTypes, typeValue]);
      }
    } else {
      onTypesChange(selectedTypes.includes(typeValue) ? [] : [typeValue]);
    }
  };

  const clearAllTypes = () => {
    onTypesChange([]);
  };

  const selectAllTypes = () => {
    onTypesChange(PROPERTY_TYPES.map(type => type.value));
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          <Home className="inline h-4 w-4 mr-1 text-gray-500" />
          Property Type {multiSelect && '(Multiple Selection)'}
        </label>
        {multiSelect && selectedTypes.length > 0 && (
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={selectAllTypes}
              className="text-xs text-blue-600 hover:text-blue-700"
            >
              Select All
            </button>
            <button
              type="button"
              onClick={clearAllTypes}
              className="text-xs text-red-600 hover:text-red-700"
            >
              Clear All
            </button>
          </div>
        )}
      </div>

      {/* Property Type Grid */}
      <div className="grid grid-cols-2 md:grid-cols-2 gap-3">
        {displayTypes.map((type) => {
          const Icon = type.icon;
          const isSelected = selectedTypes.includes(type.value);
          
          return (
            <button
              key={type.value}
              type="button"
              onClick={() => handleTypeToggle(type.value)}
              className={`relative p-4 border-2 rounded-lg transition-all duration-200 text-left group ${
                isSelected
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              {/* Selection indicator */}
              {isSelected && (
                <div className="absolute top-2 right-2 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                  <Check className="w-3 h-3 text-white" />
                </div>
              )}

              <div className="flex flex-col items-center space-y-2">
                <Icon className={`w-6 h-6 ${isSelected ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'}`} />
                <div className="text-center">
                  <div className={`text-sm font-medium ${isSelected ? 'text-blue-700' : 'text-gray-900'}`}>
                    {type.label}
                  </div>
                  <div className={`text-xs ${isSelected ? 'text-blue-600' : 'text-gray-500'}`}>
                    {type.description}
                  </div>
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {/* Show More/Less Button */}
      {PROPERTY_TYPES.length > 4 && (
        <div className="text-center">
          <button
            type="button"
            onClick={() => setShowAll(!showAll)}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            {showAll ? 'Show Less' : `Show All ${PROPERTY_TYPES.length} Types`}
          </button>
        </div>
      )}

      {/* Selected Types Summary */}
      {multiSelect && selectedTypes.length > 0 && (
        <div className="p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-blue-800">
              {selectedTypes.length} type{selectedTypes.length !== 1 ? 's' : ''} selected
            </span>
            <button
              type="button"
              onClick={clearAllTypes}
              className="text-sm text-blue-600 hover:text-blue-700 flex items-center space-x-1"
            >
              <X className="w-3 h-3" />
              <span>Clear</span>
            </button>
          </div>
          <div className="mt-2 flex flex-wrap gap-1">
            {selectedTypes.map(typeValue => {
              const type = PROPERTY_TYPES.find(t => t.value === typeValue);
              return (
                <span
                  key={typeValue}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                >
                  {type?.label}
                  <button
                    type="button"
                    onClick={() => handleTypeToggle(typeValue)}
                    className="ml-1 hover:text-blue-600"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
