const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');

const API_URL = 'http://localhost:1337/api';

async function testSingleProperty() {
  try {
    console.log('🧪 Testing single property creation...');
    
    // Login
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    const token = loginResponse.data.jwt;
    console.log('✅ Logged in successfully');
    
    // Simple property data
    const propertyData = {
      title: 'Test Property with Images',
      description: 'A simple test property to check image upload',
      price: 250000,
      currency: 'USD',
      propertyType: 'apartment',
      offer: 'for-sale',
      bedrooms: 2,
      bathrooms: 1,
      area: 85,
      areaUnit: 'sqm',
      address: '123 Test Street',
      city: 'Sharm El Sheikh',
      country: 'Egypt',
      neighborhood: 'Test Neighborhood',
      coordinates: {
        lat: 27.9158,
        lng: 34.3300
      },
      propertyCode: `TEST-${Date.now()}`,
      isLuxury: false,
      features: ['balcony'],
      furnished: false,
      petFriendly: false,
      views: 0
    };

    console.log('📝 Property data:', JSON.stringify(propertyData, null, 2));

    // Try without images first
    console.log('\n🏠 Creating property without images...');
    try {
      const simpleResponse = await axios.post(`${API_URL}/properties`, {
        data: propertyData
      }, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Property created without images!');
      console.log('Property ID:', simpleResponse.data.data.id);
    } catch (error) {
      console.error('❌ Failed to create property without images:');
      console.error('Error:', error.response?.data?.error?.message || error.message);
      if (error.response?.data?.error?.details) {
        console.error('Details:', JSON.stringify(error.response.data.error.details, null, 2));
      }
    }

    // Try with FormData (like the main script)
    console.log('\n🖼️  Creating property with FormData...');
    try {
      const formData = new FormData();
      formData.append('data', JSON.stringify(propertyData));
      
      const formDataResponse = await axios.post(`${API_URL}/properties`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          ...formData.getHeaders()
        }
      });
      console.log('✅ Property created with FormData!');
      console.log('Property ID:', formDataResponse.data.data.id);
    } catch (error) {
      console.error('❌ Failed to create property with FormData:');
      console.error('Error:', error.response?.data?.error?.message || error.message);
      if (error.response?.data?.error?.details) {
        console.error('Details:', JSON.stringify(error.response.data.error.details, null, 2));
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSingleProperty();
