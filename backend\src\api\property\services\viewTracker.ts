/**
 * Optimized View Tracking Service
 * 
 * This service provides asynchronous, non-blocking view count tracking
 * with batching, error handling, and performance optimizations.
 */

interface ViewTrackingEntry {
  propertyId: string;
  timestamp: number;
  userAgent?: string;
  ip?: string;
  sessionId?: string;
  userId?: string;
}

interface ViewCountCache {
  [propertyId: string]: {
    count: number;
    lastUpdated: number;
    pendingIncrement: number; // Track pending increments for immediate consistency
    displayCount: number; // Delayed count for display (1-minute delay)
    lastDisplayUpdate: number; // When display count was last updated
  };
}

interface SessionTracker {
  [sessionKey: string]: {
    propertyId: string;
    lastViewTime: number;
  };
}



class ViewTracker {
  private viewQueue: ViewTrackingEntry[] = [];
  private viewCache: ViewCountCache = {};
  private sessionTracker: SessionTracker = {};
  private batchSize = 10;
  private flushInterval = 5000; // 5 seconds
  private cacheTimeout = 60000; // 1 minute
  private displayDelay = 60000; // 1 minute delay for display updates
  private sessionTimeout = 30000; // 30 seconds session timeout (reduced from 5 minutes)
  private processing = false;
  private flushTimer: NodeJS.Timeout | null = null;
  private displayUpdateTimer: NodeJS.Timeout | null = null;
  constructor() {
    this.startBatchProcessor();
    this.startDisplayUpdateProcessor();
    this.startSessionCleanup();
    console.log('ViewTracker initialized with enhanced performance optimizations');
  }

  /**
   * Track a property view asynchronously with intelligent anti-spam protection
   * This method returns immediately without waiting for database updates
   */
  async trackView(propertyId: string, userAgent?: string, ip?: string, userId?: string): Promise<boolean> {
    try {
      // Create session key for anti-spam protection
      const sessionKey = this.createSessionKey(propertyId, ip, userAgent, userId);
      const now = Date.now();

      // Check if this is a duplicate view within session timeout
      const lastView = this.sessionTracker[sessionKey];
      const isSpamView = lastView && (now - lastView.lastViewTime) < this.sessionTimeout;

      if (isSpamView) {
        console.log(`⚠️  Rapid view detected for property ${propertyId} (${Math.round((now - lastView.lastViewTime) / 1000)}s ago) - throttling but maintaining count display`);
        return false; // Indicate view was not counted
      }

      // Detect potential bot traffic
      const isBotRequest = this.detectBotTraffic(userAgent, ip);
      if (isBotRequest) {
        console.log(`🤖 Bot traffic detected for property ${propertyId} - skipping increment`);
        return false;
      }

      // Update session tracker
      this.sessionTracker[sessionKey] = {
        propertyId,
        lastViewTime: now
      };

      // Add to queue for batch processing
      this.viewQueue.push({
        propertyId,
        timestamp: now,
        userAgent,
        ip,
        sessionId: sessionKey,
        userId
      });

      // Update local cache immediately for fast reads
      this.updateCache(propertyId);

      // Trigger batch processing if queue is full
      if (this.viewQueue.length >= this.batchSize) {
        // Process batch asynchronously without blocking
        setImmediate(() => this.processBatch());
      }

      console.log(`✅ Legitimate view tracked for property ${propertyId} (session: ${sessionKey.substring(0, 8)}...)`);
      return true; // Indicate view was counted
    } catch (error) {
      console.error('Error tracking view:', error);
      // Don't throw error to avoid breaking the main request
      return false;
    }
  }

  /**
   * Get display view count for a property (with 1-minute delay)
   * Returns delayed count for display to prevent real-time inflation
   */
  async getDisplayViewCount(propertyId: string): Promise<number> {
    try {
      const cached = this.viewCache[propertyId];
      const now = Date.now();

      // Return display count if available and not too old
      if (cached && cached.displayCount !== undefined &&
          (now - cached.lastDisplayUpdate) < this.cacheTimeout) {
        return cached.displayCount;
      }

      // Fetch from database if not cached
      const actualCount = await this.getActualViewCount(propertyId);

      // Initialize display count
      if (!cached) {
        this.viewCache[propertyId] = {
          count: actualCount,
          pendingIncrement: 0,
          lastUpdated: now,
          displayCount: actualCount,
          lastDisplayUpdate: now
        };
      } else {
        cached.displayCount = actualCount;
        cached.lastDisplayUpdate = now;
      }

      return actualCount;
    } catch (error) {
      console.error('Error getting display view count:', error);
      return 0;
    }
  }

  /**
   * Get actual view count for internal use (includes pending increments)
   */
  async getActualViewCount(propertyId: string): Promise<number> {
    try {
      // Check cache first
      const cached = this.viewCache[propertyId];
      if (cached && (Date.now() - cached.lastUpdated) < this.cacheTimeout) {
        return cached.count; // This already includes pending increments
      }

      // Fetch from database if not cached or expired using database query
      let property = null;
      try {
        const properties = await strapi.db.query('api::property.property').findMany({
          where: { documentId: propertyId },
          select: ['views']
        });
        property = properties.length > 0 ? properties[0] : null;
      } catch (error) {
        console.error('Error fetching property for view count:', error);
      }

      const viewCount = property?.views || 0;

      // Update cache
      if (!this.viewCache[propertyId]) {
        this.viewCache[propertyId] = {
          count: viewCount,
          pendingIncrement: 0,
          lastUpdated: Date.now(),
          displayCount: viewCount,
          lastDisplayUpdate: Date.now()
        };
      } else {
        this.viewCache[propertyId].count = viewCount;
        this.viewCache[propertyId].lastUpdated = Date.now();
      }

      return viewCount;
    } catch (error) {
      console.error('Error getting actual view count:', error);
      return 0;
    }
  }

  /**
   * Create a session key for anti-spam protection
   */
  private createSessionKey(propertyId: string, ip?: string, userAgent?: string, userId?: string): string {
    const components = [propertyId, ip || 'unknown', userId || 'anonymous'];
    // Add a hash of user agent to make it more unique without storing full UA
    if (userAgent) {
      const uaHash = userAgent.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0);
        return a & a;
      }, 0);
      components.push(uaHash.toString());
    }
    return components.join('|');
  }

  /**
   * Detect bot traffic to prevent artificial view inflation
   */
  private detectBotTraffic(userAgent?: string, _ip?: string): boolean {
    if (!userAgent) return false;

    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /googlebot/i, /bingbot/i, /slurp/i, /duckduckbot/i,
      /facebookexternalhit/i, /twitterbot/i, /linkedinbot/i,
      /whatsapp/i, /telegram/i, /curl/i, /wget/i, /python/i,
      /postman/i, /insomnia/i, /httpie/i
    ];

    return botPatterns.some(pattern => pattern.test(userAgent));
  }



  /**
   * Update local cache for immediate read consistency
   */
  private updateCache(propertyId: string): void {
    const now = Date.now();
    if (this.viewCache[propertyId]) {
      this.viewCache[propertyId].count += 1;
      this.viewCache[propertyId].pendingIncrement += 1;
      this.viewCache[propertyId].lastUpdated = now;
    } else {
      // If not in cache, we'll fetch it when needed
      this.viewCache[propertyId] = {
        count: 1, // Assume at least 1 view
        pendingIncrement: 1,
        lastUpdated: now,
        displayCount: 0, // Will be updated by display processor
        lastDisplayUpdate: 0
      };
    }
  }

  /**
   * Start the batch processor that periodically flushes view updates
   */
  private startBatchProcessor(): void {
    this.flushTimer = setInterval(() => {
      if (this.viewQueue.length > 0) {
        // Process batch asynchronously
        setImmediate(() => this.processBatch());
      }
    }, this.flushInterval);
  }

  /**
   * Start the display update processor (1-minute delay)
   */
  private startDisplayUpdateProcessor(): void {
    this.displayUpdateTimer = setInterval(() => {
      this.updateDisplayCounts();
    }, this.displayDelay);
  }

  /**
   * Start session cleanup to prevent memory leaks
   */
  private startSessionCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, this.sessionTimeout);
  }

  /**
   * Process a batch of view updates with enhanced logging and error handling
   */
  private async processBatch(): Promise<void> {
    if (this.processing || this.viewQueue.length === 0) {
      return;
    }

    this.processing = true;
    const startTime = Date.now();

    try {
      // Extract batch from queue
      const batch = this.viewQueue.splice(0, this.batchSize);
      console.log(`Processing batch of ${batch.length} view updates`);

      // Group by property ID to aggregate multiple views
      const viewCounts: { [propertyId: string]: number } = {};

      batch.forEach(entry => {
        viewCounts[entry.propertyId] = (viewCounts[entry.propertyId] || 0) + 1;
      });

      console.log(`Aggregated views for ${Object.keys(viewCounts).length} properties`);

      // Update database for each property using Strapi v5 compatible approach
      const updatePromises = Object.entries(viewCounts).map(async ([propertyId, incrementBy]) => {
        try {
          // Step 1: Find property using database query (most reliable for documentId lookup)
          let properties = [];
          try {
            properties = await strapi.db.query('api::property.property').findMany({
              where: { documentId: propertyId },
              select: ['id', 'views', 'documentId', 'title']
            });
          } catch (findError) {
            console.error(`Error finding property ${propertyId}:`, findError);
            return { success: false, propertyId, error: findError };
          }

          if (properties.length === 0) {
            console.warn(`✗ Property ${propertyId} not found using documentId lookup, skipping view count update`);
            return { success: false, propertyId, error: 'Property not found' };
          }

          const existingProperty = properties[0];
          const oldViews = existingProperty.views || 0;
          const newViews = oldViews + incrementBy;

          // Validation: Ensure view count never decreases unexpectedly
          if (newViews < oldViews) {
            console.error(`⚠️  View count validation failed for property ${propertyId}: attempted to decrease from ${oldViews} to ${newViews}`);
            return { success: false, propertyId, error: 'View count validation failed - count would decrease' };
          }

          // Additional validation: Prevent unrealistic increments
          if (incrementBy > 100) {
            console.warn(`⚠️  Suspicious view increment for property ${propertyId}: +${incrementBy} views in single batch`);
          }

          // Step 2: Update using database query for better compatibility
          await strapi.db.query('api::property.property').update({
            where: { id: existingProperty.id },
            data: { views: newViews }
          });

          console.log(`✓ Updated view count for property ${propertyId} (${existingProperty.title}): ${oldViews} → ${newViews} (+${incrementBy})`);

          // Reset pending increment in cache after successful update
          if (this.viewCache[propertyId]) {
            this.viewCache[propertyId].pendingIncrement = Math.max(0,
              this.viewCache[propertyId].pendingIncrement - incrementBy);
            // Update actual count in cache
            this.viewCache[propertyId].count = newViews;
          }



          return { success: true, propertyId, incrementBy, oldViews, newViews };
        } catch (error) {
          console.error(`✗ Failed to update view count for property ${propertyId}:`, error);
          // Re-queue failed updates for retry
          this.viewQueue.push({
            propertyId,
            timestamp: Date.now()
          });
          return { success: false, propertyId, error };
        }
      });

      const results = await Promise.allSettled(updatePromises);
      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failed = results.length - successful;

      const processingTime = Date.now() - startTime;
      console.log(`Batch processing completed in ${processingTime}ms: ${successful} successful, ${failed} failed`);

    } catch (error) {
      console.error('Error processing view batch:', error);
    } finally {
      this.processing = false;
    }
  }

  /**
   * Update display counts with 1-minute delay
   */
  private updateDisplayCounts(): void {
    const now = Date.now();
    let updatedCount = 0;

    Object.entries(this.viewCache).forEach(([_propertyId, cache]) => {
      // Only update display count if actual count has changed and enough time has passed
      if (cache.count !== cache.displayCount &&
          (now - cache.lastDisplayUpdate) >= this.displayDelay) {
        cache.displayCount = cache.count;
        cache.lastDisplayUpdate = now;
        updatedCount++;
      }
    });

    if (updatedCount > 0) {
      console.log(`Updated display counts for ${updatedCount} properties`);
    }
  }

  /**
   * Clean up expired sessions to prevent memory leaks
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    let cleanedCount = 0;

    Object.keys(this.sessionTracker).forEach(sessionKey => {
      const session = this.sessionTracker[sessionKey];
      if ((now - session.lastViewTime) > this.sessionTimeout) {
        delete this.sessionTracker[sessionKey];
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      console.log(`Cleaned up ${cleanedCount} expired sessions`);
    }
  }

  /**
   * Force flush all pending view updates
   */
  async flush(): Promise<void> {
    console.log('Flushing all pending view updates...');
    while (this.viewQueue.length > 0) {
      await this.processBatch();
    }
    console.log('All view updates flushed');
  }

  /**
   * Clear cache for a specific property
   */
  clearCache(propertyId?: string): void {
    if (propertyId) {
      delete this.viewCache[propertyId];
    } else {
      this.viewCache = {};
    }
  }



  /**
   * Cleanup resources
   */
  destroy(): void {
    console.log('Destroying ViewTracker...');

    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }

    if (this.displayUpdateTimer) {
      clearInterval(this.displayUpdateTimer);
      this.displayUpdateTimer = null;
    }

    // Final flush before destroying
    this.flush();
    console.log('ViewTracker destroyed');
  }
}

// Create singleton instance
const viewTracker = new ViewTracker();

// Graceful shutdown handling
process.on('SIGTERM', () => {
  viewTracker.destroy();
});

process.on('SIGINT', () => {
  viewTracker.destroy();
});

export default viewTracker;
