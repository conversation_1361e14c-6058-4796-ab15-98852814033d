#!/usr/bin/env node

/**
 * Chartbrew Authentication Debug Script
 * Run this to diagnose authentication issues between Strapi and Chartbrew
 * 
 * Usage: node debug-chartbrew-auth.js [API_TOKEN]
 */

const http = require('http');
const https = require('https');
const url = require('url');

// Configuration
const CHARTBREW_API = 'http://localhost:4019';
const CHARTBREW_FRONTEND = 'http://localhost:4018';
const STRAPI_BACKEND = 'http://localhost:1337';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(requestUrl, options = {}) {
  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(requestUrl);
    const client = parsedUrl.protocol === 'https:' ? https : http;
    
    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.path,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 5000
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data.substring(0, 200) // Limit response data
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function testBasicConnectivity() {
  log('\n🔍 Testing Basic Connectivity...', 'bold');
  
  const tests = [
    { name: 'Chartbrew API (localhost)', url: `${CHARTBREW_API}` },
    { name: 'Chartbrew API (127.0.0.1)', url: 'http://127.0.0.1:4019' },
    { name: 'Chartbrew Frontend', url: `${CHARTBREW_FRONTEND}` },
    { name: 'Strapi Backend', url: `${STRAPI_BACKEND}` },
  ];

  for (const test of tests) {
    try {
      const result = await makeRequest(test.url);
      if (result.status === 200) {
        log(`✅ ${test.name}: OK (${result.status})`, 'green');
      } else {
        log(`⚠️  ${test.name}: Status ${result.status}`, 'yellow');
      }
    } catch (error) {
      log(`❌ ${test.name}: ${error.message}`, 'red');
    }
  }
}

async function testCORS() {
  log('\n🌐 Testing CORS Configuration...', 'bold');
  
  try {
    const result = await makeRequest(`${CHARTBREW_API}`, {
      headers: {
        'Origin': STRAPI_BACKEND,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Authorization'
      }
    });
    
    if (result.headers['access-control-allow-origin']) {
      log(`✅ CORS: Origin allowed`, 'green');
    } else {
      log(`❌ CORS: No Access-Control-Allow-Origin header`, 'red');
    }
  } catch (error) {
    log(`❌ CORS Test Failed: ${error.message}`, 'red');
  }
}

async function testAPIToken(token) {
  if (!token) {
    log('\n⚠️  No API token provided. Skipping token tests.', 'yellow');
    log('Usage: node debug-chartbrew-auth.js YOUR_API_TOKEN', 'blue');
    return;
  }

  log('\n🔑 Testing API Token Authentication...', 'bold');
  
  const endpoints = [
    '/api/v1/teams',
    '/api/v1/user',
    '/api/v1/projects',
    '/api/v1/properties',
    '/api/properties'
  ];

  for (const endpoint of endpoints) {
    try {
      const result = await makeRequest(`${CHARTBREW_API}${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Origin': STRAPI_BACKEND
        }
      });
      
      if (result.status === 200) {
        log(`✅ ${endpoint}: Authenticated successfully`, 'green');
      } else if (result.status === 401) {
        log(`❌ ${endpoint}: Invalid token (401)`, 'red');
      } else {
        log(`⚠️  ${endpoint}: Status ${result.status}`, 'yellow');
      }
    } catch (error) {
      log(`❌ ${endpoint}: ${error.message}`, 'red');
    }
  }
}

async function checkEnvironmentConfig() {
  log('\n⚙️  Environment Configuration Check...', 'bold');
  
  // Check if we can determine the API host binding
  try {
    // Test both localhost and 127.0.0.1
    const localhostTest = await makeRequest('http://localhost:4019');
    const ipTest = await makeRequest('http://127.0.0.1:4019');
    
    if (localhostTest.status === 200 && ipTest.status === 200) {
      log('✅ API server binds to all interfaces (0.0.0.0)', 'green');
    } else if (localhostTest.status === 200 && !ipTest.status) {
      log('❌ API server only binds to localhost', 'red');
      log('   Fix: Set CB_API_HOST_DEV=0.0.0.0 in .env files', 'yellow');
    }
  } catch (error) {
    log('❌ Could not determine API binding configuration', 'red');
  }
}

function printDiagnosticInfo() {
  log('\n📋 Diagnostic Information:', 'bold');
  log(`Node.js Version: ${process.version}`, 'blue');
  log(`Platform: ${process.platform}`, 'blue');
  log(`Current Directory: ${process.cwd()}`, 'blue');
  
  log('\n🔧 Required Configuration:', 'bold');
  log('1. Chartbrew .env files should have:', 'blue');
  log('   CB_API_HOST_DEV=0.0.0.0', 'blue');
  log('   CB_API_PORT_DEV=4019', 'blue');
  
  log('\n2. Strapi middlewares.ts should include:', 'blue');
  log('   CORS origin: http://localhost:4019', 'blue');
  log('   CSP connect-src: http://localhost:*', 'blue');
  
  log('\n3. Required URLs for Strapi plugin:', 'blue');
  log('   Frontend: http://localhost:4018', 'blue');
  log('   API: http://localhost:4019', 'blue');
  log('   Strapi: http://localhost:1337', 'blue');
}

async function main() {
  const apiToken = process.argv[2];
  
  log('🚀 Chartbrew Authentication Diagnostic Tool', 'bold');
  log('='.repeat(50), 'blue');
  
  await testBasicConnectivity();
  await testCORS();
  await testAPIToken(apiToken);
  await checkEnvironmentConfig();
  printDiagnosticInfo();
  
  log('\n✨ Diagnostic Complete!', 'bold');
  log('\nIf issues persist:', 'yellow');
  log('1. Ensure all services are running', 'yellow');
  log('2. Check CB_API_HOST_DEV=0.0.0.0 in .env files', 'yellow');
  log('3. Restart Chartbrew API server after config changes', 'yellow');
  log('4. Generate a fresh API token in Chartbrew dashboard', 'yellow');
}

// Run the diagnostic
main().catch(console.error);
