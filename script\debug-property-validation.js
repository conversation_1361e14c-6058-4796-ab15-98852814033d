const axios = require('axios');

const API_URL = 'http://localhost:1337/api';

async function debugPropertyValidation() {
  console.log('🔍 Debugging Property Validation Errors...\n');

  try {
    // Login first
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    const token = loginResponse.data.jwt;
    console.log('✅ Authentication successful');
    
    // Test 1: Try the exact same data that failed
    console.log('\n1. Testing the exact data that failed...');
    
    const failedData = {
      "data": {
        "title": "Fully test",
        "description": "test",
        "price": 100,
        "currency": "USD",
        "propertyType": "apartment",
        "offer": "for-sale",
        "bedrooms": 1,
        "bathrooms": 1,
        "area": 200,
        "areaUnit": "sqm",
        "address": "florida",
        "city": "ca",
        "country": "ca",
        "neighborhood": [], // This might be an issue
        "coordinates": {
          "lat": 37.6020892,
          "lng": -122.067687
        },
        "propertyCode": "rs1",
        "isLuxury": false,
        "features": ["Swimming Pool", "Balcony"],
        "yearBuilt": null, // This might be an issue
        "parking": null, // This might be an issue
        "furnished": true,
        "petFriendly": false,
        "virtualTour": null // This might be an issue
      }
    };
    
    try {
      const response = await axios.post(`${API_URL}/properties`, failedData, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Property creation successful with exact data');
    } catch (error) {
      console.log('❌ Property creation failed with exact data');
      console.log('Status:', error.response?.status);
      console.log('Error details:', JSON.stringify(error.response?.data, null, 2));
    }
    
    // Test 2: Try with minimal required data
    console.log('\n2. Testing with minimal required data...');
    
    const minimalData = {
      "data": {
        "title": "Minimal Test Property",
        "description": "A minimal test property",
        "price": 100000,
        "currency": "USD",
        "propertyType": "apartment",
        "offer": "for-sale",
        "bedrooms": 1,
        "bathrooms": 1,
        "area": 50,
        "areaUnit": "sqm",
        "address": "123 Test Street",
        "city": "Test City",
        "country": "Test Country"
      }
    };
    
    try {
      const response = await axios.post(`${API_URL}/properties`, minimalData, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Minimal property creation successful');
      console.log('Created property ID:', response.data.data.id);
      
      // Clean up
      await axios.delete(`${API_URL}/properties/${response.data.data.documentId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Test property cleaned up');
      
    } catch (error) {
      console.log('❌ Minimal property creation failed');
      console.log('Status:', error.response?.status);
      console.log('Error details:', JSON.stringify(error.response?.data, null, 2));
    }
    
    // Test 3: Try with problematic fields removed
    console.log('\n3. Testing with problematic fields removed...');
    
    const cleanedData = {
      "data": {
        "title": "Cleaned Test Property",
        "description": "A test property with problematic fields removed",
        "price": 100,
        "currency": "USD",
        "propertyType": "apartment",
        "offer": "for-sale",
        "bedrooms": 1,
        "bathrooms": 1,
        "area": 200,
        "areaUnit": "sqm",
        "address": "florida",
        "city": "ca",
        "country": "ca",
        "coordinates": {
          "lat": 37.6020892,
          "lng": -122.067687
        },
        "propertyCode": "rs1",
        "isLuxury": false,
        "features": ["Swimming Pool", "Balcony"],
        "furnished": true,
        "petFriendly": false
        // Removed: neighborhood (empty array), yearBuilt (null), parking (null), virtualTour (null)
      }
    };
    
    try {
      const response = await axios.post(`${API_URL}/properties`, cleanedData, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Cleaned property creation successful');
      console.log('Created property ID:', response.data.data.id);
      
      // Clean up
      await axios.delete(`${API_URL}/properties/${response.data.data.documentId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Test property cleaned up');
      
    } catch (error) {
      console.log('❌ Cleaned property creation failed');
      console.log('Status:', error.response?.status);
      console.log('Error details:', JSON.stringify(error.response?.data, null, 2));
    }
    
    // Test 4: Check property schema requirements
    console.log('\n4. Checking property schema...');
    
    try {
      // Get an existing property to see the expected structure
      const existingResponse = await axios.get(`${API_URL}/properties/my-properties`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      const properties = existingResponse.data.data || existingResponse.data;
      if (properties.length > 0) {
        const sampleProperty = properties[0];
        console.log('✅ Sample property structure:');
        console.log('Required fields present:');
        console.log(`   title: ${sampleProperty.title ? '✅' : '❌'}`);
        console.log(`   description: ${sampleProperty.description ? '✅' : '❌'}`);
        console.log(`   price: ${sampleProperty.price ? '✅' : '❌'}`);
        console.log(`   currency: ${sampleProperty.currency ? '✅' : '❌'}`);
        console.log(`   propertyType: ${sampleProperty.propertyType ? '✅' : '❌'}`);
        console.log(`   offer: ${sampleProperty.offer ? '✅' : '❌'}`);
        console.log(`   bedrooms: ${sampleProperty.bedrooms !== undefined ? '✅' : '❌'}`);
        console.log(`   bathrooms: ${sampleProperty.bathrooms !== undefined ? '✅' : '❌'}`);
        console.log(`   area: ${sampleProperty.area ? '✅' : '❌'}`);
        console.log(`   areaUnit: ${sampleProperty.areaUnit ? '✅' : '❌'}`);
        console.log(`   address: ${sampleProperty.address ? '✅' : '❌'}`);
        console.log(`   city: ${sampleProperty.city ? '✅' : '❌'}`);
        console.log(`   country: ${sampleProperty.country ? '✅' : '❌'}`);
        
        console.log('\nOptional fields:');
        console.log(`   neighborhood: ${sampleProperty.neighborhood !== undefined ? '✅' : '❌'} (${typeof sampleProperty.neighborhood})`);
        console.log(`   yearBuilt: ${sampleProperty.yearBuilt !== undefined ? '✅' : '❌'} (${typeof sampleProperty.yearBuilt})`);
        console.log(`   parking: ${sampleProperty.parking !== undefined ? '✅' : '❌'} (${typeof sampleProperty.parking})`);
        console.log(`   virtualTour: ${sampleProperty.virtualTour !== undefined ? '✅' : '❌'} (${typeof sampleProperty.virtualTour})`);
      }
    } catch (schemaError) {
      console.log('⚠️  Could not retrieve sample property for schema analysis');
    }
    
    console.log('\n📋 VALIDATION ERROR ANALYSIS:');
    console.log('');
    console.log('🔍 LIKELY ISSUES:');
    console.log('   1. Empty array for neighborhood field');
    console.log('   2. Null values for optional fields (yearBuilt, parking, virtualTour)');
    console.log('   3. Frontend sending data as JSON string instead of object');
    console.log('   4. Missing required fields or wrong data types');
    console.log('');
    console.log('🔧 SOLUTIONS:');
    console.log('   1. Remove empty arrays and null values from frontend');
    console.log('   2. Ensure frontend sends proper JSON object structure');
    console.log('   3. Add proper validation on frontend before submission');
    console.log('   4. Handle optional fields properly (omit if empty)');
    
  } catch (error) {
    console.log('❌ Debug failed:', error.message);
  }
}

debugPropertyValidation().catch(console.error);
