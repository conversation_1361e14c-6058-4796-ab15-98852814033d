const axios = require('axios');

const API_URL = 'http://localhost:1337/api';

async function createTestUser() {
  console.log('🔧 Creating test user for dashboard testing...\n');

  try {
    // Try to register a new test user
    console.log('Creating test user...');
    const registerResponse = await axios.post(`${API_URL}/auth/local/register`, {
      username: 'dashboardtest',
      email: '<EMAIL>',
      password: 'Dashboard123!'
    });
    
    console.log('✅ Test user created successfully!');
    console.log(`   Username: dashboardtest`);
    console.log(`   Email: <EMAIL>`);
    console.log(`   Password: Dashboard123!`);
    console.log(`   User ID: ${registerResponse.data.user.id}`);
    console.log(`   JWT Token: ${registerResponse.data.jwt.substring(0, 30)}...`);
    
    // Test the token immediately
    const token = registerResponse.data.jwt;
    const myPropertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('\n✅ Token works! User can access dashboard');
    console.log(`   User has ${myPropertiesResponse.data.data?.length || 0} properties`);
    
    // Store credentials for frontend testing
    console.log('\n📝 Use these credentials to test the frontend dashboard:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: Dashboard123!');
    
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('already taken')) {
      console.log('ℹ️  Test user already exists. Testing login...');
      
      try {
        const loginResponse = await axios.post(`${API_URL}/auth/local`, {
          identifier: '<EMAIL>',
          password: 'Dashboard123!'
        });
        
        console.log('✅ Existing test user login successful!');
        console.log(`   User ID: ${loginResponse.data.user.id}`);
        
        const token = loginResponse.data.jwt;
        const myPropertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        console.log('✅ Dashboard access confirmed');
        console.log(`   User has ${myPropertiesResponse.data.data?.length || 0} properties`);
        
        console.log('\n📝 Use these credentials to test the frontend dashboard:');
        console.log('   Email: <EMAIL>');
        console.log('   Password: Dashboard123!');
        
      } catch (loginError) {
        console.log('❌ Login failed:', loginError.response?.data || loginError.message);
        console.log('\n💡 You may need to create a user manually through the Strapi admin panel');
      }
    } else {
      console.log('❌ User creation failed:', error.response?.data || error.message);
    }
  }
}

createTestUser().catch(console.error);
