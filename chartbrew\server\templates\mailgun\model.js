const request = require("request-promise");

const builder = require("./builder");
const { chartColors } = require("../../charts/colors");

const template = (apiKey, domain, apiRoot) => ({
  "Connections": [{
    "host": `${apiRoot}/v3`,
    "dbName": null,
    "port": null,
    "username": null,
    "password": null,
    "options": [],
    "connectionString": "null",
    "authentication": {
      "type": "basic_auth",
      "user": "api",
      "pass": apiKey,
    },
    "firebaseServiceAccount": null,
    "name": "MailgunAPI",
    "type": "api",
    "subType": "mailgun",
    "active": true,
    "srv": false
  }],
  "Datasets": [{
    "td_id": 1,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root.stats[].time": "date",
      "root.stats[].delivered": "object",
      "root.stats[].delivered.smtp": "number",
      "root.stats[].delivered.http": "number",
      "root.stats[].delivered.optimized": "number",
      "root.stats[].delivered.total": "number"
    },
    "excludedFields": null,
    "query": null,
    "xAxis": "root.stats[].time",
    "xAxisOperation": null,
    "yAxis": "root.stats[].delivered.total",
    "yAxisOperation": "sum",
    "dateField": "root.stats[].time",
    "datasetColor": chartColors.blue.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Delivered",
    "pointRadius": null,
    "formula": null,
    "DataRequests": [{
      "headers": {},
      "body": "null",
      "conditions": null,
      "method": "GET",
      "route": `/${domain}/stats/total?event=delivered`,
      "useGlobalHeaders": true,
      "query": null,
      "pagination": true,
      "items": "",
      "itemsLimit": 1000,
      "offset": "",
      "paginationField": "paging.next",
      "template": "url"
    }],
  }, {
    "td_id": 2,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root.stats[].time": "date",
      "root.stats[].failed": "object",
      "root.stats[].failed.temporary": "object",
      "root.stats[].failed.temporary.espblock": "number",
      "root.stats[].failed.temporary.total": "number",
      "root.stats[].failed.permanent": "object",
      "root.stats[].failed.permanent.suppress-bounce": "number",
      "root.stats[].failed.permanent.suppress-unsubscribe": "number",
      "root.stats[].failed.permanent.suppress-complaint": "number",
      "root.stats[].failed.permanent.bounce": "number",
      "root.stats[].failed.permanent.delayed-bounce": "number",
      "root.stats[].failed.permanent.webhook": "number",
      "root.stats[].failed.permanent.optimized": "number",
      "root.stats[].failed.permanent.total": "number"
    },
    "excludedFields": null,
    "query": null,
    "xAxis": "root.stats[].time",
    "xAxisOperation": null,
    "yAxis": "root.stats[].failed.permanent.total",
    "yAxisOperation": "sum",
    "dateField": "root.stats[].time",
    "datasetColor": chartColors.amber.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Failed",
    "pointRadius": null,
    "formula": null,
    "DataRequests": [{
      "headers": {},
      "body": "null",
      "conditions": null,
      "method": "GET",
      "route": `/${domain}/stats/total?event=failed`,
      "useGlobalHeaders": true,
      "query": null,
      "pagination": true,
      "items": "items",
      "itemsLimit": 1000,
      "offset": "skip",
      "paginationField": "paging.next",
      "template": "url"
    }],
  }, {
    "td_id": 3,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root.items[].address": "string",
      "root.items[].code": "string",
      "root.items[].error": "string",
      "root.items[].created_at": "date",
      "root.items[].MessageHash": "string"
    },
    "excludedFields": null,
    "query": null,
    "xAxis": "root.items[].created_at",
    "xAxisOperation": null,
    "yAxis": "root.items[].MessageHash",
    "yAxisOperation": "count",
    "dateField": "root.items[].created_at",
    "datasetColor": chartColors.teal.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Bounced",
    "pointRadius": null,
    "formula": null,
    "DataRequests": [{
      "headers": {},
      "body": "null",
      "conditions": null,
      "method": "GET",
      "route": `/${domain}/bounces`,
      "useGlobalHeaders": true,
      "query": null,
      "pagination": true,
      "items": "items",
      "itemsLimit": 1000,
      "offset": "offset",
      "paginationField": "paging.next",
      "template": "url"
    }],
  }, {
    "td_id": 4,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root.stats[].time": "date",
      "root.stats[].opened": "object",
      "root.stats[].opened.total": "number"
    },
    "excludedFields": null,
    "query": null,
    "xAxis": "root.stats[].time",
    "xAxisOperation": null,
    "yAxis": "root.stats[].opened.total",
    "yAxisOperation": "sum",
    "dateField": "root.stats[].time",
    "datasetColor": chartColors.teal.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Opened",
    "pointRadius": null,
    "formula": null,
    "DataRequests": [{
      "headers": {},
      "body": "null",
      "conditions": null,
      "method": "GET",
      "route": `/${domain}/stats/total?event=opened`,
      "useGlobalHeaders": true,
      "query": null,
      "pagination": true,
      "items": "items",
      "itemsLimit": 1000,
      "offset": "skip",
      "paginationField": "paging.next",
      "template": "url"
    }],
  }, {
    "td_id": 5,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root.stats[].time": "date",
      "root.stats[].clicked": "object",
      "root.stats[].clicked.total": "number"
    },
    "excludedFields": null,
    "query": null,
    "xAxis": "root.stats[].time",
    "xAxisOperation": null,
    "yAxis": "root.stats[].clicked.total",
    "yAxisOperation": "sum",
    "dateField": "root.stats[].time",
    "datasetColor": chartColors.fuchsia.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Clicked",
    "pointRadius": null,
    "formula": null,
    "DataRequests": [{
      "headers": {},
      "body": "null",
      "conditions": null,
      "method": "GET",
      "route": `/${domain}/stats/total?event=clicked`,
      "useGlobalHeaders": true,
      "query": null,
      "pagination": true,
      "items": "items",
      "itemsLimit": 1000,
      "offset": "skip",
      "paginationField": "paging.next",
      "template": "url"
    }],
  }, {
    "td_id": 6,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root.stats[].time": "date",
      "root.stats[].unsubscribed": "object",
      "root.stats[].unsubscribed.total": "number"
    },
    "excludedFields": null,
    "query": null,
    "xAxis": "root.stats[].time",
    "xAxisOperation": null,
    "yAxis": "root.stats[].unsubscribed.total",
    "yAxisOperation": "sum",
    "dateField": "root.stats[].time",
    "datasetColor": chartColors.lime.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Unsubscribed",
    "pointRadius": null,
    "formula": null,
    "DataRequests": [{
      "headers": {},
      "body": "null",
      "conditions": null,
      "method": "GET",
      "route": `/${domain}/stats/total?event=unsubscribed`,
      "useGlobalHeaders": true,
      "query": null,
      "pagination": true,
      "items": "items",
      "itemsLimit": 1000,
      "offset": "skip",
      "paginationField": "paging.next",
      "template": "url"
    }],
  }, {
    "td_id": 7,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root.stats[].time": "date",
      "root.stats[].complained": "object",
      "root.stats[].complained.total": "number"
    },
    "excludedFields": null,
    "query": null,
    "xAxis": "root.stats[].time",
    "xAxisOperation": null,
    "yAxis": "root.stats[].complained.total",
    "yAxisOperation": "sum",
    "dateField": "root.stats[].time",
    "datasetColor": chartColors.deep_fuchsia.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Complained",
    "pointRadius": null,
    "formula": null,
    "DataRequests": [{
      "headers": {},
      "body": "null",
      "conditions": null,
      "method": "GET",
      "route": `/${domain}/stats/total?event=complained`,
      "useGlobalHeaders": true,
      "query": null,
      "pagination": true,
      "items": "items",
      "itemsLimit": 100,
      "offset": "offset",
      "paginationField": "paging.next",
      "template": "url"
    }],
  }, {
    "td_id": 8,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root.items[].address": "string",
      "root.items[].tags": "array",
      "root.items[].created_at": "date"
    },
    "excludedFields": null,
    "query": null,
    "xAxis": "root.items[].created_at",
    "xAxisOperation": null,
    "yAxis": "root.items[].address",
    "yAxisOperation": "count",
    "dateField": "root.items[].created_at",
    "datasetColor": chartColors.blue.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Unsubscribes",
    "pointRadius": null,
    "formula": null,
    "DataRequests": [{
      "headers": {},
      "body": "null",
      "conditions": null,
      "method": "GET",
      "route": `/${domain}/unsubscribes`,
      "useGlobalHeaders": true,
      "query": null,
      "pagination": true,
      "items": "items",
      "itemsLimit": 1000,
      "offset": "offset",
      "paginationField": "paging.next",
      "template": "url"
    }],
  }, {
    "td_id": 9,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root.items[].address": "string",
      "root.items[].code": "string",
      "root.items[].error": "string",
      "root.items[].created_at": "date",
      "root.items[].MessageHash": "string"
    },
    "excludedFields": null,
    "query": null,
    "xAxis": "root.items[].created_at",
    "xAxisOperation": null,
    "yAxis": "root.items[].address",
    "yAxisOperation": "count",
    "dateField": "root.items[].created_at",
    "datasetColor": chartColors.amber.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Bounces",
    "pointRadius": null,
    "formula": null,
    "DataRequests": [{
      "headers": {},
      "body": "null",
      "conditions": null,
      "method": "GET",
      "route": `/${domain}/bounces`,
      "useGlobalHeaders": true,
      "query": null,
      "pagination": true,
      "items": "items",
      "itemsLimit": 1000,
      "offset": "offset",
      "paginationField": "paging.next",
      "template": "url"
    }],
  }, {
    "td_id": 10,
    "fillColor": "rgba(0,0,0,0)",
    "patterns": [],
    "conditions": null,
    "fieldsSchema": {
      "root.items[].address": "string",
      "root.items[].created_at": "date"
    },
    "excludedFields": null,
    "query": null,
    "xAxis": "root.items[].created_at",
    "xAxisOperation": null,
    "yAxis": "root.items[].address",
    "yAxisOperation": "count",
    "dateField": "root.items[].created_at",
    "datasetColor": chartColors.teal.rgb,
    "fill": false,
    "multiFill": false,
    "dateFormat": null,
    "legend": "Complaints",
    "pointRadius": null,
    "formula": null,
    "DataRequests": [{
      "headers": {},
      "body": "null",
      "conditions": null,
      "method": "GET",
      "route": `/${domain}/complaints`,
      "useGlobalHeaders": true,
      "query": null,
      "pagination": true,
      "items": "items",
      "itemsLimit": 1000,
      "offset": "offset",
      "paginationField": "paging.next",
      "template": "url"
    }],
  }],
  "Charts": [
    {
      "tid": 0,
      "name": "Sending stats",
      "type": "kpi",
      "subType": "AddTimeseries",
      "public": false,
      "chartSize": 1,
      "displayLegend": false,
      "pointRadius": null,
      "startDate": "2021-05-08T15:59:59.000Z",
      "endDate": "2021-05-15T15:59:59.000Z",
      "includeZeros": true,
      "currentEndDate": true,
      "timeInterval": "day",
      "autoUpdate": null,
      "draft": false,
      "layout": {
        "xs": [0, 0, 6, 1], "sm": [6, 0, 2, 2], "md": [7, 0, 3, 2], "lg": [8, 0, 4, 2]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 1,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.stats[].time": "date",
            "root.stats[].delivered": "object",
            "root.stats[].delivered.smtp": "number",
            "root.stats[].delivered.http": "number",
            "root.stats[].delivered.optimized": "number",
            "root.stats[].delivered.total": "number"
          },
          "excludedFields": null,
          "datasetColor": chartColors.blue.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Delivered",
          "pointRadius": null,
          "formula": null,
        },
        {
          "td_id": 2,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.stats[].time": "date",
            "root.stats[].failed": "object",
            "root.stats[].failed.temporary": "object",
            "root.stats[].failed.temporary.espblock": "number",
            "root.stats[].failed.temporary.total": "number",
            "root.stats[].failed.permanent": "object",
            "root.stats[].failed.permanent.suppress-bounce": "number",
            "root.stats[].failed.permanent.suppress-unsubscribe": "number",
            "root.stats[].failed.permanent.suppress-complaint": "number",
            "root.stats[].failed.permanent.bounce": "number",
            "root.stats[].failed.permanent.delayed-bounce": "number",
            "root.stats[].failed.permanent.webhook": "number",
            "root.stats[].failed.permanent.optimized": "number",
            "root.stats[].failed.permanent.total": "number"
          },
          "excludedFields": null,
          "datasetColor": chartColors.amber.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Failed",
          "pointRadius": null,
          "formula": null,
        },
        {
          "td_id": 3,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.items[].address": "string",
            "root.items[].code": "string",
            "root.items[].error": "string",
            "root.items[].created_at": "date",
            "root.items[].MessageHash": "string"
          },
          "excludedFields": null,
          "datasetColor": chartColors.teal.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Bounced",
          "pointRadius": null,
          "formula": null,
        }
      ]
    },
    {
      "tid": 1,
      "name": "All events timeline",
      "type": "line",
      "subType": "timeseries",
      "public": false,
      "chartSize": 3,
      "displayLegend": true,
      "pointRadius": null,
      "startDate": "2021-05-11T15:59:59.000Z",
      "endDate": "2021-05-18T15:59:59.000Z",
      "includeZeros": true,
      "currentEndDate": true,
      "timeInterval": "day",
      "autoUpdate": null,
      "draft": false,
      "mode": "chart",
      "layout": {
        "xs": [0, 3, 6, 2], "sm": [0, 2, 8, 2], "md": [0, 2, 10, 2], "lg": [0, 2, 8, 2]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 1,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.stats[].time": "date",
            "root.stats[].delivered": "object",
            "root.stats[].delivered.smtp": "number",
            "root.stats[].delivered.http": "number",
            "root.stats[].delivered.optimized": "number",
            "root.stats[].delivered.total": "number"
          },
          "excludedFields": null,
          "query": null,
          "xAxis": "root.stats[].time",
          "xAxisOperation": null,
          "yAxis": "root.stats[].delivered.total",
          "yAxisOperation": "sum",
          "dateField": "root.stats[].time",
          "datasetColor": chartColors.blue.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Delivered",
          "pointRadius": null,
          "formula": null,
        },
        {
          "td_id": 2,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.stats[].time": "date",
            "root.stats[].failed": "object",
            "root.stats[].failed.temporary": "object",
            "root.stats[].failed.temporary.espblock": "number",
            "root.stats[].failed.temporary.total": "number",
            "root.stats[].failed.permanent": "object",
            "root.stats[].failed.permanent.suppress-bounce": "number",
            "root.stats[].failed.permanent.suppress-unsubscribe": "number",
            "root.stats[].failed.permanent.suppress-complaint": "number",
            "root.stats[].failed.permanent.bounce": "number",
            "root.stats[].failed.permanent.delayed-bounce": "number",
            "root.stats[].failed.permanent.webhook": "number",
            "root.stats[].failed.permanent.optimized": "number",
            "root.stats[].failed.permanent.total": "number"
          },
          "excludedFields": null,
          "datasetColor": chartColors.amber.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Failed",
          "pointRadius": null,
          "formula": null,
        },
        {
          "td_id": 4,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.stats[].time": "date",
            "root.stats[].opened": "object",
            "root.stats[].opened.total": "number"
          },
          "excludedFields": null,
          "dateField": "root.stats[].time",
          "datasetColor": chartColors.teal.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Opened",
          "pointRadius": null,
          "formula": null,
        },
        {
          "td_id": 5,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.stats[].time": "date",
            "root.stats[].clicked": "object",
            "root.stats[].clicked.total": "number"
          },
          "excludedFields": null,
          "datasetColor": chartColors.fuchsia.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Clicked",
          "pointRadius": null,
          "formula": null,
        },
        {
          "td_id": 6,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.stats[].time": "date",
            "root.stats[].unsubscribed": "object",
            "root.stats[].unsubscribed.total": "number"
          },
          "excludedFields": null,
          "datasetColor": chartColors.lime.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Unsubscribed",
          "pointRadius": null,
          "formula": null,
        },
        {
          "td_id": 7,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.stats[].time": "date",
            "root.stats[].complained": "object",
            "root.stats[].complained.total": "number"
          },
          "excludedFields": null,
          "datasetColor": chartColors.deep_fuchsia.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Complained",
          "pointRadius": null,
          "formula": null,
        }
      ]
    },
    {
      "tid": 2,
      "name": "Opens & clicks timeline",
      "type": "line",
      "subType": "lcTimeseries",
      "public": false,
      "chartSize": 2,
      "displayLegend": true,
      "pointRadius": null,
      "startDate": "2021-05-08T15:59:59.000Z",
      "endDate": "2021-05-15T15:59:59.000Z",
      "includeZeros": true,
      "currentEndDate": true,
      "timeInterval": "day",
      "autoUpdate": null,
      "draft": false,
      "mode": "kpichart",
      "showGrowth": true,
      "layout": {
        "xs": [0, 1, 6, 2], "sm": [0, 0, 6, 2], "md": [0, 0, 7, 2], "lg": [0, 0, 8, 2]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 1,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.stats[].time": "date",
            "root.stats[].delivered": "object",
            "root.stats[].delivered.smtp": "number",
            "root.stats[].delivered.http": "number",
            "root.stats[].delivered.optimized": "number",
            "root.stats[].delivered.total": "number"
          },
          "excludedFields": null,
          "datasetColor": chartColors.blue.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Delivered",
          "pointRadius": null,
          "formula": null,
        },
        {
          "td_id": 4,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.stats[].time": "date",
            "root.stats[].opened": "object",
            "root.stats[].opened.total": "number"
          },
          "excludedFields": null,
          "datasetColor": chartColors.amber.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Opens",
          "pointRadius": null,
          "formula": null,
        },
        {
          "td_id": 5,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.stats[].time": "date",
            "root.stats[].clicked": "object",
            "root.stats[].clicked.total": "number"
          },
          "excludedFields": null,
          "datasetColor": chartColors.teal.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Clicked",
          "pointRadius": null,
          "formula": null,
        }
      ]
    },
    {
      "tid": 3,
      "name": "Suppressions details",
      "type": "table",
      "subType": "timeseries",
      "public": false,
      "chartSize": 2,
      "displayLegend": false,
      "pointRadius": null,
      "startDate": null,
      "endDate": null,
      "includeZeros": true,
      "currentEndDate": false,
      "timeInterval": "day",
      "autoUpdate": null,
      "draft": false,
      "mode": "chart",
      "layout": {
        "xs": [0, 5, 6, 3], "sm": [0, 4, 8, 3], "md": [0, 4, 10, 3], "lg": [0, 4, 12, 2]
      },
      "ChartDatasetConfigs": [
        {
          "td_id": 8,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.items[].address": "string",
            "root.items[].tags": "array",
            "root.items[].created_at": "date"
          },
          "excludedFields": null,
          "datasetColor": chartColors.blue.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Unsubscribes",
          "pointRadius": null,
          "formula": null,
        },
        {
          "td_id": 9,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.items[].address": "string",
            "root.items[].code": "string",
            "root.items[].error": "string",
            "root.items[].created_at": "date",
            "root.items[].MessageHash": "string"
          },
          "excludedFields": null,
          "datasetColor": chartColors.amber.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Bounces",
          "pointRadius": null,
          "formula": null,
        },
        {
          "td_id": 10,
          "fillColor": "rgba(0,0,0,0)",
          "patterns": [],
          "conditions": null,
          "fieldsSchema": {
            "root.items[].address": "string",
            "root.items[].created_at": "date"
          },
          "excludedFields": null,
          "datasetColor": chartColors.teal.rgb,
          "fill": false,
          "multiFill": false,
          "dateFormat": null,
          "legend": "Complaints",
          "pointRadius": null,
          "formula": null,
        }
      ]
    }
  ],
});

module.exports.template = template;

module.exports.build = async (teamId, projectId, {
  apiKey, domain, domainLocation, charts, connection_id,
}) => {
  if ((!apiKey || !domain) && !connection_id) return Promise.reject("Missing required authentication arguments");

  const apiRoot = domainLocation === "eu" ? "https://api.eu.mailgun.net" : "https://api.mailgun.net/";

  let checkErrored = false;
  if (!connection_id) {
    const checkOpt = {
      url: `${apiRoot}/v3/${domain}/stats/total?event=delivered&limit=10`,
      method: "GET",
      auth: {
        user: "api",
        pass: apiKey,
      },
      headers: {
        accept: "application/json",
      },
      json: true,
    };

    try {
      const checkAuth = await request(checkOpt); // eslint-disable-line
    } catch (e) {
      checkErrored = true;
    }
  }

  if (!connection_id && checkErrored) {
    return Promise.reject(new Error("Request cannot be authenticated"));
  }

  return builder(
    teamId, projectId, apiKey, domain, apiRoot, template, charts, connection_id
  )
    .catch((err) => {
      if (err && err.message) {
        return Promise.reject(err.message);
      }
      return Promise.reject(err);
    });
};
