module.exports = [
  'strapi::logger',
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      // No custom CSP directives — use the defaults
      contentSecurityPolicy: {
        useDefaults: true,
      },
    },
  },
  {
    name: 'strapi::cors',
    config: {
      // Default CORS settings (allows all origins, methods, credentials)
      origin: ['*'],
      headers: ['*'],
      credentials: true,
      methods: ['GET','POST','PUT','PATCH','DELETE','HEAD','OPTIONS'],
    },
  },
  'strapi::poweredBy',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
