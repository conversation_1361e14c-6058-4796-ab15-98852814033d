const axios = require('axios');

const FRONTEND_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:1337/api';

async function testFrontendDashboard() {
  console.log('🧪 Testing Frontend Dashboard Integration...\n');

  try {
    // Step 1: Test frontend pages are loading
    console.log('1. Testing frontend pages...');
    
    const homeResponse = await axios.get(`${FRONTEND_URL}/`);
    console.log('✅ Home page loading (200)');
    
    const loginResponse = await axios.get(`${FRONTEND_URL}/auth/login`);
    console.log('✅ Login page loading (200)');
    
    const dashboardResponse = await axios.get(`${FRONTEND_URL}/dashboard`);
    console.log('✅ Dashboard page loading (200)');
    
    // Step 2: Test API authentication flow
    console.log('\n2. Testing authentication flow...');
    
    const loginApiResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    console.log('✅ API login successful');
    const token = loginApiResponse.data.jwt;
    const user = loginApiResponse.data.user;
    
    // Step 3: Test dashboard API calls
    console.log('\n3. Testing dashboard API calls...');
    
    const myPropertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log(`✅ My Properties: ${myPropertiesResponse.data.data?.length || 0} properties`);
    
    const userProfileResponse = await axios.get(`${API_URL}/users/me`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log(`✅ User Profile: ${userProfileResponse.data.username}`);
    
    const featuredResponse = await axios.get(`${API_URL}/properties/featured`);
    console.log(`✅ Featured Properties: ${featuredResponse.data.data?.length || 0} properties`);
    
    // Step 4: Test property creation (if user has no properties)
    if (myPropertiesResponse.data.data?.length === 0) {
      console.log('\n4. Creating test property for dashboard...');
      
      try {
        const newProperty = await axios.post(`${API_URL}/properties`, {
          data: {
            title: 'Test Dashboard Property',
            description: 'A test property for dashboard testing',
            price: 250000,
            currency: 'USD',
            propertyType: 'apartment',
            offer: 'sale',
            bedrooms: 2,
            bathrooms: 1,
            area: 85,
            areaUnit: 'sqm',
            address: '123 Test Street',
            city: 'Test City',
            country: 'Test Country',
            coordinates: {
              lat: 40.7128,
              lng: -74.0060
            }
          }
        }, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        
        console.log('✅ Test property created successfully');
        console.log(`   Property ID: ${newProperty.data.data.id}`);
        console.log(`   Title: ${newProperty.data.data.title}`);
        
        // Verify the property appears in my-properties
        const updatedPropertiesResponse = await axios.get(`${API_URL}/properties/my-properties`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`✅ Updated properties count: ${updatedPropertiesResponse.data.data?.length || 0}`);
        
      } catch (createError) {
        console.log('⚠️  Could not create test property:', createError.response?.data?.error?.message || createError.message);
      }
    }
    
    // Step 5: Summary and next steps
    console.log('\n🎉 Frontend Dashboard Test Summary:');
    console.log('   ✅ Frontend pages loading correctly');
    console.log('   ✅ API authentication working');
    console.log('   ✅ Dashboard endpoints responding');
    console.log('   ✅ User data accessible');
    
    console.log('\n📝 Manual Testing Instructions:');
    console.log('   1. Open: http://localhost:3000/auth/login');
    console.log('   2. Login with:');
    console.log('      Email: <EMAIL>');
    console.log('      Password: TestPassword123!');
    console.log('   3. After login, navigate to: http://localhost:3000/dashboard');
    console.log('   4. Verify you can see:');
    console.log('      - Dashboard statistics');
    console.log('      - User properties (if any)');
    console.log('      - Navigation menu');
    console.log('      - User profile info');
    
    console.log('\n🔧 If you see any issues:');
    console.log('   - Check browser console for JavaScript errors');
    console.log('   - Verify network requests in browser dev tools');
    console.log('   - Check that JWT token is stored in localStorage');
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Data:`, error.response.data);
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Connection refused. Make sure:');
      console.log('   - Frontend is running on http://localhost:3000');
      console.log('   - Backend is running on http://localhost:1337');
    }
  }
}

testFrontendDashboard().catch(console.error);
