'use client';

import React from 'react';
import { ArrowUpDown, TrendingUp, TrendingDown, Calendar, Star, Eye } from 'lucide-react';

interface SortFilterProps {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  className?: string;
}

const SORT_OPTIONS = [
  {
    value: 'createdAt',
    label: 'Date Added',
    icon: Calendar,
    description: 'Recently added properties first'
  },
  {
    value: 'price',
    label: 'Price',
    icon: TrendingUp,
    description: 'Sort by property price'
  },
  {
    value: 'area',
    label: 'Area Size',
    icon: ArrowUpDown,
    description: 'Sort by property area'
  },
  {
    value: 'views',
    label: 'Popularity',
    icon: Eye,
    description: 'Most viewed properties first'
  },
  {
    value: 'relevance',
    label: 'Relevance',
    icon: Star,
    description: 'Best match for your search'
  },
];

export const SortFilter: React.FC<SortFilterProps> = ({
  sortBy,
  sortOrder,
  onSortChange,
  className = '',
}) => {
  const currentSort = SORT_OPTIONS.find(option => option.value === sortBy) || SORT_OPTIONS[0];

  const handleSortChange = (newSortBy: string) => {
    // If clicking the same sort option, toggle order
    if (newSortBy === sortBy) {
      onSortChange(sortBy, sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Default order for different sort types
      const defaultOrder = newSortBy === 'price' || newSortBy === 'area' ? 'asc' : 'desc';
      onSortChange(newSortBy, defaultOrder);
    }
  };

  const getSortLabel = (option: typeof SORT_OPTIONS[0]) => {
    if (option.value === sortBy) {
      const orderText = sortOrder === 'asc' ? 'Low to High' : 'High to Low';
      if (option.value === 'createdAt') {
        return sortOrder === 'asc' ? 'Oldest First' : 'Newest First';
      }
      if (option.value === 'views' || option.value === 'relevance') {
        return sortOrder === 'asc' ? 'Least Popular' : 'Most Popular';
      }
      return orderText;
    }
    return option.label;
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-gray-700">
          Sort Properties
        </label>
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <span>Current:</span>
          <span className="font-medium text-gray-700">
            {getSortLabel(currentSort)}
          </span>
          {sortBy && (
            sortOrder === 'asc' ? (
              <TrendingDown className="w-3 h-3 text-red-500" />
              
            ) : (
              <TrendingUp className="w-3 h-3 text-green-500" />
            )
          )}
        </div>
      </div>

      {/* Sort Options Grid */}
      <div className="grid grid-cols-2 gap-2">
        {SORT_OPTIONS.map((option) => {
          const Icon = option.icon;
          const isActive = option.value === sortBy;
          
          return (
            <button
              key={option.value}
              type="button"
              onClick={() => handleSortChange(option.value)}
              className={`p-3 border rounded-lg text-left transition-all duration-200 group ${
                isActive
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-2 mb-1">
                <Icon className={`w-4 h-4 ${isActive ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'}`} />
                <span className={`text-sm font-medium ${isActive ? 'text-blue-700' : 'text-gray-900'}`}>
                  {option.label}
                </span>
                {isActive && (
                  <div className="ml-auto">
                    {sortOrder === 'asc' ? (
                      <TrendingDown className="w-3 h-3 text-blue-600" />
                    ) : (
                      <TrendingUp className="w-3 h-3 text-blue-600" />
                    )}
                  </div>
                )}
              </div>
              <div className={`text-xs ${isActive ? 'text-blue-600' : 'text-gray-500'}`}>
                {isActive ? getSortLabel(option) : option.description}
              </div>
            </button>
          );
        })}
      </div>

      {/* Quick Sort Buttons */}
      <div className="flex flex-wrap gap-2">
        <button
          type="button"
          onClick={() => onSortChange('price', 'asc')}
          className={`px-3 py-1 text-xs rounded-full border transition-colors ${
            sortBy === 'price' && sortOrder === 'asc'
              ? 'border-green-500 bg-green-50 text-green-700'
              : 'border-gray-300 text-gray-600 hover:border-gray-400'
          }`}
        >
          Price: Low to High
        </button>
        
        <button
          type="button"
          onClick={() => onSortChange('price', 'desc')}
          className={`px-3 py-1 text-xs rounded-full border transition-colors ${
            sortBy === 'price' && sortOrder === 'desc'
              ? 'border-red-500 bg-red-50 text-red-700'
              : 'border-gray-300 text-gray-600 hover:border-gray-400'
          }`}
        >
          Price: High to Low
        </button>
        
        <button
          type="button"
          onClick={() => onSortChange('createdAt', 'desc')}
          className={`px-3 py-1 text-xs rounded-full border transition-colors ${
            sortBy === 'createdAt' && sortOrder === 'desc'
              ? 'border-blue-500 bg-blue-50 text-blue-700'
              : 'border-gray-300 text-gray-600 hover:border-gray-400'
          }`}
        >
          Newest First
        </button>
        
        <button
          type="button"
          onClick={() => onSortChange('views', 'desc')}
          className={`px-3 py-1 text-xs rounded-full border transition-colors ${
            sortBy === 'views' && sortOrder === 'desc'
              ? 'border-purple-500 bg-purple-50 text-purple-700'
              : 'border-gray-300 text-gray-600 hover:border-gray-400'
          }`}
        >
          Most Popular
        </button>
      </div>

      {/* Sort Summary */}
      {sortBy && (
        <div className="p-2 bg-gray-50 rounded-md">
          <div className="flex items-center space-x-2 text-sm text-gray-700">
            <ArrowUpDown className="w-4 h-4" />
            <span>
              Sorting by <strong>{currentSort.label}</strong> 
              ({sortOrder === 'asc' ? 'ascending' : 'descending'})
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
