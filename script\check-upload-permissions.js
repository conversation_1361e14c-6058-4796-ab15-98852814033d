const axios = require('axios');

const API_URL = 'http://localhost:1337/api';
const ADMIN_URL = 'http://localhost:1337/admin';

async function checkUploadPermissions() {
  console.log('🔍 Checking Upload Permissions Configuration...\n');

  try {
    // First, let's check what permissions are available
    console.log('1. Checking available permissions...');
    
    // Login as user first
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    const token = loginResponse.data.jwt;
    console.log('✅ User authentication successful');
    
    // Check current user permissions
    try {
      const userResponse = await axios.get(`${API_URL}/users/me`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log(`✅ Current user: ${userResponse.data.username}`);
      console.log(`   Role: ${userResponse.data.role?.name || 'Unknown'}`);
      console.log(`   Role Type: ${userResponse.data.role?.type || 'Unknown'}`);
    } catch (userError) {
      console.log('⚠️  Could not get user details');
    }
    
    // Test different upload endpoints to understand the structure
    console.log('\n2. Testing upload endpoints...');
    
    const uploadEndpoints = [
      '/upload',
      '/upload/files',
      '/upload/search',
      '/upload/settings'
    ];
    
    for (const endpoint of uploadEndpoints) {
      try {
        const response = await axios.get(`${API_URL}${endpoint}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`✅ ${endpoint}: Available (${response.status})`);
      } catch (error) {
        const status = error.response?.status;
        if (status === 403) {
          console.log(`🔒 ${endpoint}: Forbidden (needs permission)`);
        } else if (status === 404) {
          console.log(`❌ ${endpoint}: Not found`);
        } else if (status === 405) {
          console.log(`✅ ${endpoint}: Available (method not allowed for GET)`);
        } else {
          console.log(`⚠️  ${endpoint}: ${status || 'Unknown error'}`);
        }
      }
    }
    
    console.log('\n3. Checking Strapi admin API for permissions...');
    
    // Try to get permissions info (this might require admin token)
    try {
      const permissionsResponse = await axios.get(`${API_URL}/users-permissions/permissions`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Permissions endpoint accessible');
      console.log('   Available permissions:', Object.keys(permissionsResponse.data));
    } catch (permError) {
      console.log(`⚠️  Permissions endpoint: ${permError.response?.status || 'Error'}`);
    }
    
    // Check roles
    try {
      const rolesResponse = await axios.get(`${API_URL}/users-permissions/roles`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Roles endpoint accessible');
      if (rolesResponse.data?.roles) {
        rolesResponse.data.roles.forEach(role => {
          console.log(`   Role: ${role.name} (${role.type})`);
        });
      }
    } catch (roleError) {
      console.log(`⚠️  Roles endpoint: ${roleError.response?.status || 'Error'}`);
    }
    
    console.log('\n📋 UPLOAD PERMISSION TROUBLESHOOTING GUIDE:');
    console.log('');
    console.log('🔧 METHOD 1 - Through Strapi Admin Panel:');
    console.log('   1. Go to: http://localhost:1337/admin');
    console.log('   2. Login with your admin credentials');
    console.log('   3. Navigate to: Settings > Users & Permissions plugin > Roles');
    console.log('   4. Click on "Authenticated" role');
    console.log('   5. Look for these sections:');
    console.log('      - "Upload" or "Upload-file"');
    console.log('      - "Application" section with upload permissions');
    console.log('      - "Plugins" section with upload permissions');
    console.log('   6. Enable the following permissions:');
    console.log('      ✅ upload (if available)');
    console.log('      ✅ upload.upload (if available)');
    console.log('      ✅ upload.destroy (if available)');
    console.log('   7. Save changes');
    console.log('');
    console.log('🔧 METHOD 2 - Check Different Sections:');
    console.log('   In the Authenticated role permissions, look for:');
    console.log('   - "Application" section');
    console.log('   - "Plugin" section');
    console.log('   - "Upload" under plugins');
    console.log('   - "File" or "Media" sections');
    console.log('');
    console.log('🔧 METHOD 3 - Enable Public Upload (for testing):');
    console.log('   1. Go to "Public" role instead of "Authenticated"');
    console.log('   2. Enable upload permissions there');
    console.log('   3. Test upload without authentication');
    console.log('   4. Then move permissions back to "Authenticated"');
    console.log('');
    console.log('🔧 METHOD 4 - Check Plugin Installation:');
    console.log('   1. In admin panel, go to: Marketplace');
    console.log('   2. Check if "Upload" plugin is installed');
    console.log('   3. If not installed, install it');
    console.log('   4. Restart Strapi');
    console.log('');
    console.log('💡 WHAT TO LOOK FOR:');
    console.log('   - Upload permissions might be under "Plugins" section');
    console.log('   - Look for checkboxes next to "upload", "find", "findOne", "destroy"');
    console.log('   - The section might be called "Upload-file" instead of "Upload"');
    console.log('   - Some versions show it under "Application" > "Upload"');
    
  } catch (error) {
    console.log('❌ Error checking permissions:', error.message);
  }
}

checkUploadPermissions().catch(console.error);
