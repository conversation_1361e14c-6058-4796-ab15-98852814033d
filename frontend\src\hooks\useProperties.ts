'use client';

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { propertiesAPI } from '@/lib/api';

// Query Keys - Centralized for consistency
export const PROPERTY_KEYS = {
  all: ['properties'] as const,
  lists: () => [...PROPERTY_KEYS.all, 'list'] as const,
  list: (filters: any) => [...PROPERTY_KEYS.lists(), filters] as const,
  details: () => [...PROPERTY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...PROPERTY_KEYS.details(), id] as const,
  myProperties: () => [...PROPERTY_KEYS.all, 'my-properties'] as const,
  featured: () => [...PROPERTY_KEYS.all, 'featured'] as const,
  cities: () => [...PROPERTY_KEYS.all, 'cities'] as const,
  neighborhoods: (city?: string) => [...PROPERTY_KEYS.all, 'neighborhoods', city] as const,
};

// Hook for fetching all properties with filters
export const useProperties = (filters?: any) => {
  return useQuery({
    queryKey: PROPERTY_KEYS.list(filters),
    queryFn: () => propertiesAPI.search(filters || {}),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  });
};

// Hook for infinite scroll properties
export const useInfiniteProperties = (filters?: any) => {
  return useInfiniteQuery({
    queryKey: PROPERTY_KEYS.list(filters),
    queryFn: ({ pageParam = 1 }) => 
      propertiesAPI.search({ ...filters, page: pageParam, pageSize: 12 }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      const { page, pageCount } = lastPage.meta?.pagination || {};
      return page < pageCount ? page + 1 : undefined;
    },
    staleTime: 5 * 60 * 1000,
  });
};

// Hook for single property
export const useProperty = (id: string, enabled = true) => {
  return useQuery({
    queryKey: PROPERTY_KEYS.detail(id),
    queryFn: () => propertiesAPI.getById(id),
    enabled: !!id && enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes for individual properties
  });
};

// Hook for property edit data
export const usePropertyForEdit = (id: string, enabled = true) => {
  return useQuery({
    queryKey: [...PROPERTY_KEYS.detail(id), 'edit'],
    queryFn: () => propertiesAPI.getForEdit(id),
    enabled: !!id && enabled,
    staleTime: 2 * 60 * 1000, // 2 minutes for edit data
  });
};

// Hook for user's properties
export const useMyProperties = () => {
  return useQuery({
    queryKey: PROPERTY_KEYS.myProperties(),
    queryFn: propertiesAPI.getMyProperties,
    staleTime: 5 * 60 * 1000,
  });
};

// Hook for featured properties
export const useFeaturedProperties = () => {
  return useQuery({
    queryKey: PROPERTY_KEYS.featured(),
    queryFn: propertiesAPI.getFeatured,
    staleTime: 15 * 60 * 1000, // 15 minutes for featured
  });
};

// Hook for cities
export const useCities = () => {
  return useQuery({
    queryKey: PROPERTY_KEYS.cities(),
    queryFn: propertiesAPI.getCities,
    staleTime: 30 * 60 * 1000, // 30 minutes for cities
  });
};

// Hook for neighborhoods
export const useNeighborhoods = (city?: string) => {
  return useQuery({
    queryKey: PROPERTY_KEYS.neighborhoods(city),
    queryFn: () => propertiesAPI.getNeighborhoods(city),
    enabled: !!city,
    staleTime: 30 * 60 * 1000,
  });
};

// Mutation hooks
export const useCreateProperty = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: propertiesAPI.create,
    onSuccess: () => {
      // Invalidate and refetch properties
      queryClient.invalidateQueries({ queryKey: PROPERTY_KEYS.all });
    },
  });
};

export const useUpdateProperty = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      propertiesAPI.update(id, data),
    onSuccess: (data, variables) => {
      // Update specific property in cache
      queryClient.setQueryData(
        PROPERTY_KEYS.detail(variables.id),
        data
      );
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: PROPERTY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: PROPERTY_KEYS.myProperties() });
    },
  });
};

export const useDeleteProperty = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: propertiesAPI.delete,
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: PROPERTY_KEYS.detail(deletedId) });
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: PROPERTY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: PROPERTY_KEYS.myProperties() });
    },
  });
};

export const usePublishProperty = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: propertiesAPI.publish,
    onSuccess: (data, propertyId) => {
      // Update cache with published status
      queryClient.setQueryData(
        PROPERTY_KEYS.detail(propertyId),
        data
      );
      queryClient.invalidateQueries({ queryKey: PROPERTY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: PROPERTY_KEYS.myProperties() });
    },
  });
};

export const useUnpublishProperty = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: propertiesAPI.unpublish,
    onSuccess: (data, propertyId) => {
      queryClient.setQueryData(
        PROPERTY_KEYS.detail(propertyId),
        data
      );
      queryClient.invalidateQueries({ queryKey: PROPERTY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: PROPERTY_KEYS.myProperties() });
    },
  });
};
