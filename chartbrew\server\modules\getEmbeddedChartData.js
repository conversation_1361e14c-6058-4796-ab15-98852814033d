module.exports = (chart, team = {}) => ({
  id: chart.id,
  name: chart.name,
  type: chart.type,
  subType: chart.subType,
  chartDataUpdated: chart.chartDataUpdated,
  chartData: chart.chartData,
  ChartDatasetConfigs: chart.ChartDatasetConfigs,
  mode: chart.mode,
  chartSize: chart.chartSize,
  project_id: chart.project_id,
  showBranding: team?.showBranding || true,
  showGrowth: chart.showGrowth,
  timeInterval: chart.timeInterval,
  autoUpdate: chart.autoUpdate,
  xLabelTicks: chart.xLabelTicks,
  pointRadius: chart.pointRadius,
  currentEndDate: chart.currentEndDate,
  dataLabels: chart.dataLabels,
  displayLegend: chart.displayLegend,
  maxValue: chart.maxValue,
  minValue: chart.minValue,
  horizontal: chart.horizontal,
  stacked: chart.stacked,
  startDate: chart.startDate,
  endDate: chart.endDate,
  isLogarithmic: chart.isLogarithmic,
});
