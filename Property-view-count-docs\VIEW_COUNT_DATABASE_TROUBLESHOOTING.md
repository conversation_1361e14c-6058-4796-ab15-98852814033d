# View Count Database Persistence - Troubleshooting Guide

## 🚨 Critical Issue: Database Persistence Failure

### **Problem Statement**
View counts are being tracked in memory/cache but failing to persist to the database, resulting in:
- View counts appearing to work during session
- Counts resetting to 0 after server restart
- "Property not found" errors in batch processing logs

---

## 🔍 Diagnostic Steps

### **Step 1: Verify Database Schema**
```sql
-- Check if views field exists in properties table
.schema properties

-- Check current view count values
SELECT documentId, title, views FROM properties LIMIT 10;

-- Check for properties with non-zero view counts
SELECT COUNT(*) FROM properties WHERE views > 0;
```

### **Step 2: Test Property Lookup Methods**

#### **Method 1: Direct Entity Service (Current Failing Method)**
```bash
# Test in Strapi console or create test endpoint
curl -X POST "http://localhost:1337/api/test-property-lookup" \
  -H "Content-Type: application/json" \
  -d '{"propertyId": "bz3ap0vxrk74dvxdv9vwz1yc"}'
```

```typescript
// Test endpoint implementation
async testPropertyLookup(ctx) {
  const { propertyId } = ctx.request.body;
  
  try {
    // Method 1: Direct findOne (currently failing)
    const property1 = await strapi.entityService.findOne('api::property.property', propertyId, {
      fields: ['views', 'title', 'documentId']
    });
    console.log('Method 1 (findOne):', property1);
    
    // Method 2: findMany with documentId filter
    const properties2 = await strapi.entityService.findMany('api::property.property', {
      filters: { documentId: propertyId },
      fields: ['views', 'title', 'documentId']
    });
    console.log('Method 2 (findMany):', properties2);
    
    // Method 3: Database query
    const properties3 = await strapi.db.query('api::property.property').findMany({
      where: { documentId: propertyId },
      select: ['views', 'title', 'documentId']
    });
    console.log('Method 3 (db.query):', properties3);
    
    return { data: { method1: property1, method2: properties2, method3: properties3 } };
  } catch (error) {
    console.error('Property lookup test failed:', error);
    return ctx.badRequest('Lookup test failed');
  }
}
```

### **Step 3: Monitor Batch Processing**

#### **Add Debug Logging to ViewTracker**
```typescript
// In processBatch method, add detailed logging
console.log(`🔍 DEBUG: Processing batch of ${batch.length} view updates`);
console.log(`🔍 DEBUG: Property IDs in batch:`, Object.keys(viewCounts));

// Before property lookup
console.log(`🔍 DEBUG: Looking up property ${propertyId} using entityService.findOne`);

// After property lookup
console.log(`🔍 DEBUG: Property lookup result:`, existingProperty ? 'FOUND' : 'NOT FOUND');
if (existingProperty) {
  console.log(`🔍 DEBUG: Property details:`, {
    id: existingProperty.id,
    documentId: existingProperty.documentId,
    title: existingProperty.title,
    views: existingProperty.views
  });
}
```

### **Step 4: Test Database Update Methods**

```typescript
// Create test endpoint to try different update methods
async testViewCountUpdate(ctx) {
  const { propertyId, incrementBy = 1 } = ctx.request.body;
  const results = {};
  
  try {
    // Method 1: Entity Service Update (current method)
    try {
      const property = await strapi.entityService.findOne('api::property.property', propertyId);
      if (property) {
        await strapi.entityService.update('api::property.property', propertyId, {
          data: { views: (property.views || 0) + incrementBy }
        });
        results.entityServiceUpdate = 'SUCCESS';
      } else {
        results.entityServiceUpdate = 'PROPERTY_NOT_FOUND';
      }
    } catch (error) {
      results.entityServiceUpdate = `ERROR: ${error.message}`;
    }
    
    // Method 2: Database Query Update
    try {
      const updateResult = await strapi.db.query('api::property.property').update({
        where: { documentId: propertyId },
        data: { views: strapi.db.connection.raw('views + ?', [incrementBy]) }
      });
      results.dbQueryUpdate = updateResult ? 'SUCCESS' : 'NO_ROWS_AFFECTED';
    } catch (error) {
      results.dbQueryUpdate = `ERROR: ${error.message}`;
    }
    
    // Method 3: Find then Update by ID
    try {
      const properties = await strapi.entityService.findMany('api::property.property', {
        filters: { documentId: propertyId },
        fields: ['id', 'views']
      });
      
      if (properties.length > 0) {
        const property = properties[0];
        await strapi.entityService.update('api::property.property', property.id, {
          data: { views: (property.views || 0) + incrementBy }
        });
        results.findThenUpdate = 'SUCCESS';
      } else {
        results.findThenUpdate = 'PROPERTY_NOT_FOUND';
      }
    } catch (error) {
      results.findThenUpdate = `ERROR: ${error.message}`;
    }
    
    return { data: results };
  } catch (error) {
    console.error('Update test failed:', error);
    return ctx.badRequest('Update test failed');
  }
}
```

---

## 🛠️ Potential Root Causes

### **1. Strapi v5 DocumentId vs ID Mismatch**

**Issue**: Strapi v5 uses `documentId` for external references but internal operations may require numeric `id`.

**Evidence**:
```
Property bz3ap0vxrk74dvxdv9vwz1yc not found, skipping view count update
```

**Investigation**:
```typescript
// Check what type of ID is being passed to batch processor
console.log('Property ID type:', typeof propertyId, 'Value:', propertyId);
console.log('Is documentId format:', /^[a-z0-9]{25}$/.test(propertyId));
console.log('Is numeric ID:', /^\d+$/.test(propertyId));
```

### **2. Entity Service API Changes**

**Issue**: Strapi v5 may have changed how `entityService.findOne` handles documentId lookups.

**Investigation**:
```typescript
// Test different ID formats
const testIds = [
  'bz3ap0vxrk74dvxdv9vwz1yc', // documentId
  '1', '2', '3',                // numeric IDs
];

for (const testId of testIds) {
  try {
    const result = await strapi.entityService.findOne('api::property.property', testId);
    console.log(`ID ${testId}: ${result ? 'FOUND' : 'NOT FOUND'}`);
  } catch (error) {
    console.log(`ID ${testId}: ERROR - ${error.message}`);
  }
}
```

### **3. Database Connection Issues**

**Issue**: Database connection problems during batch processing.

**Investigation**:
```typescript
// Test database connectivity
try {
  const dbTest = await strapi.db.query('api::property.property').count();
  console.log('Database connection test - Total properties:', dbTest);
} catch (error) {
  console.error('Database connection failed:', error);
}
```

### **4. Transaction/Concurrency Issues**

**Issue**: Multiple concurrent updates causing conflicts.

**Investigation**:
```typescript
// Add transaction logging
console.log('Starting batch update transaction');
const startTime = Date.now();

// After batch completion
const endTime = Date.now();
console.log(`Batch update completed in ${endTime - startTime}ms`);
```

---

## 🔧 Immediate Fix Strategies

### **Strategy 1: Use findMany + Update by ID**

```typescript
// Replace current batch processing logic
const updatePromises = Object.entries(viewCounts).map(async ([propertyId, incrementBy]) => {
  try {
    // Step 1: Find property using findMany with documentId filter
    const properties = await strapi.entityService.findMany('api::property.property', {
      filters: { documentId: propertyId },
      fields: ['id', 'views', 'documentId']
    });
    
    if (properties.length === 0) {
      console.warn(`✗ Property ${propertyId} not found using documentId filter`);
      return { success: false, propertyId, error: 'Property not found' };
    }
    
    const property = properties[0];
    const oldViews = property.views || 0;
    const newViews = oldViews + incrementBy;
    
    // Step 2: Update using numeric ID
    await strapi.entityService.update('api::property.property', property.id, {
      data: { views: newViews }
    });
    
    console.log(`✓ Updated view count for property ${propertyId}: ${oldViews} → ${newViews} (+${incrementBy})`);
    return { success: true, propertyId, incrementBy, oldViews, newViews };
    
  } catch (error) {
    console.error(`✗ Failed to update view count for property ${propertyId}:`, error);
    return { success: false, propertyId, error: error.message };
  }
});
```

### **Strategy 2: Use Database Query Builder**

```typescript
// Alternative using direct database queries
const updatePromises = Object.entries(viewCounts).map(async ([propertyId, incrementBy]) => {
  try {
    const updateResult = await strapi.db.query('api::property.property').update({
      where: { documentId: propertyId },
      data: { views: strapi.db.connection.raw('views + ?', [incrementBy]) }
    });
    
    if (updateResult) {
      console.log(`✓ Updated view count for property ${propertyId}: +${incrementBy}`);
      return { success: true, propertyId, incrementBy };
    } else {
      console.warn(`✗ No rows updated for property ${propertyId}`);
      return { success: false, propertyId, error: 'No rows affected' };
    }
  } catch (error) {
    console.error(`✗ Database update failed for property ${propertyId}:`, error);
    return { success: false, propertyId, error: error.message };
  }
});
```

### **Strategy 3: Hybrid Approach with Fallbacks**

```typescript
async function updatePropertyViews(propertyId: string, incrementBy: number) {
  // Try Method 1: Direct entity service update
  try {
    const property = await strapi.entityService.findOne('api::property.property', propertyId, {
      fields: ['views']
    });
    
    if (property) {
      await strapi.entityService.update('api::property.property', propertyId, {
        data: { views: (property.views || 0) + incrementBy }
      });
      return { success: true, method: 'direct' };
    }
  } catch (error) {
    console.log('Method 1 failed, trying fallback...');
  }
  
  // Try Method 2: Find by documentId then update by ID
  try {
    const properties = await strapi.entityService.findMany('api::property.property', {
      filters: { documentId: propertyId },
      fields: ['id', 'views']
    });
    
    if (properties.length > 0) {
      const property = properties[0];
      await strapi.entityService.update('api::property.property', property.id, {
        data: { views: (property.views || 0) + incrementBy }
      });
      return { success: true, method: 'findMany' };
    }
  } catch (error) {
    console.log('Method 2 failed, trying database query...');
  }
  
  // Try Method 3: Direct database query
  try {
    const result = await strapi.db.query('api::property.property').update({
      where: { documentId: propertyId },
      data: { views: strapi.db.connection.raw('views + ?', [incrementBy]) }
    });
    
    if (result) {
      return { success: true, method: 'dbQuery' };
    }
  } catch (error) {
    console.error('All update methods failed:', error);
  }
  
  return { success: false, error: 'All update methods failed' };
}
```

---

## 📋 Testing Checklist

### **Before Implementing Fix**
- [ ] Backup current database
- [ ] Document current view count values
- [ ] Test property lookup methods
- [ ] Verify database connectivity
- [ ] Check Strapi logs for errors

### **After Implementing Fix**
- [ ] Test view count increments
- [ ] Verify database persistence
- [ ] Test server restart persistence
- [ ] Monitor batch processing logs
- [ ] Validate analytics accuracy

### **Regression Testing**
- [ ] Test with different property ID formats
- [ ] Test concurrent view tracking
- [ ] Test error handling scenarios
- [ ] Verify cache consistency
- [ ] Test anti-spam functionality

---

## 🚀 Implementation Priority

1. **Immediate**: Implement Strategy 1 (findMany + update by ID)
2. **Short-term**: Add comprehensive logging and monitoring
3. **Medium-term**: Implement Strategy 3 (hybrid with fallbacks)
4. **Long-term**: Performance optimization and caching improvements

---

*This troubleshooting guide should be used alongside the main documentation to resolve the database persistence issue.*
