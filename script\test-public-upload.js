const axios = require('axios');

const API_URL = 'http://localhost:1337/api';

async function testPublicUpload() {
  console.log('🧪 Testing Public Upload (without authentication)...\n');

  try {
    console.log('1. Testing upload without authentication...');
    
    const FormData = require('form-data');
    const form = new FormData();
    
    // Create a simple test file
    const textBuffer = Buffer.from('Test file for public upload', 'utf8');
    form.append('files', textBuffer, {
      filename: 'test-public.txt',
      contentType: 'text/plain'
    });
    
    try {
      const uploadResponse = await axios.post(`${API_URL}/upload`, form, {
        headers: {
          ...form.getHeaders()
          // No Authorization header for public test
        }
      });
      
      console.log('✅ Public upload working!');
      console.log(`   Uploaded file: ${uploadResponse.data[0]?.name}`);
      console.log(`   File URL: ${uploadResponse.data[0]?.url}`);
      console.log('');
      console.log('🎉 Upload permissions are enabled for Public role!');
      console.log('💡 You can now move these permissions to Authenticated role');
      
      // Clean up - try to delete the test file
      if (uploadResponse.data[0]?.id) {
        try {
          await axios.delete(`${API_URL}/upload/files/${uploadResponse.data[0].id}`);
          console.log('✅ Test file cleaned up');
        } catch (deleteError) {
          console.log('⚠️  Could not delete test file (this is okay)');
        }
      }
      
    } catch (uploadError) {
      if (uploadError.response?.status === 403) {
        console.log('❌ Public upload also forbidden (403)');
        console.log('💡 Upload permissions need to be enabled in admin panel');
        console.log('');
        console.log('🔧 NEXT STEPS:');
        console.log('   1. Go to Strapi admin: http://localhost:1337/admin');
        console.log('   2. Settings > Users & Permissions > Roles');
        console.log('   3. Click "Public" role');
        console.log('   4. Look for upload permissions in these sections:');
        console.log('      - Plugins > Upload');
        console.log('      - Application > Upload');
        console.log('      - Any section with "file" or "media"');
        console.log('   5. Enable upload permissions');
        console.log('   6. Save changes');
        console.log('   7. Test again');
      } else if (uploadError.response?.status === 404) {
        console.log('❌ Upload endpoint not found (404)');
        console.log('💡 Upload plugin might not be installed');
      } else {
        console.log(`❌ Upload failed: ${uploadError.response?.status}`);
        console.log('   Error:', uploadError.response?.data);
      }
    }
    
    console.log('\n2. Testing authenticated upload...');
    
    // Test with authentication
    const loginResponse = await axios.post(`${API_URL}/auth/local`, {
      identifier: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    const token = loginResponse.data.jwt;
    
    const authForm = new FormData();
    authForm.append('files', textBuffer, {
      filename: 'test-auth.txt',
      contentType: 'text/plain'
    });
    
    try {
      const authUploadResponse = await axios.post(`${API_URL}/upload`, authForm, {
        headers: {
          'Authorization': `Bearer ${token}`,
          ...authForm.getHeaders()
        }
      });
      
      console.log('✅ Authenticated upload working!');
      console.log(`   Uploaded file: ${authUploadResponse.data[0]?.name}`);
      
    } catch (authUploadError) {
      if (authUploadError.response?.status === 403) {
        console.log('❌ Authenticated upload forbidden (403)');
        console.log('💡 Need to enable upload for Authenticated role');
      } else {
        console.log(`❌ Authenticated upload failed: ${authUploadError.response?.status}`);
      }
    }
    
    console.log('\n📋 SUMMARY:');
    console.log('   If public upload works: Move permissions to Authenticated role');
    console.log('   If neither works: Enable upload permissions in admin panel');
    console.log('   Look for upload permissions under Plugins or Application sections');
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
  }
}

testPublicUpload().catch(console.error);
